/*

This CSS file will be included with your plugin, and
available in the app when your plugin is enabled.

If your plugin does not need CSS, delete this file.

*/

body {
	--cloze-underline-width: 1px;
	--cloze-underline-style: solid;
	/*noinspection CssUnresolvedCustomProperty*/
	--cloze-underline-color: var(--text-normal);
	--cloze-fixed-width: 30px;
	/*noinspection CssUnresolvedCustomProperty*/
	--cloze-hint-color: var(--text-muted);
	/*noinspection CssUnresolvedCustomProperty*/
	--cloze-hint-font-size: var(--font-ui-smaller);
}

.cloze-span,
.cloze:not(u){
	border-bottom: var(--cloze-underline-width) var(--cloze-underline-style) var(--cloze-underline-color) !important;
	position: relative;
}

/* special for <u> */
u.cloze.cloze-hide{
	border-bottom: var(--cloze-underline-width) var(--cloze-underline-style) var(--cloze-underline-color) !important;
	position: relative;
}

.cloze-span,
.cloze.cloze-hide{
	cursor: pointer;
}

.cloze-hide:not(.cloze-hint) {
	color: transparent !important;
	text-shadow: none !important;
}

/* embedded segments */
.cloze-hide .cloze-content,
.cloze-hide mark,
.cloze-hide u,
.cloze-hide strong,
.cloze-hide a {
	color: transparent !important;
	text-shadow: none !important;
	pointer-events: none;
}

/* fixed cloze width */
.cloze-fixed-width .cloze.cloze-hide:not(.cloze-hint) {
	white-space: nowrap;
	width: var(--cloze-fixed-width);
	display: inline-block;
	overflow: hidden;
}

/* cloze hint */
.cloze-hint {
	color: var(--cloze-hint-color);
	font-size: var(--cloze-hint-font-size);
	text-shadow: none !important;
	position: absolute;
	left: 0;
	top: 0;
	width: max-content;
	display: none;
	pointer-events: none;
}

.cloze-hide .cloze-hint {
	display: block;
}

.modal-hint-setting input{
	width: 100%;
}
