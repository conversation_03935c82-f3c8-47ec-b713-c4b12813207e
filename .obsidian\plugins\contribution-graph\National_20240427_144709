/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var JS=Object.create;var Fl=Object.defineProperty;var XS=Object.getOwnPropertyDescriptor;var eC=Object.getOwnPropertyNames;var tC=Object.getPrototypeOf,nC=Object.prototype.hasOwnProperty;var Rn=(t,e)=>()=>(e||t((e={exports:{}}).exports,e),e.exports),rC=(t,e)=>{for(var n in e)Fl(t,n,{get:e[n],enumerable:!0})},sy=(t,e,n,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let o of eC(e))!nC.call(t,o)&&o!==n&&Fl(t,o,{get:()=>e[o],enumerable:!(r=XS(e,o))||r.enumerable});return t};var B=(t,e,n)=>(n=t!=null?JS(tC(t)):{},sy(e||!t||!t.__esModule?Fl(n,"default",{value:t,enumerable:!0}):n,t)),oC=t=>sy(Fl({},"__esModule",{value:!0}),t);var ud=Rn(An=>{"use strict";Object.defineProperty(An,"__esModule",{value:!0});require("obsidian");var er=class extends Error{},Pf=class extends er{constructor(e){super(`Invalid DateTime: ${e.toMessage()}`)}},Bf=class extends er{constructor(e){super(`Invalid Interval: ${e.toMessage()}`)}},Vf=class extends er{constructor(e){super(`Invalid Duration: ${e.toMessage()}`)}},po=class extends er{},Zl=class extends er{constructor(e){super(`Invalid unit ${e}`)}},wt=class extends er{},Mn=class extends er{constructor(){super("Zone is an abstract class")}},z="numeric",dn="short",Nt="long",ql={year:z,month:z,day:z},Qy={year:z,month:dn,day:z},lC={year:z,month:dn,day:z,weekday:dn},Jy={year:z,month:Nt,day:z},Xy={year:z,month:Nt,day:z,weekday:Nt},eg={hour:z,minute:z},tg={hour:z,minute:z,second:z},ng={hour:z,minute:z,second:z,timeZoneName:dn},rg={hour:z,minute:z,second:z,timeZoneName:Nt},og={hour:z,minute:z,hourCycle:"h23"},ig={hour:z,minute:z,second:z,hourCycle:"h23"},sg={hour:z,minute:z,second:z,hourCycle:"h23",timeZoneName:dn},ag={hour:z,minute:z,second:z,hourCycle:"h23",timeZoneName:Nt},lg={year:z,month:z,day:z,hour:z,minute:z},ug={year:z,month:z,day:z,hour:z,minute:z,second:z},cg={year:z,month:dn,day:z,hour:z,minute:z},fg={year:z,month:dn,day:z,hour:z,minute:z,second:z},uC={year:z,month:dn,day:z,weekday:dn,hour:z,minute:z},dg={year:z,month:Nt,day:z,hour:z,minute:z,timeZoneName:dn},mg={year:z,month:Nt,day:z,hour:z,minute:z,second:z,timeZoneName:dn},pg={year:z,month:Nt,day:z,weekday:Nt,hour:z,minute:z,timeZoneName:Nt},hg={year:z,month:Nt,day:z,weekday:Nt,hour:z,minute:z,second:z,timeZoneName:Nt},go=class{get type(){throw new Mn}get name(){throw new Mn}get ianaName(){return this.name}get isUniversal(){throw new Mn}offsetName(e,n){throw new Mn}formatOffset(e,n){throw new Mn}offset(e){throw new Mn}equals(e){throw new Mn}get isValid(){throw new Mn}},bf=null,oi=class extends go{static get instance(){return bf===null&&(bf=new oi),bf}get type(){return"system"}get name(){return new Intl.DateTimeFormat().resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(e,{format:n,locale:r}){return gg(e,n,r)}formatOffset(e,n){return _s(this.offset(e),n)}offset(e){return-new Date(e).getTimezoneOffset()}equals(e){return e.type==="system"}get isValid(){return!0}},Gl={};function cC(t){return Gl[t]||(Gl[t]=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:t,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"})),Gl[t]}var fC={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};function dC(t,e){let n=t.format(e).replace(/\u200E/g,""),r=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(n),[,o,i,s,a,l,u,d]=r;return[s,o,i,a,l,u,d]}function mC(t,e){let n=t.formatToParts(e),r=[];for(let o=0;o<n.length;o++){let{type:i,value:s}=n[o],a=fC[i];i==="era"?r[a]=s:le(a)||(r[a]=parseInt(s,10))}return r}var Wl={},mn=class extends go{static create(e){return Wl[e]||(Wl[e]=new mn(e)),Wl[e]}static resetCache(){Wl={},Gl={}}static isValidSpecifier(e){return this.isValidZone(e)}static isValidZone(e){if(!e)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:e}).format(),!0}catch(n){return!1}}constructor(e){super(),this.zoneName=e,this.valid=mn.isValidZone(e)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(e,{format:n,locale:r}){return gg(e,n,r,this.name)}formatOffset(e,n){return _s(this.offset(e),n)}offset(e){let n=new Date(e);if(isNaN(n))return NaN;let r=cC(this.name),[o,i,s,a,l,u,d]=r.formatToParts?mC(r,n):dC(r,n);a==="BC"&&(o=-Math.abs(o)+1);let m=ru({year:o,month:i,day:s,hour:l===24?0:l,minute:u,second:d,millisecond:0}),c=+n,y=c%1e3;return c-=y>=0?y:1e3+y,(m-c)/(60*1e3)}equals(e){return e.type==="iana"&&e.name===this.name}get isValid(){return this.valid}},Ey={};function pC(t,e={}){let n=JSON.stringify([t,e]),r=Ey[n];return r||(r=new Intl.ListFormat(t,e),Ey[n]=r),r}var Wf={};function Hf(t,e={}){let n=JSON.stringify([t,e]),r=Wf[n];return r||(r=new Intl.DateTimeFormat(t,e),Wf[n]=r),r}var zf={};function hC(t,e={}){let n=JSON.stringify([t,e]),r=zf[n];return r||(r=new Intl.NumberFormat(t,e),zf[n]=r),r}var Uf={};function yC(t,e={}){let{base:n,...r}=e,o=JSON.stringify([t,r]),i=Uf[o];return i||(i=new Intl.RelativeTimeFormat(t,e),Uf[o]=i),i}var Ts=null;function gC(){return Ts||(Ts=new Intl.DateTimeFormat().resolvedOptions().locale,Ts)}function DC(t){let e=t.indexOf("-x-");e!==-1&&(t=t.substring(0,e));let n=t.indexOf("-u-");if(n===-1)return[t];{let r,o;try{r=Hf(t).resolvedOptions(),o=t}catch(a){let l=t.substring(0,n);r=Hf(l).resolvedOptions(),o=l}let{numberingSystem:i,calendar:s}=r;return[o,i,s]}}function vC(t,e,n){return(n||e)&&(t.includes("-u-")||(t+="-u"),n&&(t+=`-ca-${n}`),e&&(t+=`-nu-${e}`)),t}function EC(t){let e=[];for(let n=1;n<=12;n++){let r=q.utc(2009,n,1);e.push(t(r))}return e}function wC(t){let e=[];for(let n=1;n<=7;n++){let r=q.utc(2016,11,13+n);e.push(t(r))}return e}function Hl(t,e,n,r){let o=t.listingMode();return o==="error"?null:o==="en"?n(e):r(e)}function SC(t){return t.numberingSystem&&t.numberingSystem!=="latn"?!1:t.numberingSystem==="latn"||!t.locale||t.locale.startsWith("en")||new Intl.DateTimeFormat(t.intl).resolvedOptions().numberingSystem==="latn"}var jf=class{constructor(e,n,r){this.padTo=r.padTo||0,this.floor=r.floor||!1;let{padTo:o,floor:i,...s}=r;if(!n||Object.keys(s).length>0){let a={useGrouping:!1,...r};r.padTo>0&&(a.minimumIntegerDigits=r.padTo),this.inf=hC(e,a)}}format(e){if(this.inf){let n=this.floor?Math.floor(e):e;return this.inf.format(n)}else{let n=this.floor?Math.floor(e):nd(e,3);return Ve(n,this.padTo)}}},$f=class{constructor(e,n,r){this.opts=r,this.originalZone=void 0;let o;if(this.opts.timeZone)this.dt=e;else if(e.zone.type==="fixed"){let s=-1*(e.offset/60),a=s>=0?`Etc/GMT+${s}`:`Etc/GMT${s}`;e.offset!==0&&mn.create(a).valid?(o=a,this.dt=e):(o="UTC",this.dt=e.offset===0?e:e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone)}else e.zone.type==="system"?this.dt=e:e.zone.type==="iana"?(this.dt=e,o=e.zone.name):(o="UTC",this.dt=e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone);let i={...this.opts};i.timeZone=i.timeZone||o,this.dtf=Hf(n,i)}format(){return this.originalZone?this.formatToParts().map(({value:e})=>e).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){let e=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?e.map(n=>{if(n.type==="timeZoneName"){let r=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return{...n,value:r}}else return n}):e}resolvedOptions(){return this.dtf.resolvedOptions()}},Gf=class{constructor(e,n,r){this.opts={style:"long",...r},!n&&yg()&&(this.rtf=yC(e,r))}format(e,n){return this.rtf?this.rtf.format(e,n):BC(n,e,this.opts.numeric,this.opts.style!=="long")}formatToParts(e,n){return this.rtf?this.rtf.formatToParts(e,n):[]}},he=class{static fromOpts(e){return he.create(e.locale,e.numberingSystem,e.outputCalendar,e.defaultToEN)}static create(e,n,r,o=!1){let i=e||Ie.defaultLocale,s=i||(o?"en-US":gC()),a=n||Ie.defaultNumberingSystem,l=r||Ie.defaultOutputCalendar;return new he(s,a,l,i)}static resetCache(){Ts=null,Wf={},zf={},Uf={}}static fromObject({locale:e,numberingSystem:n,outputCalendar:r}={}){return he.create(e,n,r)}constructor(e,n,r,o){let[i,s,a]=DC(e);this.locale=i,this.numberingSystem=n||s||null,this.outputCalendar=r||a||null,this.intl=vC(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=o,this.fastNumbersCached=null}get fastNumbers(){return this.fastNumbersCached==null&&(this.fastNumbersCached=SC(this)),this.fastNumbersCached}listingMode(){let e=this.isEnglish(),n=(this.numberingSystem===null||this.numberingSystem==="latn")&&(this.outputCalendar===null||this.outputCalendar==="gregory");return e&&n?"en":"intl"}clone(e){return!e||Object.getOwnPropertyNames(e).length===0?this:he.create(e.locale||this.specifiedLocale,e.numberingSystem||this.numberingSystem,e.outputCalendar||this.outputCalendar,e.defaultToEN||!1)}redefaultToEN(e={}){return this.clone({...e,defaultToEN:!0})}redefaultToSystem(e={}){return this.clone({...e,defaultToEN:!1})}months(e,n=!1){return Hl(this,e,Eg,()=>{let r=n?{month:e,day:"numeric"}:{month:e},o=n?"format":"standalone";return this.monthsCache[o][e]||(this.monthsCache[o][e]=EC(i=>this.extract(i,r,"month"))),this.monthsCache[o][e]})}weekdays(e,n=!1){return Hl(this,e,Cg,()=>{let r=n?{weekday:e,year:"numeric",month:"long",day:"numeric"}:{weekday:e},o=n?"format":"standalone";return this.weekdaysCache[o][e]||(this.weekdaysCache[o][e]=wC(i=>this.extract(i,r,"weekday"))),this.weekdaysCache[o][e]})}meridiems(){return Hl(this,void 0,()=>xg,()=>{if(!this.meridiemCache){let e={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[q.utc(2016,11,13,9),q.utc(2016,11,13,19)].map(n=>this.extract(n,e,"dayperiod"))}return this.meridiemCache})}eras(e){return Hl(this,e,Tg,()=>{let n={era:e};return this.eraCache[e]||(this.eraCache[e]=[q.utc(-40,1,1),q.utc(2017,1,1)].map(r=>this.extract(r,n,"era"))),this.eraCache[e]})}extract(e,n,r){let o=this.dtFormatter(e,n),i=o.formatToParts(),s=i.find(a=>a.type.toLowerCase()===r);return s?s.value:null}numberFormatter(e={}){return new jf(this.intl,e.forceSimple||this.fastNumbers,e)}dtFormatter(e,n={}){return new $f(e,this.intl,n)}relFormatter(e={}){return new Gf(this.intl,this.isEnglish(),e)}listFormatter(e={}){return pC(this.intl,e)}isEnglish(){return this.locale==="en"||this.locale.toLowerCase()==="en-us"||new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith("en-us")}equals(e){return this.locale===e.locale&&this.numberingSystem===e.numberingSystem&&this.outputCalendar===e.outputCalendar}},Rf=null,rt=class extends go{static get utcInstance(){return Rf===null&&(Rf=new rt(0)),Rf}static instance(e){return e===0?rt.utcInstance:new rt(e)}static parseSpecifier(e){if(e){let n=e.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(n)return new rt(ou(n[1],n[2]))}return null}constructor(e){super(),this.fixed=e}get type(){return"fixed"}get name(){return this.fixed===0?"UTC":`UTC${_s(this.fixed,"narrow")}`}get ianaName(){return this.fixed===0?"Etc/UTC":`Etc/GMT${_s(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(e,n){return _s(this.fixed,n)}get isUniversal(){return!0}offset(){return this.fixed}equals(e){return e.type==="fixed"&&e.fixed===this.fixed}get isValid(){return!0}},Yf=class extends go{constructor(e){super(),this.zoneName=e}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}};function _r(t,e){if(le(t)||t===null)return e;if(t instanceof go)return t;if(CC(t)){let n=t.toLowerCase();return n==="default"?e:n==="local"||n==="system"?oi.instance:n==="utc"||n==="gmt"?rt.utcInstance:rt.parseSpecifier(n)||mn.create(t)}else return yo(t)?rt.instance(t):typeof t=="object"&&"offset"in t&&typeof t.offset=="function"?t:new Yf(t)}var wy=()=>Date.now(),Sy="system",Cy=null,xy=null,Ty=null,Fy=60,_y,Ie=class{static get now(){return wy}static set now(e){wy=e}static set defaultZone(e){Sy=e}static get defaultZone(){return _r(Sy,oi.instance)}static get defaultLocale(){return Cy}static set defaultLocale(e){Cy=e}static get defaultNumberingSystem(){return xy}static set defaultNumberingSystem(e){xy=e}static get defaultOutputCalendar(){return Ty}static set defaultOutputCalendar(e){Ty=e}static get twoDigitCutoffYear(){return Fy}static set twoDigitCutoffYear(e){Fy=e%100}static get throwOnInvalid(){return _y}static set throwOnInvalid(e){_y=e}static resetCaches(){he.resetCache(),mn.resetCache()}};function le(t){return typeof t=="undefined"}function yo(t){return typeof t=="number"}function nu(t){return typeof t=="number"&&t%1===0}function CC(t){return typeof t=="string"}function xC(t){return Object.prototype.toString.call(t)==="[object Date]"}function yg(){try{return typeof Intl!="undefined"&&!!Intl.RelativeTimeFormat}catch(t){return!1}}function TC(t){return Array.isArray(t)?t:[t]}function ky(t,e,n){if(t.length!==0)return t.reduce((r,o)=>{let i=[e(o),o];return r&&n(r[0],i[0])===r[0]?r:i},null)[1]}function FC(t,e){return e.reduce((n,r)=>(n[r]=t[r],n),{})}function ii(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function Xn(t,e,n){return nu(t)&&t>=e&&t<=n}function _C(t,e){return t-e*Math.floor(t/e)}function Ve(t,e=2){let n=t<0,r;return n?r="-"+(""+-t).padStart(e,"0"):r=(""+t).padStart(e,"0"),r}function Fr(t){if(!(le(t)||t===null||t===""))return parseInt(t,10)}function fo(t){if(!(le(t)||t===null||t===""))return parseFloat(t)}function td(t){if(!(le(t)||t===null||t==="")){let e=parseFloat("0."+t)*1e3;return Math.floor(e)}}function nd(t,e,n=!1){let r=10**e;return(n?Math.trunc:Math.round)(t*r)/r}function Ns(t){return t%4===0&&(t%100!==0||t%400===0)}function Fs(t){return Ns(t)?366:365}function Kl(t,e){let n=_C(e-1,12)+1,r=t+(e-n)/12;return n===2?Ns(r)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][n-1]}function ru(t){let e=Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,t.second,t.millisecond);return t.year<100&&t.year>=0&&(e=new Date(e),e.setUTCFullYear(t.year,t.month-1,t.day)),+e}function Ql(t){let e=(t+Math.floor(t/4)-Math.floor(t/100)+Math.floor(t/400))%7,n=t-1,r=(n+Math.floor(n/4)-Math.floor(n/100)+Math.floor(n/400))%7;return e===4||r===3?53:52}function Zf(t){return t>99?t:t>Ie.twoDigitCutoffYear?1900+t:2e3+t}function gg(t,e,n,r=null){let o=new Date(t),i={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};r&&(i.timeZone=r);let s={timeZoneName:e,...i},a=new Intl.DateTimeFormat(n,s).formatToParts(o).find(l=>l.type.toLowerCase()==="timezonename");return a?a.value:null}function ou(t,e){let n=parseInt(t,10);Number.isNaN(n)&&(n=0);let r=parseInt(e,10)||0,o=n<0||Object.is(n,-0)?-r:r;return n*60+o}function Dg(t){let e=Number(t);if(typeof t=="boolean"||t===""||Number.isNaN(e))throw new wt(`Invalid unit value ${t}`);return e}function Jl(t,e){let n={};for(let r in t)if(ii(t,r)){let o=t[r];if(o==null)continue;n[e(r)]=Dg(o)}return n}function _s(t,e){let n=Math.trunc(Math.abs(t/60)),r=Math.trunc(Math.abs(t%60)),o=t>=0?"+":"-";switch(e){case"short":return`${o}${Ve(n,2)}:${Ve(r,2)}`;case"narrow":return`${o}${n}${r>0?`:${r}`:""}`;case"techie":return`${o}${Ve(n,2)}${Ve(r,2)}`;default:throw new RangeError(`Value format ${e} is out of range for property format`)}}function iu(t){return FC(t,["hour","minute","second","millisecond"])}var kC=["January","February","March","April","May","June","July","August","September","October","November","December"],vg=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],OC=["J","F","M","A","M","J","J","A","S","O","N","D"];function Eg(t){switch(t){case"narrow":return[...OC];case"short":return[...vg];case"long":return[...kC];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}var wg=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],Sg=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],bC=["M","T","W","T","F","S","S"];function Cg(t){switch(t){case"narrow":return[...bC];case"short":return[...Sg];case"long":return[...wg];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}var xg=["AM","PM"],RC=["Before Christ","Anno Domini"],NC=["BC","AD"],MC=["B","A"];function Tg(t){switch(t){case"narrow":return[...MC];case"short":return[...NC];case"long":return[...RC];default:return null}}function IC(t){return xg[t.hour<12?0:1]}function AC(t,e){return Cg(e)[t.weekday-1]}function LC(t,e){return Eg(e)[t.month-1]}function PC(t,e){return Tg(e)[t.year<0?0:1]}function BC(t,e,n="always",r=!1){let o={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},i=["hours","minutes","seconds"].indexOf(t)===-1;if(n==="auto"&&i){let f=t==="days";switch(e){case 1:return f?"tomorrow":`next ${o[t][0]}`;case-1:return f?"yesterday":`last ${o[t][0]}`;case 0:return f?"today":`this ${o[t][0]}`}}let s=Object.is(e,-0)||e<0,a=Math.abs(e),l=a===1,u=o[t],d=r?l?u[1]:u[2]||u[1]:l?o[t][0]:t;return s?`${a} ${d} ago`:`in ${a} ${d}`}function Oy(t,e){let n="";for(let r of t)r.literal?n+=r.val:n+=e(r.val);return n}var VC={D:ql,DD:Qy,DDD:Jy,DDDD:Xy,t:eg,tt:tg,ttt:ng,tttt:rg,T:og,TT:ig,TTT:sg,TTTT:ag,f:lg,ff:cg,fff:dg,ffff:pg,F:ug,FF:fg,FFF:mg,FFFF:hg},Ke=class{static create(e,n={}){return new Ke(e,n)}static parseFormat(e){let n=null,r="",o=!1,i=[];for(let s=0;s<e.length;s++){let a=e.charAt(s);a==="'"?(r.length>0&&i.push({literal:o||/^\s+$/.test(r),val:r}),n=null,r="",o=!o):o||a===n?r+=a:(r.length>0&&i.push({literal:/^\s+$/.test(r),val:r}),r=a,n=a)}return r.length>0&&i.push({literal:o||/^\s+$/.test(r),val:r}),i}static macroTokenToFormatOpts(e){return VC[e]}constructor(e,n){this.opts=n,this.loc=e,this.systemLoc=null}formatWithSystemDefault(e,n){return this.systemLoc===null&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(e,{...this.opts,...n}).format()}dtFormatter(e,n={}){return this.loc.dtFormatter(e,{...this.opts,...n})}formatDateTime(e,n){return this.dtFormatter(e,n).format()}formatDateTimeParts(e,n){return this.dtFormatter(e,n).formatToParts()}formatInterval(e,n){return this.dtFormatter(e.start,n).dtf.formatRange(e.start.toJSDate(),e.end.toJSDate())}resolvedOptions(e,n){return this.dtFormatter(e,n).resolvedOptions()}num(e,n=0){if(this.opts.forceSimple)return Ve(e,n);let r={...this.opts};return n>0&&(r.padTo=n),this.loc.numberFormatter(r).format(e)}formatDateTimeFromString(e,n){let r=this.loc.listingMode()==="en",o=this.loc.outputCalendar&&this.loc.outputCalendar!=="gregory",i=(c,y)=>this.loc.extract(e,c,y),s=c=>e.isOffsetFixed&&e.offset===0&&c.allowZ?"Z":e.isValid?e.zone.formatOffset(e.ts,c.format):"",a=()=>r?IC(e):i({hour:"numeric",hourCycle:"h12"},"dayperiod"),l=(c,y)=>r?LC(e,c):i(y?{month:c}:{month:c,day:"numeric"},"month"),u=(c,y)=>r?AC(e,c):i(y?{weekday:c}:{weekday:c,month:"long",day:"numeric"},"weekday"),d=c=>{let y=Ke.macroTokenToFormatOpts(c);return y?this.formatWithSystemDefault(e,y):c},f=c=>r?PC(e,c):i({era:c},"era"),m=c=>{switch(c){case"S":return this.num(e.millisecond);case"u":case"SSS":return this.num(e.millisecond,3);case"s":return this.num(e.second);case"ss":return this.num(e.second,2);case"uu":return this.num(Math.floor(e.millisecond/10),2);case"uuu":return this.num(Math.floor(e.millisecond/100));case"m":return this.num(e.minute);case"mm":return this.num(e.minute,2);case"h":return this.num(e.hour%12===0?12:e.hour%12);case"hh":return this.num(e.hour%12===0?12:e.hour%12,2);case"H":return this.num(e.hour);case"HH":return this.num(e.hour,2);case"Z":return s({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return s({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return s({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return e.zone.offsetName(e.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return e.zone.offsetName(e.ts,{format:"long",locale:this.loc.locale});case"z":return e.zoneName;case"a":return a();case"d":return o?i({day:"numeric"},"day"):this.num(e.day);case"dd":return o?i({day:"2-digit"},"day"):this.num(e.day,2);case"c":return this.num(e.weekday);case"ccc":return u("short",!0);case"cccc":return u("long",!0);case"ccccc":return u("narrow",!0);case"E":return this.num(e.weekday);case"EEE":return u("short",!1);case"EEEE":return u("long",!1);case"EEEEE":return u("narrow",!1);case"L":return o?i({month:"numeric",day:"numeric"},"month"):this.num(e.month);case"LL":return o?i({month:"2-digit",day:"numeric"},"month"):this.num(e.month,2);case"LLL":return l("short",!0);case"LLLL":return l("long",!0);case"LLLLL":return l("narrow",!0);case"M":return o?i({month:"numeric"},"month"):this.num(e.month);case"MM":return o?i({month:"2-digit"},"month"):this.num(e.month,2);case"MMM":return l("short",!1);case"MMMM":return l("long",!1);case"MMMMM":return l("narrow",!1);case"y":return o?i({year:"numeric"},"year"):this.num(e.year);case"yy":return o?i({year:"2-digit"},"year"):this.num(e.year.toString().slice(-2),2);case"yyyy":return o?i({year:"numeric"},"year"):this.num(e.year,4);case"yyyyyy":return o?i({year:"numeric"},"year"):this.num(e.year,6);case"G":return f("short");case"GG":return f("long");case"GGGGG":return f("narrow");case"kk":return this.num(e.weekYear.toString().slice(-2),2);case"kkkk":return this.num(e.weekYear,4);case"W":return this.num(e.weekNumber);case"WW":return this.num(e.weekNumber,2);case"o":return this.num(e.ordinal);case"ooo":return this.num(e.ordinal,3);case"q":return this.num(e.quarter);case"qq":return this.num(e.quarter,2);case"X":return this.num(Math.floor(e.ts/1e3));case"x":return this.num(e.ts);default:return d(c)}};return Oy(Ke.parseFormat(n),m)}formatDurationFromString(e,n){let r=l=>{switch(l[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},o=l=>u=>{let d=r(u);return d?this.num(l.get(d),u.length):u},i=Ke.parseFormat(n),s=i.reduce((l,{literal:u,val:d})=>u?l:l.concat(d),[]),a=e.shiftTo(...s.map(r).filter(l=>l));return Oy(i,o(a))}},Rt=class{constructor(e,n){this.reason=e,this.explanation=n}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}},Fg=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function si(...t){let e=t.reduce((n,r)=>n+r.source,"");return RegExp(`^${e}$`)}function ai(...t){return e=>t.reduce(([n,r,o],i)=>{let[s,a,l]=i(e,o);return[{...n,...s},a||r,l]},[{},null,1]).slice(0,2)}function li(t,...e){if(t==null)return[null,null];for(let[n,r]of e){let o=n.exec(t);if(o)return r(o)}return[null,null]}function _g(...t){return(e,n)=>{let r={},o;for(o=0;o<t.length;o++)r[t[o]]=Fr(e[n+o]);return[r,null,n+o]}}var kg=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,WC=`(?:${kg.source}?(?:\\[(${Fg.source})\\])?)?`,rd=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,Og=RegExp(`${rd.source}${WC}`),od=RegExp(`(?:T${Og.source})?`),HC=/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,zC=/(\d{4})-?W(\d\d)(?:-?(\d))?/,UC=/(\d{4})-?(\d{3})/,jC=_g("weekYear","weekNumber","weekDay"),$C=_g("year","ordinal"),GC=/(\d{4})-(\d\d)-(\d\d)/,bg=RegExp(`${rd.source} ?(?:${kg.source}|(${Fg.source}))?`),YC=RegExp(`(?: ${bg.source})?`);function ri(t,e,n){let r=t[e];return le(r)?n:Fr(r)}function ZC(t,e){return[{year:ri(t,e),month:ri(t,e+1,1),day:ri(t,e+2,1)},null,e+3]}function ui(t,e){return[{hours:ri(t,e,0),minutes:ri(t,e+1,0),seconds:ri(t,e+2,0),milliseconds:td(t[e+3])},null,e+4]}function Ms(t,e){let n=!t[e]&&!t[e+1],r=ou(t[e+1],t[e+2]),o=n?null:rt.instance(r);return[{},o,e+3]}function Is(t,e){let n=t[e]?mn.create(t[e]):null;return[{},n,e+1]}var qC=RegExp(`^T?${rd.source}$`),KC=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function QC(t){let[e,n,r,o,i,s,a,l,u]=t,d=e[0]==="-",f=l&&l[0]==="-",m=(c,y=!1)=>c!==void 0&&(y||c&&d)?-c:c;return[{years:m(fo(n)),months:m(fo(r)),weeks:m(fo(o)),days:m(fo(i)),hours:m(fo(s)),minutes:m(fo(a)),seconds:m(fo(l),l==="-0"),milliseconds:m(td(u),f)}]}var JC={GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function id(t,e,n,r,o,i,s){let a={year:e.length===2?Zf(Fr(e)):Fr(e),month:vg.indexOf(n)+1,day:Fr(r),hour:Fr(o),minute:Fr(i)};return s&&(a.second=Fr(s)),t&&(a.weekday=t.length>3?wg.indexOf(t)+1:Sg.indexOf(t)+1),a}var XC=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function ex(t){let[,e,n,r,o,i,s,a,l,u,d,f]=t,m=id(e,o,r,n,i,s,a),c;return l?c=JC[l]:u?c=0:c=ou(d,f),[m,new rt(c)]}function tx(t){return t.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}var nx=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,rx=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,ox=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function by(t){let[,e,n,r,o,i,s,a]=t;return[id(e,o,r,n,i,s,a),rt.utcInstance]}function ix(t){let[,e,n,r,o,i,s,a]=t;return[id(e,a,n,r,o,i,s),rt.utcInstance]}var sx=si(HC,od),ax=si(zC,od),lx=si(UC,od),ux=si(Og),Rg=ai(ZC,ui,Ms,Is),cx=ai(jC,ui,Ms,Is),fx=ai($C,ui,Ms,Is),dx=ai(ui,Ms,Is);function mx(t){return li(t,[sx,Rg],[ax,cx],[lx,fx],[ux,dx])}function px(t){return li(tx(t),[XC,ex])}function hx(t){return li(t,[nx,by],[rx,by],[ox,ix])}function yx(t){return li(t,[KC,QC])}var gx=ai(ui);function Dx(t){return li(t,[qC,gx])}var vx=si(GC,YC),Ex=si(bg),wx=ai(ui,Ms,Is);function Sx(t){return li(t,[vx,Rg],[Ex,wx])}var Ry="Invalid Duration",Ng={weeks:{days:7,hours:7*24,minutes:7*24*60,seconds:7*24*60*60,milliseconds:7*24*60*60*1e3},days:{hours:24,minutes:24*60,seconds:24*60*60,milliseconds:24*60*60*1e3},hours:{minutes:60,seconds:60*60,milliseconds:60*60*1e3},minutes:{seconds:60,milliseconds:60*1e3},seconds:{milliseconds:1e3}},Cx={years:{quarters:4,months:12,weeks:52,days:365,hours:365*24,minutes:365*24*60,seconds:365*24*60*60,milliseconds:365*24*60*60*1e3},quarters:{months:3,weeks:13,days:91,hours:91*24,minutes:91*24*60,seconds:91*24*60*60,milliseconds:91*24*60*60*1e3},months:{weeks:4,days:30,hours:30*24,minutes:30*24*60,seconds:30*24*60*60,milliseconds:30*24*60*60*1e3},...Ng},jt=146097/400,ei=146097/4800,xx={years:{quarters:4,months:12,weeks:jt/7,days:jt,hours:jt*24,minutes:jt*24*60,seconds:jt*24*60*60,milliseconds:jt*24*60*60*1e3},quarters:{months:3,weeks:jt/28,days:jt/4,hours:jt*24/4,minutes:jt*24*60/4,seconds:jt*24*60*60/4,milliseconds:jt*24*60*60*1e3/4},months:{weeks:ei/7,days:ei,hours:ei*24,minutes:ei*24*60,seconds:ei*24*60*60,milliseconds:ei*24*60*60*1e3},...Ng},ho=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],Tx=ho.slice(0).reverse();function Tr(t,e,n=!1){let r={values:n?e.values:{...t.values,...e.values||{}},loc:t.loc.clone(e.loc),conversionAccuracy:e.conversionAccuracy||t.conversionAccuracy,matrix:e.matrix||t.matrix};return new j(r)}function Mg(t,e){var r;let n=(r=e.milliseconds)!=null?r:0;for(let o of Tx.slice(1))e[o]&&(n+=e[o]*t[o].milliseconds);return n}function Ny(t,e){let n=Mg(t,e)<0?-1:1;ho.reduceRight((r,o)=>{if(le(e[o]))return r;if(r){let i=e[r]*n,s=t[o][r],a=Math.floor(i/s);e[o]+=a*n,e[r]-=a*s*n}return o},null),ho.reduce((r,o)=>{if(le(e[o]))return r;if(r){let i=e[r]%1;e[r]-=i,e[o]+=i*t[r][o]}return o},null)}function Fx(t){let e={};for(let[n,r]of Object.entries(t))r!==0&&(e[n]=r);return e}var j=class{constructor(e){let n=e.conversionAccuracy==="longterm"||!1,r=n?xx:Cx;e.matrix&&(r=e.matrix),this.values=e.values,this.loc=e.loc||he.create(),this.conversionAccuracy=n?"longterm":"casual",this.invalid=e.invalid||null,this.matrix=r,this.isLuxonDuration=!0}static fromMillis(e,n){return j.fromObject({milliseconds:e},n)}static fromObject(e,n={}){if(e==null||typeof e!="object")throw new wt(`Duration.fromObject: argument expected to be an object, got ${e===null?"null":typeof e}`);return new j({values:Jl(e,j.normalizeUnit),loc:he.fromObject(n),conversionAccuracy:n.conversionAccuracy,matrix:n.matrix})}static fromDurationLike(e){if(yo(e))return j.fromMillis(e);if(j.isDuration(e))return e;if(typeof e=="object")return j.fromObject(e);throw new wt(`Unknown duration argument ${e} of type ${typeof e}`)}static fromISO(e,n){let[r]=yx(e);return r?j.fromObject(r,n):j.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static fromISOTime(e,n){let[r]=Dx(e);return r?j.fromObject(r,n):j.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static invalid(e,n=null){if(!e)throw new wt("need to specify a reason the Duration is invalid");let r=e instanceof Rt?e:new Rt(e,n);if(Ie.throwOnInvalid)throw new Vf(r);return new j({invalid:r})}static normalizeUnit(e){let n={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[e&&e.toLowerCase()];if(!n)throw new Zl(e);return n}static isDuration(e){return e&&e.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(e,n={}){let r={...n,floor:n.round!==!1&&n.floor!==!1};return this.isValid?Ke.create(this.loc,r).formatDurationFromString(this,e):Ry}toHuman(e={}){if(!this.isValid)return Ry;let n=ho.map(r=>{let o=this.values[r];return le(o)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...e,unit:r.slice(0,-1)}).format(o)}).filter(r=>r);return this.loc.listFormatter({type:"conjunction",style:e.listStyle||"narrow",...e}).format(n)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let e="P";return this.years!==0&&(e+=this.years+"Y"),(this.months!==0||this.quarters!==0)&&(e+=this.months+this.quarters*3+"M"),this.weeks!==0&&(e+=this.weeks+"W"),this.days!==0&&(e+=this.days+"D"),(this.hours!==0||this.minutes!==0||this.seconds!==0||this.milliseconds!==0)&&(e+="T"),this.hours!==0&&(e+=this.hours+"H"),this.minutes!==0&&(e+=this.minutes+"M"),(this.seconds!==0||this.milliseconds!==0)&&(e+=nd(this.seconds+this.milliseconds/1e3,3)+"S"),e==="P"&&(e+="T0S"),e}toISOTime(e={}){if(!this.isValid)return null;let n=this.toMillis();return n<0||n>=864e5?null:(e={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...e,includeOffset:!1},q.fromMillis(n,{zone:"UTC"}).toISOTime(e))}toJSON(){return this.toISO()}toString(){return this.toISO()}toMillis(){return this.isValid?Mg(this.matrix,this.values):NaN}valueOf(){return this.toMillis()}plus(e){if(!this.isValid)return this;let n=j.fromDurationLike(e),r={};for(let o of ho)(ii(n.values,o)||ii(this.values,o))&&(r[o]=n.get(o)+this.get(o));return Tr(this,{values:r},!0)}minus(e){if(!this.isValid)return this;let n=j.fromDurationLike(e);return this.plus(n.negate())}mapUnits(e){if(!this.isValid)return this;let n={};for(let r of Object.keys(this.values))n[r]=Dg(e(this.values[r],r));return Tr(this,{values:n},!0)}get(e){return this[j.normalizeUnit(e)]}set(e){if(!this.isValid)return this;let n={...this.values,...Jl(e,j.normalizeUnit)};return Tr(this,{values:n})}reconfigure({locale:e,numberingSystem:n,conversionAccuracy:r,matrix:o}={}){let s={loc:this.loc.clone({locale:e,numberingSystem:n}),matrix:o,conversionAccuracy:r};return Tr(this,s)}as(e){return this.isValid?this.shiftTo(e).get(e):NaN}normalize(){if(!this.isValid)return this;let e=this.toObject();return Ny(this.matrix,e),Tr(this,{values:e},!0)}rescale(){if(!this.isValid)return this;let e=Fx(this.normalize().shiftToAll().toObject());return Tr(this,{values:e},!0)}shiftTo(...e){if(!this.isValid)return this;if(e.length===0)return this;e=e.map(s=>j.normalizeUnit(s));let n={},r={},o=this.toObject(),i;for(let s of ho)if(e.indexOf(s)>=0){i=s;let a=0;for(let u in r)a+=this.matrix[u][s]*r[u],r[u]=0;yo(o[s])&&(a+=o[s]);let l=Math.trunc(a);n[s]=l,r[s]=(a*1e3-l*1e3)/1e3}else yo(o[s])&&(r[s]=o[s]);for(let s in r)r[s]!==0&&(n[i]+=s===i?r[s]:r[s]/this.matrix[i][s]);return Ny(this.matrix,n),Tr(this,{values:n},!0)}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;let e={};for(let n of Object.keys(this.values))e[n]=this.values[n]===0?0:-this.values[n];return Tr(this,{values:e},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(e){if(!this.isValid||!e.isValid||!this.loc.equals(e.loc))return!1;function n(r,o){return r===void 0||r===0?o===void 0||o===0:r===o}for(let r of ho)if(!n(this.values[r],e.values[r]))return!1;return!0}},ti="Invalid Interval";function _x(t,e){return!t||!t.isValid?Ce.invalid("missing or invalid start"):!e||!e.isValid?Ce.invalid("missing or invalid end"):e<t?Ce.invalid("end before start",`The end of an interval must be after its start, but you had start=${t.toISO()} and end=${e.toISO()}`):null}var Ce=class{constructor(e){this.s=e.start,this.e=e.end,this.invalid=e.invalid||null,this.isLuxonInterval=!0}static invalid(e,n=null){if(!e)throw new wt("need to specify a reason the Interval is invalid");let r=e instanceof Rt?e:new Rt(e,n);if(Ie.throwOnInvalid)throw new Bf(r);return new Ce({invalid:r})}static fromDateTimes(e,n){let r=Cs(e),o=Cs(n),i=_x(r,o);return i==null?new Ce({start:r,end:o}):i}static after(e,n){let r=j.fromDurationLike(n),o=Cs(e);return Ce.fromDateTimes(o,o.plus(r))}static before(e,n){let r=j.fromDurationLike(n),o=Cs(e);return Ce.fromDateTimes(o.minus(r),o)}static fromISO(e,n){let[r,o]=(e||"").split("/",2);if(r&&o){let i,s;try{i=q.fromISO(r,n),s=i.isValid}catch(u){s=!1}let a,l;try{a=q.fromISO(o,n),l=a.isValid}catch(u){l=!1}if(s&&l)return Ce.fromDateTimes(i,a);if(s){let u=j.fromISO(o,n);if(u.isValid)return Ce.after(i,u)}else if(l){let u=j.fromISO(r,n);if(u.isValid)return Ce.before(a,u)}}return Ce.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static isInterval(e){return e&&e.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get isValid(){return this.invalidReason===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(e="milliseconds"){return this.isValid?this.toDuration(e).get(e):NaN}count(e="milliseconds"){if(!this.isValid)return NaN;let n=this.start.startOf(e),r=this.end.startOf(e);return Math.floor(r.diff(n,e).get(e))+(r.valueOf()!==this.end.valueOf())}hasSame(e){return this.isValid?this.isEmpty()||this.e.minus(1).hasSame(this.s,e):!1}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(e){return this.isValid?this.s>e:!1}isBefore(e){return this.isValid?this.e<=e:!1}contains(e){return this.isValid?this.s<=e&&this.e>e:!1}set({start:e,end:n}={}){return this.isValid?Ce.fromDateTimes(e||this.s,n||this.e):this}splitAt(...e){if(!this.isValid)return[];let n=e.map(Cs).filter(s=>this.contains(s)).sort(),r=[],{s:o}=this,i=0;for(;o<this.e;){let s=n[i]||this.e,a=+s>+this.e?this.e:s;r.push(Ce.fromDateTimes(o,a)),o=a,i+=1}return r}splitBy(e){let n=j.fromDurationLike(e);if(!this.isValid||!n.isValid||n.as("milliseconds")===0)return[];let{s:r}=this,o=1,i,s=[];for(;r<this.e;){let a=this.start.plus(n.mapUnits(l=>l*o));i=+a>+this.e?this.e:a,s.push(Ce.fromDateTimes(r,i)),r=i,o+=1}return s}divideEqually(e){return this.isValid?this.splitBy(this.length()/e).slice(0,e):[]}overlaps(e){return this.e>e.s&&this.s<e.e}abutsStart(e){return this.isValid?+this.e==+e.s:!1}abutsEnd(e){return this.isValid?+e.e==+this.s:!1}engulfs(e){return this.isValid?this.s<=e.s&&this.e>=e.e:!1}equals(e){return!this.isValid||!e.isValid?!1:this.s.equals(e.s)&&this.e.equals(e.e)}intersection(e){if(!this.isValid)return this;let n=this.s>e.s?this.s:e.s,r=this.e<e.e?this.e:e.e;return n>=r?null:Ce.fromDateTimes(n,r)}union(e){if(!this.isValid)return this;let n=this.s<e.s?this.s:e.s,r=this.e>e.e?this.e:e.e;return Ce.fromDateTimes(n,r)}static merge(e){let[n,r]=e.sort((o,i)=>o.s-i.s).reduce(([o,i],s)=>i?i.overlaps(s)||i.abutsStart(s)?[o,i.union(s)]:[o.concat([i]),s]:[o,s],[[],null]);return r&&n.push(r),n}static xor(e){let n=null,r=0,o=[],i=e.map(l=>[{time:l.s,type:"s"},{time:l.e,type:"e"}]),s=Array.prototype.concat(...i),a=s.sort((l,u)=>l.time-u.time);for(let l of a)r+=l.type==="s"?1:-1,r===1?n=l.time:(n&&+n!=+l.time&&o.push(Ce.fromDateTimes(n,l.time)),n=null);return Ce.merge(o)}difference(...e){return Ce.xor([this].concat(e)).map(n=>this.intersection(n)).filter(n=>n&&!n.isEmpty())}toString(){return this.isValid?`[${this.s.toISO()} \u2013 ${this.e.toISO()})`:ti}toLocaleString(e=ql,n={}){return this.isValid?Ke.create(this.s.loc.clone(n),e).formatInterval(this):ti}toISO(e){return this.isValid?`${this.s.toISO(e)}/${this.e.toISO(e)}`:ti}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:ti}toISOTime(e){return this.isValid?`${this.s.toISOTime(e)}/${this.e.toISOTime(e)}`:ti}toFormat(e,{separator:n=" \u2013 "}={}){return this.isValid?`${this.s.toFormat(e)}${n}${this.e.toFormat(e)}`:ti}toDuration(e,n){return this.isValid?this.e.diff(this.s,e,n):j.invalid(this.invalidReason)}mapEndpoints(e){return Ce.fromDateTimes(e(this.s),e(this.e))}},ni=class{static hasDST(e=Ie.defaultZone){let n=q.now().setZone(e).set({month:12});return!e.isUniversal&&n.offset!==n.set({month:6}).offset}static isValidIANAZone(e){return mn.isValidZone(e)}static normalizeZone(e){return _r(e,Ie.defaultZone)}static months(e="long",{locale:n=null,numberingSystem:r=null,locObj:o=null,outputCalendar:i="gregory"}={}){return(o||he.create(n,r,i)).months(e)}static monthsFormat(e="long",{locale:n=null,numberingSystem:r=null,locObj:o=null,outputCalendar:i="gregory"}={}){return(o||he.create(n,r,i)).months(e,!0)}static weekdays(e="long",{locale:n=null,numberingSystem:r=null,locObj:o=null}={}){return(o||he.create(n,r,null)).weekdays(e)}static weekdaysFormat(e="long",{locale:n=null,numberingSystem:r=null,locObj:o=null}={}){return(o||he.create(n,r,null)).weekdays(e,!0)}static meridiems({locale:e=null}={}){return he.create(e).meridiems()}static eras(e="short",{locale:n=null}={}){return he.create(n,null,"gregory").eras(e)}static features(){return{relative:yg()}}};function My(t,e){let n=o=>o.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),r=n(e)-n(t);return Math.floor(j.fromMillis(r).as("days"))}function kx(t,e,n){let r=[["years",(l,u)=>u.year-l.year],["quarters",(l,u)=>u.quarter-l.quarter+(u.year-l.year)*4],["months",(l,u)=>u.month-l.month+(u.year-l.year)*12],["weeks",(l,u)=>{let d=My(l,u);return(d-d%7)/7}],["days",My]],o={},i=t,s,a;for(let[l,u]of r)n.indexOf(l)>=0&&(s=l,o[l]=u(t,e),a=i.plus(o),a>e?(o[l]--,t=i.plus(o),t>e&&(a=t,o[l]--,t=i.plus(o))):t=a);return[t,o,a,s]}function Ox(t,e,n,r){let[o,i,s,a]=kx(t,e,n),l=e-o,u=n.filter(f=>["hours","minutes","seconds","milliseconds"].indexOf(f)>=0);u.length===0&&(s<e&&(s=o.plus({[a]:1})),s!==o&&(i[a]=(i[a]||0)+l/(s-o)));let d=j.fromObject(i,r);return u.length>0?j.fromMillis(l,r).shiftTo(...u).plus(d):d}var sd={arab:"[\u0660-\u0669]",arabext:"[\u06F0-\u06F9]",bali:"[\u1B50-\u1B59]",beng:"[\u09E6-\u09EF]",deva:"[\u0966-\u096F]",fullwide:"[\uFF10-\uFF19]",gujr:"[\u0AE6-\u0AEF]",hanidec:"[\u3007|\u4E00|\u4E8C|\u4E09|\u56DB|\u4E94|\u516D|\u4E03|\u516B|\u4E5D]",khmr:"[\u17E0-\u17E9]",knda:"[\u0CE6-\u0CEF]",laoo:"[\u0ED0-\u0ED9]",limb:"[\u1946-\u194F]",mlym:"[\u0D66-\u0D6F]",mong:"[\u1810-\u1819]",mymr:"[\u1040-\u1049]",orya:"[\u0B66-\u0B6F]",tamldec:"[\u0BE6-\u0BEF]",telu:"[\u0C66-\u0C6F]",thai:"[\u0E50-\u0E59]",tibt:"[\u0F20-\u0F29]",latn:"\\d"},Iy={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},bx=sd.hanidec.replace(/[\[|\]]/g,"").split("");function Rx(t){let e=parseInt(t,10);if(isNaN(e)){e="";for(let n=0;n<t.length;n++){let r=t.charCodeAt(n);if(t[n].search(sd.hanidec)!==-1)e+=bx.indexOf(t[n]);else for(let o in Iy){let[i,s]=Iy[o];r>=i&&r<=s&&(e+=r-i)}}return parseInt(e,10)}else return e}function un({numberingSystem:t},e=""){return new RegExp(`${sd[t||"latn"]}${e}`)}var Nx="missing Intl.DateTimeFormat.formatToParts support";function fe(t,e=n=>n){return{regex:t,deser:([n])=>e(Rx(n))}}var Mx=String.fromCharCode(160),Ig=`[ ${Mx}]`,Ag=new RegExp(Ig,"g");function Ix(t){return t.replace(/\./g,"\\.?").replace(Ag,Ig)}function Ay(t){return t.replace(/\./g,"").replace(Ag," ").toLowerCase()}function cn(t,e){return t===null?null:{regex:RegExp(t.map(Ix).join("|")),deser:([n])=>t.findIndex(r=>Ay(n)===Ay(r))+e}}function Ly(t,e){return{regex:t,deser:([,n,r])=>ou(n,r),groups:e}}function zl(t){return{regex:t,deser:([e])=>e}}function Ax(t){return t.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function Lx(t,e){let n=un(e),r=un(e,"{2}"),o=un(e,"{3}"),i=un(e,"{4}"),s=un(e,"{6}"),a=un(e,"{1,2}"),l=un(e,"{1,3}"),u=un(e,"{1,6}"),d=un(e,"{1,9}"),f=un(e,"{2,4}"),m=un(e,"{4,6}"),c=S=>({regex:RegExp(Ax(S.val)),deser:([h])=>h,literal:!0}),v=(S=>{if(t.literal)return c(S);switch(S.val){case"G":return cn(e.eras("short"),0);case"GG":return cn(e.eras("long"),0);case"y":return fe(u);case"yy":return fe(f,Zf);case"yyyy":return fe(i);case"yyyyy":return fe(m);case"yyyyyy":return fe(s);case"M":return fe(a);case"MM":return fe(r);case"MMM":return cn(e.months("short",!0),1);case"MMMM":return cn(e.months("long",!0),1);case"L":return fe(a);case"LL":return fe(r);case"LLL":return cn(e.months("short",!1),1);case"LLLL":return cn(e.months("long",!1),1);case"d":return fe(a);case"dd":return fe(r);case"o":return fe(l);case"ooo":return fe(o);case"HH":return fe(r);case"H":return fe(a);case"hh":return fe(r);case"h":return fe(a);case"mm":return fe(r);case"m":return fe(a);case"q":return fe(a);case"qq":return fe(r);case"s":return fe(a);case"ss":return fe(r);case"S":return fe(l);case"SSS":return fe(o);case"u":return zl(d);case"uu":return zl(a);case"uuu":return fe(n);case"a":return cn(e.meridiems(),0);case"kkkk":return fe(i);case"kk":return fe(f,Zf);case"W":return fe(a);case"WW":return fe(r);case"E":case"c":return fe(n);case"EEE":return cn(e.weekdays("short",!1),1);case"EEEE":return cn(e.weekdays("long",!1),1);case"ccc":return cn(e.weekdays("short",!0),1);case"cccc":return cn(e.weekdays("long",!0),1);case"Z":case"ZZ":return Ly(new RegExp(`([+-]${a.source})(?::(${r.source}))?`),2);case"ZZZ":return Ly(new RegExp(`([+-]${a.source})(${r.source})?`),2);case"z":return zl(/[a-z_+-/]{1,256}?/i);case" ":return zl(/[^\S\n\r]/);default:return c(S)}})(t)||{invalidReason:Nx};return v.token=t,v}var Px={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour12:{numeric:"h","2-digit":"hh"},hour24:{numeric:"H","2-digit":"HH"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};function Bx(t,e,n){let{type:r,value:o}=t;if(r==="literal"){let l=/^\s+$/.test(o);return{literal:!l,val:l?" ":o}}let i=e[r],s=r;r==="hour"&&(e.hour12!=null?s=e.hour12?"hour12":"hour24":e.hourCycle!=null?e.hourCycle==="h11"||e.hourCycle==="h12"?s="hour12":s="hour24":s=n.hour12?"hour12":"hour24");let a=Px[s];if(typeof a=="object"&&(a=a[i]),a)return{literal:!1,val:a}}function Vx(t){return[`^${t.map(n=>n.regex).reduce((n,r)=>`${n}(${r.source})`,"")}$`,t]}function Wx(t,e,n){let r=t.match(e);if(r){let o={},i=1;for(let s in n)if(ii(n,s)){let a=n[s],l=a.groups?a.groups+1:1;!a.literal&&a.token&&(o[a.token.val[0]]=a.deser(r.slice(i,i+l))),i+=l}return[r,o]}else return[r,{}]}function Hx(t){let e=i=>{switch(i){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}},n=null,r;return le(t.z)||(n=mn.create(t.z)),le(t.Z)||(n||(n=new rt(t.Z)),r=t.Z),le(t.q)||(t.M=(t.q-1)*3+1),le(t.h)||(t.h<12&&t.a===1?t.h+=12:t.h===12&&t.a===0&&(t.h=0)),t.G===0&&t.y&&(t.y=-t.y),le(t.u)||(t.S=td(t.u)),[Object.keys(t).reduce((i,s)=>{let a=e(s);return a&&(i[a]=t[s]),i},{}),n,r]}var Nf=null;function zx(){return Nf||(Nf=q.fromMillis(1555555555555)),Nf}function Ux(t,e){if(t.literal)return t;let n=Ke.macroTokenToFormatOpts(t.val),r=Bg(n,e);return r==null||r.includes(void 0)?t:r}function Lg(t,e){return Array.prototype.concat(...t.map(n=>Ux(n,e)))}function Pg(t,e,n){let r=Lg(Ke.parseFormat(n),t),o=r.map(s=>Lx(s,t)),i=o.find(s=>s.invalidReason);if(i)return{input:e,tokens:r,invalidReason:i.invalidReason};{let[s,a]=Vx(o),l=RegExp(s,"i"),[u,d]=Wx(e,l,a),[f,m,c]=d?Hx(d):[null,null,void 0];if(ii(d,"a")&&ii(d,"H"))throw new po("Can't include meridiem when specifying 24-hour format");return{input:e,tokens:r,regex:l,rawMatches:u,matches:d,result:f,zone:m,specificOffset:c}}}function jx(t,e,n){let{result:r,zone:o,specificOffset:i,invalidReason:s}=Pg(t,e,n);return[r,o,i,s]}function Bg(t,e){if(!t)return null;let r=Ke.create(e,t).dtFormatter(zx()),o=r.formatToParts(),i=r.resolvedOptions();return o.map(s=>Bx(s,t,i))}var Vg=[0,31,59,90,120,151,181,212,243,273,304,334],Wg=[0,31,60,91,121,152,182,213,244,274,305,335];function $t(t,e){return new Rt("unit out of range",`you specified ${e} (of type ${typeof e}) as a ${t}, which is invalid`)}function Hg(t,e,n){let r=new Date(Date.UTC(t,e-1,n));t<100&&t>=0&&r.setUTCFullYear(r.getUTCFullYear()-1900);let o=r.getUTCDay();return o===0?7:o}function zg(t,e,n){return n+(Ns(t)?Wg:Vg)[e-1]}function Ug(t,e){let n=Ns(t)?Wg:Vg,r=n.findIndex(i=>i<e),o=e-n[r];return{month:r+1,day:o}}function qf(t){let{year:e,month:n,day:r}=t,o=zg(e,n,r),i=Hg(e,n,r),s=Math.floor((o-i+10)/7),a;return s<1?(a=e-1,s=Ql(a)):s>Ql(e)?(a=e+1,s=1):a=e,{weekYear:a,weekNumber:s,weekday:i,...iu(t)}}function Py(t){let{weekYear:e,weekNumber:n,weekday:r}=t,o=Hg(e,1,4),i=Fs(e),s=n*7+r-o-3,a;s<1?(a=e-1,s+=Fs(a)):s>i?(a=e+1,s-=Fs(e)):a=e;let{month:l,day:u}=Ug(a,s);return{year:a,month:l,day:u,...iu(t)}}function Mf(t){let{year:e,month:n,day:r}=t,o=zg(e,n,r);return{year:e,ordinal:o,...iu(t)}}function By(t){let{year:e,ordinal:n}=t,{month:r,day:o}=Ug(e,n);return{year:e,month:r,day:o,...iu(t)}}function $x(t){let e=nu(t.weekYear),n=Xn(t.weekNumber,1,Ql(t.weekYear)),r=Xn(t.weekday,1,7);return e?n?r?!1:$t("weekday",t.weekday):$t("week",t.week):$t("weekYear",t.weekYear)}function Gx(t){let e=nu(t.year),n=Xn(t.ordinal,1,Fs(t.year));return e?n?!1:$t("ordinal",t.ordinal):$t("year",t.year)}function jg(t){let e=nu(t.year),n=Xn(t.month,1,12),r=Xn(t.day,1,Kl(t.year,t.month));return e?n?r?!1:$t("day",t.day):$t("month",t.month):$t("year",t.year)}function $g(t){let{hour:e,minute:n,second:r,millisecond:o}=t,i=Xn(e,0,23)||e===24&&n===0&&r===0&&o===0,s=Xn(n,0,59),a=Xn(r,0,59),l=Xn(o,0,999);return i?s?a?l?!1:$t("millisecond",o):$t("second",r):$t("minute",n):$t("hour",e)}var If="Invalid DateTime",Vy=864e13;function Ul(t){return new Rt("unsupported zone",`the zone "${t.name}" is not supported`)}function Af(t){return t.weekData===null&&(t.weekData=qf(t.c)),t.weekData}function mo(t,e){let n={ts:t.ts,zone:t.zone,c:t.c,o:t.o,loc:t.loc,invalid:t.invalid};return new q({...n,...e,old:n})}function Gg(t,e,n){let r=t-e*60*1e3,o=n.offset(r);if(e===o)return[r,e];r-=(o-e)*60*1e3;let i=n.offset(r);return o===i?[r,o]:[t-Math.min(o,i)*60*1e3,Math.max(o,i)]}function jl(t,e){t+=e*60*1e3;let n=new Date(t);return{year:n.getUTCFullYear(),month:n.getUTCMonth()+1,day:n.getUTCDate(),hour:n.getUTCHours(),minute:n.getUTCMinutes(),second:n.getUTCSeconds(),millisecond:n.getUTCMilliseconds()}}function Yl(t,e,n){return Gg(ru(t),e,n)}function Wy(t,e){let n=t.o,r=t.c.year+Math.trunc(e.years),o=t.c.month+Math.trunc(e.months)+Math.trunc(e.quarters)*3,i={...t.c,year:r,month:o,day:Math.min(t.c.day,Kl(r,o))+Math.trunc(e.days)+Math.trunc(e.weeks)*7},s=j.fromObject({years:e.years-Math.trunc(e.years),quarters:e.quarters-Math.trunc(e.quarters),months:e.months-Math.trunc(e.months),weeks:e.weeks-Math.trunc(e.weeks),days:e.days-Math.trunc(e.days),hours:e.hours,minutes:e.minutes,seconds:e.seconds,milliseconds:e.milliseconds}).as("milliseconds"),a=ru(i),[l,u]=Gg(a,n,t.zone);return s!==0&&(l+=s,u=t.zone.offset(l)),{ts:l,o:u}}function Ss(t,e,n,r,o,i){let{setZone:s,zone:a}=n;if(t&&Object.keys(t).length!==0||e){let l=e||a,u=q.fromObject(t,{...n,zone:l,specificOffset:i});return s?u:u.setZone(a)}else return q.invalid(new Rt("unparsable",`the input "${o}" can't be parsed as ${r}`))}function $l(t,e,n=!0){return t.isValid?Ke.create(he.create("en-US"),{allowZ:n,forceSimple:!0}).formatDateTimeFromString(t,e):null}function Lf(t,e){let n=t.c.year>9999||t.c.year<0,r="";return n&&t.c.year>=0&&(r+="+"),r+=Ve(t.c.year,n?6:4),e?(r+="-",r+=Ve(t.c.month),r+="-",r+=Ve(t.c.day)):(r+=Ve(t.c.month),r+=Ve(t.c.day)),r}function Hy(t,e,n,r,o,i){let s=Ve(t.c.hour);return e?(s+=":",s+=Ve(t.c.minute),(t.c.millisecond!==0||t.c.second!==0||!n)&&(s+=":")):s+=Ve(t.c.minute),(t.c.millisecond!==0||t.c.second!==0||!n)&&(s+=Ve(t.c.second),(t.c.millisecond!==0||!r)&&(s+=".",s+=Ve(t.c.millisecond,3))),o&&(t.isOffsetFixed&&t.offset===0&&!i?s+="Z":t.o<0?(s+="-",s+=Ve(Math.trunc(-t.o/60)),s+=":",s+=Ve(Math.trunc(-t.o%60))):(s+="+",s+=Ve(Math.trunc(t.o/60)),s+=":",s+=Ve(Math.trunc(t.o%60)))),i&&(s+="["+t.zone.ianaName+"]"),s}var Yg={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},Yx={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},Zx={ordinal:1,hour:0,minute:0,second:0,millisecond:0},Zg=["year","month","day","hour","minute","second","millisecond"],qx=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],Kx=["year","ordinal","hour","minute","second","millisecond"];function zy(t){let e={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[t.toLowerCase()];if(!e)throw new Zl(t);return e}function Uy(t,e){let n=_r(e.zone,Ie.defaultZone),r=he.fromObject(e),o=Ie.now(),i,s;if(le(t.year))i=o;else{for(let u of Zg)le(t[u])&&(t[u]=Yg[u]);let a=jg(t)||$g(t);if(a)return q.invalid(a);let l=n.offset(o);[i,s]=Yl(t,l,n)}return new q({ts:i,zone:n,loc:r,o:s})}function jy(t,e,n){let r=le(n.round)?!0:n.round,o=(s,a)=>(s=nd(s,r||n.calendary?0:2,!0),e.loc.clone(n).relFormatter(n).format(s,a)),i=s=>n.calendary?e.hasSame(t,s)?0:e.startOf(s).diff(t.startOf(s),s).get(s):e.diff(t,s).get(s);if(n.unit)return o(i(n.unit),n.unit);for(let s of n.units){let a=i(s);if(Math.abs(a)>=1)return o(a,s)}return o(t>e?-0:0,n.units[n.units.length-1])}function $y(t){let e={},n;return t.length>0&&typeof t[t.length-1]=="object"?(e=t[t.length-1],n=Array.from(t).slice(0,t.length-1)):n=Array.from(t),[e,n]}var q=class{constructor(e){let n=e.zone||Ie.defaultZone,r=e.invalid||(Number.isNaN(e.ts)?new Rt("invalid input"):null)||(n.isValid?null:Ul(n));this.ts=le(e.ts)?Ie.now():e.ts;let o=null,i=null;if(!r)if(e.old&&e.old.ts===this.ts&&e.old.zone.equals(n))[o,i]=[e.old.c,e.old.o];else{let a=n.offset(this.ts);o=jl(this.ts,a),r=Number.isNaN(o.year)?new Rt("invalid input"):null,o=r?null:o,i=r?null:a}this._zone=n,this.loc=e.loc||he.create(),this.invalid=r,this.weekData=null,this.c=o,this.o=i,this.isLuxonDateTime=!0}static now(){return new q({})}static local(){let[e,n]=$y(arguments),[r,o,i,s,a,l,u]=n;return Uy({year:r,month:o,day:i,hour:s,minute:a,second:l,millisecond:u},e)}static utc(){let[e,n]=$y(arguments),[r,o,i,s,a,l,u]=n;return e.zone=rt.utcInstance,Uy({year:r,month:o,day:i,hour:s,minute:a,second:l,millisecond:u},e)}static fromJSDate(e,n={}){let r=xC(e)?e.valueOf():NaN;if(Number.isNaN(r))return q.invalid("invalid input");let o=_r(n.zone,Ie.defaultZone);return o.isValid?new q({ts:r,zone:o,loc:he.fromObject(n)}):q.invalid(Ul(o))}static fromMillis(e,n={}){if(yo(e))return e<-Vy||e>Vy?q.invalid("Timestamp out of range"):new q({ts:e,zone:_r(n.zone,Ie.defaultZone),loc:he.fromObject(n)});throw new wt(`fromMillis requires a numerical input, but received a ${typeof e} with value ${e}`)}static fromSeconds(e,n={}){if(yo(e))return new q({ts:e*1e3,zone:_r(n.zone,Ie.defaultZone),loc:he.fromObject(n)});throw new wt("fromSeconds requires a numerical input")}static fromObject(e,n={}){e=e||{};let r=_r(n.zone,Ie.defaultZone);if(!r.isValid)return q.invalid(Ul(r));let o=Ie.now(),i=le(n.specificOffset)?r.offset(o):n.specificOffset,s=Jl(e,zy),a=!le(s.ordinal),l=!le(s.year),u=!le(s.month)||!le(s.day),d=l||u,f=s.weekYear||s.weekNumber,m=he.fromObject(n);if((d||a)&&f)throw new po("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(u&&a)throw new po("Can't mix ordinal dates with month/day");let c=f||s.weekday&&!d,y,v,S=jl(o,i);c?(y=qx,v=Yx,S=qf(S)):a?(y=Kx,v=Zx,S=Mf(S)):(y=Zg,v=Yg);let h=!1;for(let k of y){let L=s[k];le(L)?h?s[k]=v[k]:s[k]=S[k]:h=!0}let p=c?$x(s):a?Gx(s):jg(s),E=p||$g(s);if(E)return q.invalid(E);let D=c?Py(s):a?By(s):s,[x,O]=Yl(D,i,r),_=new q({ts:x,zone:r,o:O,loc:m});return s.weekday&&d&&e.weekday!==_.weekday?q.invalid("mismatched weekday",`you can't specify both a weekday of ${s.weekday} and a date of ${_.toISO()}`):_}static fromISO(e,n={}){let[r,o]=mx(e);return Ss(r,o,n,"ISO 8601",e)}static fromRFC2822(e,n={}){let[r,o]=px(e);return Ss(r,o,n,"RFC 2822",e)}static fromHTTP(e,n={}){let[r,o]=hx(e);return Ss(r,o,n,"HTTP",n)}static fromFormat(e,n,r={}){if(le(e)||le(n))throw new wt("fromFormat requires an input string and a format");let{locale:o=null,numberingSystem:i=null}=r,s=he.fromOpts({locale:o,numberingSystem:i,defaultToEN:!0}),[a,l,u,d]=jx(s,e,n);return d?q.invalid(d):Ss(a,l,r,`format ${n}`,e,u)}static fromString(e,n,r={}){return q.fromFormat(e,n,r)}static fromSQL(e,n={}){let[r,o]=Sx(e);return Ss(r,o,n,"SQL",e)}static invalid(e,n=null){if(!e)throw new wt("need to specify a reason the DateTime is invalid");let r=e instanceof Rt?e:new Rt(e,n);if(Ie.throwOnInvalid)throw new Pf(r);return new q({invalid:r})}static isDateTime(e){return e&&e.isLuxonDateTime||!1}static parseFormatForOpts(e,n={}){let r=Bg(e,he.fromObject(n));return r?r.map(o=>o?o.val:null).join(""):null}static expandFormat(e,n={}){return Lg(Ke.parseFormat(e),he.fromObject(n)).map(o=>o.val).join("")}get(e){return this[e]}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?Af(this).weekYear:NaN}get weekNumber(){return this.isValid?Af(this).weekNumber:NaN}get weekday(){return this.isValid?Af(this).weekday:NaN}get ordinal(){return this.isValid?Mf(this.c).ordinal:NaN}get monthShort(){return this.isValid?ni.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?ni.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?ni.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?ni.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return this.isOffsetFixed?!1:this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset}getPossibleOffsets(){if(!this.isValid||this.isOffsetFixed)return[this];let e=864e5,n=6e4,r=ru(this.c),o=this.zone.offset(r-e),i=this.zone.offset(r+e),s=this.zone.offset(r-o*n),a=this.zone.offset(r-i*n);if(s===a)return[this];let l=r-s*n,u=r-a*n,d=jl(l,s),f=jl(u,a);return d.hour===f.hour&&d.minute===f.minute&&d.second===f.second&&d.millisecond===f.millisecond?[mo(this,{ts:l}),mo(this,{ts:u})]:[this]}get isInLeapYear(){return Ns(this.year)}get daysInMonth(){return Kl(this.year,this.month)}get daysInYear(){return this.isValid?Fs(this.year):NaN}get weeksInWeekYear(){return this.isValid?Ql(this.weekYear):NaN}resolvedLocaleOptions(e={}){let{locale:n,numberingSystem:r,calendar:o}=Ke.create(this.loc.clone(e),e).resolvedOptions(this);return{locale:n,numberingSystem:r,outputCalendar:o}}toUTC(e=0,n={}){return this.setZone(rt.instance(e),n)}toLocal(){return this.setZone(Ie.defaultZone)}setZone(e,{keepLocalTime:n=!1,keepCalendarTime:r=!1}={}){if(e=_r(e,Ie.defaultZone),e.equals(this.zone))return this;if(e.isValid){let o=this.ts;if(n||r){let i=e.offset(this.ts),s=this.toObject();[o]=Yl(s,i,e)}return mo(this,{ts:o,zone:e})}else return q.invalid(Ul(e))}reconfigure({locale:e,numberingSystem:n,outputCalendar:r}={}){let o=this.loc.clone({locale:e,numberingSystem:n,outputCalendar:r});return mo(this,{loc:o})}setLocale(e){return this.reconfigure({locale:e})}set(e){if(!this.isValid)return this;let n=Jl(e,zy),r=!le(n.weekYear)||!le(n.weekNumber)||!le(n.weekday),o=!le(n.ordinal),i=!le(n.year),s=!le(n.month)||!le(n.day),a=i||s,l=n.weekYear||n.weekNumber;if((a||o)&&l)throw new po("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(s&&o)throw new po("Can't mix ordinal dates with month/day");let u;r?u=Py({...qf(this.c),...n}):le(n.ordinal)?(u={...this.toObject(),...n},le(n.day)&&(u.day=Math.min(Kl(u.year,u.month),u.day))):u=By({...Mf(this.c),...n});let[d,f]=Yl(u,this.o,this.zone);return mo(this,{ts:d,o:f})}plus(e){if(!this.isValid)return this;let n=j.fromDurationLike(e);return mo(this,Wy(this,n))}minus(e){if(!this.isValid)return this;let n=j.fromDurationLike(e).negate();return mo(this,Wy(this,n))}startOf(e){if(!this.isValid)return this;let n={},r=j.normalizeUnit(e);switch(r){case"years":n.month=1;case"quarters":case"months":n.day=1;case"weeks":case"days":n.hour=0;case"hours":n.minute=0;case"minutes":n.second=0;case"seconds":n.millisecond=0;break}if(r==="weeks"&&(n.weekday=1),r==="quarters"){let o=Math.ceil(this.month/3);n.month=(o-1)*3+1}return this.set(n)}endOf(e){return this.isValid?this.plus({[e]:1}).startOf(e).minus(1):this}toFormat(e,n={}){return this.isValid?Ke.create(this.loc.redefaultToEN(n)).formatDateTimeFromString(this,e):If}toLocaleString(e=ql,n={}){return this.isValid?Ke.create(this.loc.clone(n),e).formatDateTime(this):If}toLocaleParts(e={}){return this.isValid?Ke.create(this.loc.clone(e),e).formatDateTimeParts(this):[]}toISO({format:e="extended",suppressSeconds:n=!1,suppressMilliseconds:r=!1,includeOffset:o=!0,extendedZone:i=!1}={}){if(!this.isValid)return null;let s=e==="extended",a=Lf(this,s);return a+="T",a+=Hy(this,s,n,r,o,i),a}toISODate({format:e="extended"}={}){return this.isValid?Lf(this,e==="extended"):null}toISOWeekDate(){return $l(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:e=!1,suppressSeconds:n=!1,includeOffset:r=!0,includePrefix:o=!1,extendedZone:i=!1,format:s="extended"}={}){return this.isValid?(o?"T":"")+Hy(this,s==="extended",n,e,r,i):null}toRFC2822(){return $l(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return $l(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?Lf(this,!0):null}toSQLTime({includeOffset:e=!0,includeZone:n=!1,includeOffsetSpace:r=!0}={}){let o="HH:mm:ss.SSS";return(n||e)&&(r&&(o+=" "),n?o+="z":e&&(o+="ZZ")),$l(this,o,!0)}toSQL(e={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(e)}`:null}toString(){return this.isValid?this.toISO():If}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(e={}){if(!this.isValid)return{};let n={...this.c};return e.includeConfig&&(n.outputCalendar=this.outputCalendar,n.numberingSystem=this.loc.numberingSystem,n.locale=this.loc.locale),n}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(e,n="milliseconds",r={}){if(!this.isValid||!e.isValid)return j.invalid("created by diffing an invalid DateTime");let o={locale:this.locale,numberingSystem:this.numberingSystem,...r},i=TC(n).map(j.normalizeUnit),s=e.valueOf()>this.valueOf(),a=s?this:e,l=s?e:this,u=Ox(a,l,i,o);return s?u.negate():u}diffNow(e="milliseconds",n={}){return this.diff(q.now(),e,n)}until(e){return this.isValid?Ce.fromDateTimes(this,e):this}hasSame(e,n){if(!this.isValid)return!1;let r=e.valueOf(),o=this.setZone(e.zone,{keepLocalTime:!0});return o.startOf(n)<=r&&r<=o.endOf(n)}equals(e){return this.isValid&&e.isValid&&this.valueOf()===e.valueOf()&&this.zone.equals(e.zone)&&this.loc.equals(e.loc)}toRelative(e={}){if(!this.isValid)return null;let n=e.base||q.fromObject({},{zone:this.zone}),r=e.padding?this<n?-e.padding:e.padding:0,o=["years","months","days","hours","minutes","seconds"],i=e.unit;return Array.isArray(e.unit)&&(o=e.unit,i=void 0),jy(n,this.plus(r),{...e,numeric:"always",units:o,unit:i})}toRelativeCalendar(e={}){return this.isValid?jy(e.base||q.fromObject({},{zone:this.zone}),this,{...e,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...e){if(!e.every(q.isDateTime))throw new wt("min requires all arguments be DateTimes");return ky(e,n=>n.valueOf(),Math.min)}static max(...e){if(!e.every(q.isDateTime))throw new wt("max requires all arguments be DateTimes");return ky(e,n=>n.valueOf(),Math.max)}static fromFormatExplain(e,n,r={}){let{locale:o=null,numberingSystem:i=null}=r,s=he.fromOpts({locale:o,numberingSystem:i,defaultToEN:!0});return Pg(s,e,n)}static fromStringExplain(e,n,r={}){return q.fromFormatExplain(e,n,r)}static get DATE_SHORT(){return ql}static get DATE_MED(){return Qy}static get DATE_MED_WITH_WEEKDAY(){return lC}static get DATE_FULL(){return Jy}static get DATE_HUGE(){return Xy}static get TIME_SIMPLE(){return eg}static get TIME_WITH_SECONDS(){return tg}static get TIME_WITH_SHORT_OFFSET(){return ng}static get TIME_WITH_LONG_OFFSET(){return rg}static get TIME_24_SIMPLE(){return og}static get TIME_24_WITH_SECONDS(){return ig}static get TIME_24_WITH_SHORT_OFFSET(){return sg}static get TIME_24_WITH_LONG_OFFSET(){return ag}static get DATETIME_SHORT(){return lg}static get DATETIME_SHORT_WITH_SECONDS(){return ug}static get DATETIME_MED(){return cg}static get DATETIME_MED_WITH_SECONDS(){return fg}static get DATETIME_MED_WITH_WEEKDAY(){return uC}static get DATETIME_FULL(){return dg}static get DATETIME_FULL_WITH_SECONDS(){return mg}static get DATETIME_HUGE(){return pg}static get DATETIME_HUGE_WITH_SECONDS(){return hg}};function Cs(t){if(q.isDateTime(t))return t;if(t&&t.valueOf&&yo(t.valueOf()))return q.fromJSDate(t);if(t&&typeof t=="object")return q.fromObject(t);throw new wt(`Unknown datetime argument: ${t}, of type ${typeof t}`)}var ad={renderNullAs:"\\-",taskCompletionTracking:!1,taskCompletionUseEmojiShorthand:!1,taskCompletionText:"completion",taskCompletionDateFormat:"yyyy-MM-dd",recursiveSubTaskCompletion:!1,warnOnEmptyResult:!0,refreshEnabled:!0,refreshInterval:2500,defaultDateFormat:"MMMM dd, yyyy",defaultDateTimeFormat:"h:mm a - MMMM dd, yyyy",maxRecursiveRenderDepth:4,tableIdColumnName:"File",tableGroupColumnName:"Group",showResultCount:!0},Qx={allowHtml:!0};({...ad,...Qx});var ks=class{constructor(e){this.value=e,this.successful=!0}map(e){return new ks(e(this.value))}flatMap(e){return e(this.value)}mapErr(e){return this}bimap(e,n){return this.map(e)}orElse(e){return this.value}cast(){return this}orElseThrow(e){return this.value}},Os=class{constructor(e){this.error=e,this.successful=!1}map(e){return this}flatMap(e){return this}mapErr(e){return new Os(e(this.error))}bimap(e,n){return this.mapErr(n)}orElse(e){return e}cast(){return this}orElseThrow(e){throw e?new Error(e(this.error)):new Error(""+this.error)}},Xl;(function(t){function e(i){return new ks(i)}t.success=e;function n(i){return new Os(i)}t.failure=n;function r(i,s,a){return i.successful?s.successful?a(i.value,s.value):n(s.error):n(i.error)}t.flatMap2=r;function o(i,s,a){return r(i,s,(l,u)=>e(a(l,u)))}t.map2=o})(Xl||(Xl={}));var Jx=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{},eu={exports:{}};eu.exports;(function(t,e){(function(n,r){t.exports=r()})(typeof self!="undefined"?self:Jx,function(){return function(n){var r={};function o(i){if(r[i])return r[i].exports;var s=r[i]={i,l:!1,exports:{}};return n[i].call(s.exports,s,s.exports,o),s.l=!0,s.exports}return o.m=n,o.c=r,o.d=function(i,s,a){o.o(i,s)||Object.defineProperty(i,s,{configurable:!1,enumerable:!0,get:a})},o.r=function(i){Object.defineProperty(i,"__esModule",{value:!0})},o.n=function(i){var s=i&&i.__esModule?function(){return i.default}:function(){return i};return o.d(s,"a",s),s},o.o=function(i,s){return Object.prototype.hasOwnProperty.call(i,s)},o.p="",o(o.s=0)}([function(n,r,o){function i(g){if(!(this instanceof i))return new i(g);this._=g}var s=i.prototype;function a(g,C){for(var F=0;F<g;F++)C(F)}function l(g,C,F){return function(R,A){a(A.length,function(W){R(A[W],W,A)})}(function(R,A,W){C=g(C,R,A,W)},F),C}function u(g,C){return l(function(F,R,A,W){return F.concat([g(R,A,W)])},[],C)}function d(g,C){var F={v:0,buf:C};return a(g,function(){var R;F={v:F.v<<1|(R=F.buf,R[0]>>7),buf:function(A){var W=l(function(H,ne,ge,mt){return H.concat(ge===mt.length-1?Buffer.from([ne,0]).readUInt16BE(0):mt.readUInt16BE(ge))},[],A);return Buffer.from(u(function(H){return(H<<1&65535)>>8},W))}(F.buf)}}),F}function f(){return typeof Buffer!="undefined"}function m(){if(!f())throw new Error("Buffer global does not exist; please use webpack if you need to parse Buffers in the browser.")}function c(g){m();var C=l(function(W,H){return W+H},0,g);if(C%8!=0)throw new Error("The bits ["+g.join(", ")+"] add up to "+C+" which is not an even number of bytes; the total should be divisible by 8");var F,R=C/8,A=(F=function(W){return W>48},l(function(W,H){return W||(F(H)?H:W)},null,g));if(A)throw new Error(A+" bit range requested exceeds 48 bit (6 byte) Number max.");return new i(function(W,H){var ne=R+H;return ne>W.length?k(H,R.toString()+" bytes"):_(ne,l(function(ge,mt){var nt=d(mt,ge.buf);return{coll:ge.coll.concat(nt.v),buf:nt.buf}},{coll:[],buf:W.slice(H,ne)},g).coll)})}function y(g,C){return new i(function(F,R){return m(),R+C>F.length?k(R,C+" bytes for "+g):_(R+C,F.slice(R,R+C))})}function v(g,C){if(typeof(F=C)!="number"||Math.floor(F)!==F||C<0||C>6)throw new Error(g+" requires integer length in range [0, 6].");var F}function S(g){return v("uintBE",g),y("uintBE("+g+")",g).map(function(C){return C.readUIntBE(0,g)})}function h(g){return v("uintLE",g),y("uintLE("+g+")",g).map(function(C){return C.readUIntLE(0,g)})}function p(g){return v("intBE",g),y("intBE("+g+")",g).map(function(C){return C.readIntBE(0,g)})}function E(g){return v("intLE",g),y("intLE("+g+")",g).map(function(C){return C.readIntLE(0,g)})}function D(g){return g instanceof i}function x(g){return{}.toString.call(g)==="[object Array]"}function O(g){return f()&&Buffer.isBuffer(g)}function _(g,C){return{status:!0,index:g,value:C,furthest:-1,expected:[]}}function k(g,C){return x(C)||(C=[C]),{status:!1,index:-1,value:null,furthest:g,expected:C}}function L(g,C){if(!C||g.furthest>C.furthest)return g;var F=g.furthest===C.furthest?function(R,A){if(function(){if(i._supportsSet!==void 0)return i._supportsSet;var Jn=typeof Set!="undefined";return i._supportsSet=Jn,Jn}()&&Array.from){for(var W=new Set(R),H=0;H<A.length;H++)W.add(A[H]);var ne=Array.from(W);return ne.sort(),ne}for(var ge={},mt=0;mt<R.length;mt++)ge[R[mt]]=!0;for(var nt=0;nt<A.length;nt++)ge[A[nt]]=!0;var Qn=[];for(var bt in ge)({}).hasOwnProperty.call(ge,bt)&&Qn.push(bt);return Qn.sort(),Qn}(g.expected,C.expected):C.expected;return{status:g.status,index:g.index,value:g.value,furthest:C.furthest,expected:F}}var N={};function ee(g,C){if(O(g))return{offset:C,line:-1,column:-1};g in N||(N[g]={});for(var F=N[g],R=0,A=0,W=0,H=C;H>=0;){if(H in F){R=F[H].line,W===0&&(W=F[H].lineStart);break}(g.charAt(H)===`
`||g.charAt(H)==="\r"&&g.charAt(H+1)!==`
`)&&(A++,W===0&&(W=H+1)),H--}var ne=R+A,ge=C-W;return F[C]={line:ne,lineStart:W},{offset:C,line:ne+1,column:ge+1}}function oe(g){if(!D(g))throw new Error("not a parser: "+g)}function b(g,C){return typeof g=="string"?g.charAt(C):g[C]}function $(g){if(typeof g!="number")throw new Error("not a number: "+g)}function T(g){if(typeof g!="function")throw new Error("not a function: "+g)}function P(g){if(typeof g!="string")throw new Error("not a string: "+g)}var Y=2,Z=3,Ue=8,Cr=5*Ue,bn=4*Ue,so="  ";function tt(g,C){return new Array(C+1).join(g)}function Te(g,C,F){var R=C-g.length;return R<=0?g:tt(F,R)+g}function an(g,C,F,R){return{from:g-C>0?g-C:0,to:g+F>R?R:g+F}}function ao(g,C){var F,R,A,W,H,ne=C.index,ge=ne.offset,mt=1;if(ge===g.length)return"Got the end of the input";if(O(g)){var nt=ge-ge%Ue,Qn=ge-nt,bt=an(nt,Cr,bn+Ue,g.length),Jn=u(function(je){return u(function(Yo){return Te(Yo.toString(16),2,"0")},je)},function(je,Yo){var Zo=je.length,co=[],qo=0;if(Zo<=Yo)return[je.slice()];for(var Ko=0;Ko<Zo;Ko++)co[qo]||co.push([]),co[qo].push(je[Ko]),(Ko+1)%Yo==0&&qo++;return co}(g.slice(bt.from,bt.to).toJSON().data,Ue));W=function(je){return je.from===0&&je.to===1?{from:je.from,to:je.to}:{from:je.from/Ue,to:Math.floor(je.to/Ue)}}(bt),R=nt/Ue,F=3*Qn,Qn>=4&&(F+=1),mt=2,A=u(function(je){return je.length<=4?je.join(" "):je.slice(0,4).join(" ")+"  "+je.slice(4).join(" ")},Jn),(H=(8*(W.to>0?W.to-1:W.to)).toString(16).length)<2&&(H=2)}else{var Go=g.split(/\r\n|[\n\r\u2028\u2029]/);F=ne.column-1,R=ne.line-1,W=an(R,Y,Z,Go.length),A=Go.slice(W.from,W.to),H=W.to.toString().length}var QS=R-W.from;return O(g)&&(H=(8*(W.to>0?W.to-1:W.to)).toString(16).length)<2&&(H=2),l(function(je,Yo,Zo){var co,qo=Zo===QS,Ko=qo?"> ":so;return co=O(g)?Te((8*(W.from+Zo)).toString(16),H,"0"):Te((W.from+Zo+1).toString(),H," "),[].concat(je,[Ko+co+" | "+Yo],qo?[so+tt(" ",H)+" | "+Te("",F," ")+tt("^",mt)]:[])},[],A).join(`
`)}function Sf(g,C){return[`
`,"-- PARSING FAILED "+tt("-",50),`

`,ao(g,C),`

`,(F=C.expected,F.length===1?`Expected:

`+F[0]:`Expected one of the following: 

`+F.join(", ")),`
`].join("");var F}function Jh(g){return g.flags!==void 0?g.flags:[g.global?"g":"",g.ignoreCase?"i":"",g.multiline?"m":"",g.unicode?"u":"",g.sticky?"y":""].join("")}function wl(){for(var g=[].slice.call(arguments),C=g.length,F=0;F<C;F+=1)oe(g[F]);return i(function(R,A){for(var W,H=new Array(C),ne=0;ne<C;ne+=1){if(!(W=L(g[ne]._(R,A),W)).status)return W;H[ne]=W.value,A=W.index}return L(_(A,H),W)})}function lo(){var g=[].slice.call(arguments);if(g.length===0)throw new Error("seqMap needs at least one argument");var C=g.pop();return T(C),wl.apply(null,g).map(function(F){return C.apply(null,F)})}function Sl(){var g=[].slice.call(arguments),C=g.length;if(C===0)return Cl("zero alternates");for(var F=0;F<C;F+=1)oe(g[F]);return i(function(R,A){for(var W,H=0;H<g.length;H+=1)if((W=L(g[H]._(R,A),W)).status)return W;return W})}function Xh(g,C){return Cf(g,C).or(uo([]))}function Cf(g,C){return oe(g),oe(C),lo(g,C.then(g).many(),function(F,R){return[F].concat(R)})}function vs(g){P(g);var C="'"+g+"'";return i(function(F,R){var A=R+g.length,W=F.slice(R,A);return W===g?_(A,W):k(R,C)})}function Kn(g,C){(function(A){if(!(A instanceof RegExp))throw new Error("not a regexp: "+A);for(var W=Jh(A),H=0;H<W.length;H++){var ne=W.charAt(H);if(ne!=="i"&&ne!=="m"&&ne!=="u"&&ne!=="s")throw new Error('unsupported regexp flag "'+ne+'": '+A)}})(g),arguments.length>=2?$(C):C=0;var F=function(A){return RegExp("^(?:"+A.source+")",Jh(A))}(g),R=""+g;return i(function(A,W){var H=F.exec(A.slice(W));if(H){if(0<=C&&C<=H.length){var ne=H[0],ge=H[C];return _(W+ne.length,ge)}return k(W,"valid match group (0 to "+H.length+") in "+R)}return k(W,R)})}function uo(g){return i(function(C,F){return _(F,g)})}function Cl(g){return i(function(C,F){return k(F,g)})}function xl(g){if(D(g))return i(function(C,F){var R=g._(C,F);return R.index=F,R.value="",R});if(typeof g=="string")return xl(vs(g));if(g instanceof RegExp)return xl(Kn(g));throw new Error("not a string, regexp, or parser: "+g)}function ey(g){return oe(g),i(function(C,F){var R=g._(C,F),A=C.slice(F,R.index);return R.status?k(F,'not "'+A+'"'):_(F,null)})}function Tl(g){return T(g),i(function(C,F){var R=b(C,F);return F<C.length&&g(R)?_(F+1,R):k(F,"a character/byte matching "+g)})}function ty(g,C){arguments.length<2&&(C=g,g=void 0);var F=i(function(R,A){return F._=C()._,F._(R,A)});return g?F.desc(g):F}function xf(){return Cl("fantasy-land/empty")}s.parse=function(g){if(typeof g!="string"&&!O(g))throw new Error(".parse must be called with a string or Buffer as its argument");var C,F=this.skip(Tf)._(g,0);return C=F.status?{status:!0,value:F.value}:{status:!1,index:ee(g,F.furthest),expected:F.expected},delete N[g],C},s.tryParse=function(g){var C=this.parse(g);if(C.status)return C.value;var F=Sf(g,C),R=new Error(F);throw R.type="ParsimmonError",R.result=C,R},s.assert=function(g,C){return this.chain(function(F){return g(F)?uo(F):Cl(C)})},s.or=function(g){return Sl(this,g)},s.trim=function(g){return this.wrap(g,g)},s.wrap=function(g,C){return lo(g,this,C,function(F,R){return R})},s.thru=function(g){return g(this)},s.then=function(g){return oe(g),wl(this,g).map(function(C){return C[1]})},s.many=function(){var g=this;return i(function(C,F){for(var R=[],A=void 0;;){if(!(A=L(g._(C,F),A)).status)return L(_(F,R),A);if(F===A.index)throw new Error("infinite loop detected in .many() parser --- calling .many() on a parser which can accept zero characters is usually the cause");F=A.index,R.push(A.value)}})},s.tieWith=function(g){return P(g),this.map(function(C){if(function(A){if(!x(A))throw new Error("not an array: "+A)}(C),C.length){P(C[0]);for(var F=C[0],R=1;R<C.length;R++)P(C[R]),F+=g+C[R];return F}return""})},s.tie=function(){return this.tieWith("")},s.times=function(g,C){var F=this;return arguments.length<2&&(C=g),$(g),$(C),i(function(R,A){for(var W=[],H=void 0,ne=void 0,ge=0;ge<g;ge+=1){if(ne=L(H=F._(R,A),ne),!H.status)return ne;A=H.index,W.push(H.value)}for(;ge<C&&(ne=L(H=F._(R,A),ne),H.status);ge+=1)A=H.index,W.push(H.value);return L(_(A,W),ne)})},s.result=function(g){return this.map(function(){return g})},s.atMost=function(g){return this.times(0,g)},s.atLeast=function(g){return lo(this.times(g),this.many(),function(C,F){return C.concat(F)})},s.map=function(g){T(g);var C=this;return i(function(F,R){var A=C._(F,R);return A.status?L(_(A.index,g(A.value)),A):A})},s.contramap=function(g){T(g);var C=this;return i(function(F,R){var A=C.parse(g(F.slice(R)));return A.status?_(R+F.length,A.value):A})},s.promap=function(g,C){return T(g),T(C),this.contramap(g).map(C)},s.skip=function(g){return wl(this,g).map(function(C){return C[0]})},s.mark=function(){return lo(Es,this,Es,function(g,C,F){return{start:g,value:C,end:F}})},s.node=function(g){return lo(Es,this,Es,function(C,F,R){return{name:g,value:F,start:C,end:R}})},s.sepBy=function(g){return Xh(this,g)},s.sepBy1=function(g){return Cf(this,g)},s.lookahead=function(g){return this.skip(xl(g))},s.notFollowedBy=function(g){return this.skip(ey(g))},s.desc=function(g){x(g)||(g=[g]);var C=this;return i(function(F,R){var A=C._(F,R);return A.status||(A.expected=g),A})},s.fallback=function(g){return this.or(uo(g))},s.ap=function(g){return lo(g,this,function(C,F){return C(F)})},s.chain=function(g){var C=this;return i(function(F,R){var A=C._(F,R);return A.status?L(g(A.value)._(F,A.index),A):A})},s.concat=s.or,s.empty=xf,s.of=uo,s["fantasy-land/ap"]=s.ap,s["fantasy-land/chain"]=s.chain,s["fantasy-land/concat"]=s.concat,s["fantasy-land/empty"]=s.empty,s["fantasy-land/of"]=s.of,s["fantasy-land/map"]=s.map;var Es=i(function(g,C){return _(C,ee(g,C))}),zS=i(function(g,C){return C>=g.length?k(C,"any character/byte"):_(C+1,b(g,C))}),US=i(function(g,C){return _(g.length,g.slice(C))}),Tf=i(function(g,C){return C<g.length?k(C,"EOF"):_(C,null)}),jS=Kn(/[0-9]/).desc("a digit"),$S=Kn(/[0-9]*/).desc("optional digits"),GS=Kn(/[a-z]/i).desc("a letter"),YS=Kn(/[a-z]*/i).desc("optional letters"),ZS=Kn(/\s*/).desc("optional whitespace"),qS=Kn(/\s+/).desc("whitespace"),ny=vs("\r"),ry=vs(`
`),oy=vs(`\r
`),iy=Sl(oy,ry,ny).desc("newline"),KS=Sl(iy,Tf);i.all=US,i.alt=Sl,i.any=zS,i.cr=ny,i.createLanguage=function(g){var C={};for(var F in g)({}).hasOwnProperty.call(g,F)&&function(R){C[R]=ty(function(){return g[R](C)})}(F);return C},i.crlf=oy,i.custom=function(g){return i(g(_,k))},i.digit=jS,i.digits=$S,i.empty=xf,i.end=KS,i.eof=Tf,i.fail=Cl,i.formatError=Sf,i.index=Es,i.isParser=D,i.lazy=ty,i.letter=GS,i.letters=YS,i.lf=ry,i.lookahead=xl,i.makeFailure=k,i.makeSuccess=_,i.newline=iy,i.noneOf=function(g){return Tl(function(C){return g.indexOf(C)<0}).desc("none of '"+g+"'")},i.notFollowedBy=ey,i.of=uo,i.oneOf=function(g){for(var C=g.split(""),F=0;F<C.length;F++)C[F]="'"+C[F]+"'";return Tl(function(R){return g.indexOf(R)>=0}).desc(C)},i.optWhitespace=ZS,i.Parser=i,i.range=function(g,C){return Tl(function(F){return g<=F&&F<=C}).desc(g+"-"+C)},i.regex=Kn,i.regexp=Kn,i.sepBy=Xh,i.sepBy1=Cf,i.seq=wl,i.seqMap=lo,i.seqObj=function(){for(var g,C={},F=0,R=(g=arguments,Array.prototype.slice.call(g)),A=R.length,W=0;W<A;W+=1){var H=R[W];if(!D(H)){if(x(H)&&H.length===2&&typeof H[0]=="string"&&D(H[1])){var ne=H[0];if(Object.prototype.hasOwnProperty.call(C,ne))throw new Error("seqObj: duplicate key "+ne);C[ne]=!0,F++;continue}throw new Error("seqObj arguments must be parsers or [string, parser] array pairs.")}}if(F===0)throw new Error("seqObj expects at least one named parser, found zero");return i(function(ge,mt){for(var nt,Qn={},bt=0;bt<A;bt+=1){var Jn,Go;if(x(R[bt])?(Jn=R[bt][0],Go=R[bt][1]):(Jn=null,Go=R[bt]),!(nt=L(Go._(ge,mt),nt)).status)return nt;Jn&&(Qn[Jn]=nt.value),mt=nt.index}return L(_(mt,Qn),nt)})},i.string=vs,i.succeed=uo,i.takeWhile=function(g){return T(g),i(function(C,F){for(var R=F;R<C.length&&g(b(C,R));)R++;return _(R,C.slice(F,R))})},i.test=Tl,i.whitespace=qS,i["fantasy-land/empty"]=xf,i["fantasy-land/of"]=uo,i.Binary={bitSeq:c,bitSeqObj:function(g){m();var C={},F=0,R=u(function(W){if(x(W)){var H=W;if(H.length!==2)throw new Error("["+H.join(", ")+"] should be length 2, got length "+H.length);if(P(H[0]),$(H[1]),Object.prototype.hasOwnProperty.call(C,H[0]))throw new Error("duplicate key in bitSeqObj: "+H[0]);return C[H[0]]=!0,F++,H}return $(W),[null,W]},g);if(F<1)throw new Error("bitSeqObj expects at least one named pair, got ["+g.join(", ")+"]");var A=u(function(W){return W[0]},R);return c(u(function(W){return W[1]},R)).map(function(W){return l(function(H,ne){return ne[0]!==null&&(H[ne[0]]=ne[1]),H},{},u(function(H,ne){return[H,W[ne]]},A))})},byte:function(g){if(m(),$(g),g>255)throw new Error("Value specified to byte constructor ("+g+"=0x"+g.toString(16)+") is larger in value than a single byte.");var C=(g>15?"0x":"0x0")+g.toString(16);return i(function(F,R){var A=b(F,R);return A===g?_(R+1,A):k(R,C)})},buffer:function(g){return y("buffer",g).map(function(C){return Buffer.from(C)})},encodedString:function(g,C){return y("string",C).map(function(F){return F.toString(g)})},uintBE:S,uint8BE:S(1),uint16BE:S(2),uint32BE:S(4),uintLE:h,uint8LE:h(1),uint16LE:h(2),uint32LE:h(4),intBE:p,int8BE:p(1),int16BE:p(2),int32BE:p(4),intLE:E,int8LE:E(1),int16LE:E(2),int32LE:E(4),floatBE:y("floatBE",4).map(function(g){return g.readFloatBE(0)}),floatLE:y("floatLE",4).map(function(g){return g.readFloatLE(0)}),doubleBE:y("doubleBE",8).map(function(g){return g.readDoubleBE(0)}),doubleLE:y("doubleLE",8).map(function(g){return g.readDoubleLE(0)})},n.exports=i}])})})(eu,eu.exports);var w=eu.exports,ld=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26D3\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26F9(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC3\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF5\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC08\uDC26](?:\u200D\u2B1B)?|[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4-\uDEB6](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E[\uDDAF-\uDDB3\uDDBC\uDDBD])|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uFE0F|\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD-\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC2\uDECE-\uDEDB\uDEE0-\uDEE8]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF-\uDDB3\uDDBC\uDDBD]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g;function qg(t){return t==null?t:t.shiftToAll().normalize()}function Gy(t){return t.includes("/")&&(t=t.substring(t.lastIndexOf("/")+1)),t.endsWith(".md")&&(t=t.substring(0,t.length-3)),t}w.alt(w.regex(new RegExp(ld(),"")),w.regex(/[0-9\p{Letter}_-]+/u).map(t=>t.toLocaleLowerCase()),w.whitespace.map(t=>"-"),w.any.map(t=>"")).many().map(t=>t.join(""));var Xx=w.alt(w.regex(new RegExp(ld(),"")),w.regex(/[0-9\p{Letter}_-]+/u),w.whitespace.map(t=>" "),w.any.map(t=>" ")).many().map(t=>t.join("").split(/\s+/).join(" ").trim());function eT(t){return Xx.tryParse(t)}function tT(t){return t=qg(t),t=j.fromObject(Object.fromEntries(Object.entries(t.toObject()).filter(([,e])=>e!=0))),t.toHuman()}var bs;(function(t){function e(D,x=ad,O=!1){let _=n(D);if(!_)return x.renderNullAs;switch(_.type){case"null":return x.renderNullAs;case"string":return _.value;case"number":case"boolean":return""+_.value;case"html":return _.value.outerHTML;case"widget":return _.value.markdown();case"link":return _.value.markdown();case"function":return"<function>";case"array":let k="";return O&&(k+="["),k+=_.value.map(L=>e(L,x,!0)).join(", "),O&&(k+="]"),k;case"object":return"{ "+Object.entries(_.value).map(L=>L[0]+": "+e(L[1],x,!0)).join(", ")+" }";case"date":return _.value.second==0&&_.value.hour==0&&_.value.minute==0?_.value.toFormat(x.defaultDateFormat):_.value.toFormat(x.defaultDateTimeFormat);case"duration":return tT(_.value)}}t.toString=e;function n(D){return m(D)?{type:"null",value:D}:u(D)?{type:"number",value:D}:l(D)?{type:"string",value:D}:y(D)?{type:"boolean",value:D}:f(D)?{type:"duration",value:D}:d(D)?{type:"date",value:D}:S(D)?{type:"widget",value:D}:c(D)?{type:"array",value:D}:v(D)?{type:"link",value:D}:E(D)?{type:"function",value:D}:h(D)?{type:"html",value:D}:p(D)?{type:"object",value:D}:void 0}t.wrapValue=n;function r(D,x){if(p(D)){let O={};for(let[_,k]of Object.entries(D))O[_]=r(k,x);return O}else if(c(D)){let O=[];for(let _ of D)O.push(r(_,x));return O}else return x(D)}t.mapLeaves=r;function o(D,x,O){var _,k;if(D===void 0&&(D=null),x===void 0&&(x=null),D===null&&x===null)return 0;if(D===null)return-1;if(x===null)return 1;let L=n(D),N=n(x);if(L===void 0&&N===void 0)return 0;if(L===void 0)return-1;if(N===void 0)return 1;if(L.type!=N.type)return L.type.localeCompare(N.type);if(L.value===N.value)return 0;switch(L.type){case"string":return L.value.localeCompare(N.value);case"number":return L.value<N.value?-1:L.value==N.value?0:1;case"null":return 0;case"boolean":return L.value==N.value?0:L.value?1:-1;case"link":let ee=L.value,oe=N.value,b=O!=null?O:tt=>tt,$=b(ee.path).localeCompare(b(oe.path));if($!=0)return $;let T=ee.type.localeCompare(oe.type);return T!=0?T:ee.subpath&&!oe.subpath?1:!ee.subpath&&oe.subpath?-1:!ee.subpath&&!oe.subpath?0:((_=ee.subpath)!==null&&_!==void 0?_:"").localeCompare((k=oe.subpath)!==null&&k!==void 0?k:"");case"date":return L.value<N.value?-1:L.value.equals(N.value)?0:1;case"duration":return L.value<N.value?-1:L.value.equals(N.value)?0:1;case"array":let P=L.value,Y=N.value;for(let tt=0;tt<Math.min(P.length,Y.length);tt++){let Te=o(P[tt],Y[tt]);if(Te!=0)return Te}return P.length-Y.length;case"object":let Z=L.value,Ue=N.value,Cr=Array.from(Object.keys(Z)),bn=Array.from(Object.keys(Ue));Cr.sort(),bn.sort();let so=o(Cr,bn);if(so!=0)return so;for(let tt of Cr){let Te=o(Z[tt],Ue[tt]);if(Te!=0)return Te}return 0;case"widget":case"html":case"function":return 0}}t.compareValue=o;function i(D){var x;return(x=n(D))===null||x===void 0?void 0:x.type}t.typeOf=i;function s(D){let x=n(D);if(!x)return!1;switch(x.type){case"number":return x.value!=0;case"string":return x.value.length>0;case"boolean":return x.value;case"link":return!!x.value.path;case"date":return x.value.toMillis()!=0;case"duration":return x.value.as("seconds")!=0;case"object":return Object.keys(x.value).length>0;case"array":return x.value.length>0;case"null":return!1;case"html":case"widget":case"function":return!0}}t.isTruthy=s;function a(D){if(D==null)return D;if(t.isArray(D))return[].concat(D.map(x=>a(x)));if(t.isObject(D)){let x={};for(let[O,_]of Object.entries(D))x[O]=a(_);return x}else return D}t.deepCopy=a;function l(D){return typeof D=="string"}t.isString=l;function u(D){return typeof D=="number"}t.isNumber=u;function d(D){return D instanceof q}t.isDate=d;function f(D){return D instanceof j}t.isDuration=f;function m(D){return D==null}t.isNull=m;function c(D){return Array.isArray(D)}t.isArray=c;function y(D){return typeof D=="boolean"}t.isBoolean=y;function v(D){return D instanceof qe}t.isLink=v;function S(D){return D instanceof Rs}t.isWidget=S;function h(D){return typeof HTMLElement!="undefined"?D instanceof HTMLElement:!1}t.isHtml=h;function p(D){return typeof D=="object"&&!h(D)&&!S(D)&&!c(D)&&!f(D)&&!d(D)&&!v(D)&&D!==void 0&&!m(D)}t.isObject=p;function E(D){return typeof D=="function"}t.isFunction=E})(bs||(bs={}));var Yy;(function(t){function e(o){return bs.isObject(o)&&Object.keys(o).length==2&&"key"in o&&"rows"in o}t.isElementGroup=e;function n(o){for(let i of o)if(!e(i))return!1;return!0}t.isGrouping=n;function r(o){if(n(o)){let i=0;for(let s of o)i+=r(s.rows);return i}else return o.length}t.count=r})(Yy||(Yy={}));var qe=class{static file(e,n=!1,r){return new qe({path:e,embed:n,display:r,subpath:void 0,type:"file"})}static infer(e,n=!1,r){if(e.includes("#^")){let o=e.split("#^");return qe.block(o[0],o[1],n,r)}else if(e.includes("#")){let o=e.split("#");return qe.header(o[0],o[1],n,r)}else return qe.file(e,n,r)}static header(e,n,r,o){return new qe({path:e,embed:r,display:o,subpath:eT(n),type:"header"})}static block(e,n,r,o){return new qe({path:e,embed:r,display:o,subpath:n,type:"block"})}static fromObject(e){return new qe(e)}constructor(e){Object.assign(this,e)}equals(e){return e==null||e==null?!1:this.path==e.path&&this.type==e.type&&this.subpath==e.subpath}toString(){return this.markdown()}toObject(){return{path:this.path,type:this.type,subpath:this.subpath,display:this.display,embed:this.embed}}withPath(e){return new qe(Object.assign({},this,{path:e}))}withDisplay(e){return new qe(Object.assign({},this,{display:e}))}withHeader(e){return qe.header(this.path,e,this.embed,this.display)}toFile(){return qe.file(this.path,this.embed,this.display)}toEmbed(){if(this.embed)return this;{let e=new qe(this);return e.embed=!0,e}}fromEmbed(){if(this.embed){let e=new qe(this);return e.embed=!1,e}else return this}markdown(){let e=(this.embed?"!":"")+"[["+this.obsidianLink();return this.display?e+="|"+this.display:(e+="|"+Gy(this.path),(this.type=="header"||this.type=="block")&&(e+=" > "+this.subpath)),e+="]]",e}obsidianLink(){var e,n;let r=this.path.replace("|","\\|");return this.type=="header"?r+"#"+((e=this.subpath)===null||e===void 0?void 0:e.replace("|","\\|")):this.type=="block"?r+"#^"+((n=this.subpath)===null||n===void 0?void 0:n.replace("|","\\|")):r}fileName(){return Gy(this.path).replace(".md","")}},Rs=class{constructor(e){this.$widget=e}},Kf=class extends Rs{constructor(e,n){super("dataview:list-pair"),this.key=e,this.value=n}markdown(){return`${bs.toString(this.key)}: ${bs.toString(this.value)}`}},Qf=class extends Rs{constructor(e,n){super("dataview:external-link"),this.url=e,this.display=n}markdown(){var e;return`[${(e=this.display)!==null&&e!==void 0?e:this.url}](${this.url})`}},Zy;(function(t){function e(s,a){return new Kf(s,a)}t.listPair=e;function n(s,a){return new Qf(s,a)}t.externalLink=n;function r(s){return s.$widget==="dataview:list-pair"}t.isListPair=r;function o(s){return s.$widget==="dataview:external-link"}t.isExternalLink=o;function i(s){return r(s)||o(s)}t.isBuiltin=i})(Zy||(Zy={}));var Me;(function(t){function e(m){return{type:"variable",name:m}}t.variable=e;function n(m){return{type:"literal",value:m}}t.literal=n;function r(m,c,y){return{type:"binaryop",left:m,op:c,right:y}}t.binaryOp=r;function o(m,c){return{type:"index",object:m,index:c}}t.index=o;function i(m){let c=m.split("."),y=t.variable(c[0]);for(let v=1;v<c.length;v++)y=t.index(y,t.literal(c[v]));return y}t.indexVariable=i;function s(m,c){return{type:"lambda",arguments:m,value:c}}t.lambda=s;function a(m,c){return{type:"function",func:m,arguments:c}}t.func=a;function l(m){return{type:"list",values:m}}t.list=l;function u(m){return{type:"object",values:m}}t.object=u;function d(m){return{type:"negated",child:m}}t.negate=d;function f(m){return m=="<="||m=="<"||m==">"||m==">="||m=="!="||m=="="}t.isCompareOp=f,t.NULL=t.literal(null)})(Me||(Me={}));var In;(function(t){function e(d){return{type:"tag",tag:d}}t.tag=e;function n(d){return{type:"csv",path:d}}t.csv=n;function r(d){return{type:"folder",folder:d}}t.folder=r;function o(d,f){return{type:"link",file:d,direction:f?"incoming":"outgoing"}}t.link=o;function i(d,f,m){return{type:"binaryop",left:d,op:f,right:m}}t.binaryOp=i;function s(d,f){return{type:"binaryop",left:d,op:"&",right:f}}t.and=s;function a(d,f){return{type:"binaryop",left:d,op:"|",right:f}}t.or=a;function l(d){return{type:"negate",child:d}}t.negate=l;function u(){return{type:"empty"}}t.empty=u})(In||(In={}));var qy=new RegExp(ld(),""),Jf={year:j.fromObject({years:1}),years:j.fromObject({years:1}),yr:j.fromObject({years:1}),yrs:j.fromObject({years:1}),month:j.fromObject({months:1}),months:j.fromObject({months:1}),mo:j.fromObject({months:1}),mos:j.fromObject({months:1}),week:j.fromObject({weeks:1}),weeks:j.fromObject({weeks:1}),wk:j.fromObject({weeks:1}),wks:j.fromObject({weeks:1}),w:j.fromObject({weeks:1}),day:j.fromObject({days:1}),days:j.fromObject({days:1}),d:j.fromObject({days:1}),hour:j.fromObject({hours:1}),hours:j.fromObject({hours:1}),hr:j.fromObject({hours:1}),hrs:j.fromObject({hours:1}),h:j.fromObject({hours:1}),minute:j.fromObject({minutes:1}),minutes:j.fromObject({minutes:1}),min:j.fromObject({minutes:1}),mins:j.fromObject({minutes:1}),m:j.fromObject({minutes:1}),second:j.fromObject({seconds:1}),seconds:j.fromObject({seconds:1}),sec:j.fromObject({seconds:1}),secs:j.fromObject({seconds:1}),s:j.fromObject({seconds:1})},Xf={now:()=>q.local(),today:()=>q.local().startOf("day"),yesterday:()=>q.local().startOf("day").minus(j.fromObject({days:1})),tomorrow:()=>q.local().startOf("day").plus(j.fromObject({days:1})),sow:()=>q.local().startOf("week"),"start-of-week":()=>q.local().startOf("week"),eow:()=>q.local().endOf("week"),"end-of-week":()=>q.local().endOf("week"),soy:()=>q.local().startOf("year"),"start-of-year":()=>q.local().startOf("year"),eoy:()=>q.local().endOf("year"),"end-of-year":()=>q.local().endOf("year"),som:()=>q.local().startOf("month"),"start-of-month":()=>q.local().startOf("month"),eom:()=>q.local().endOf("month"),"end-of-month":()=>q.local().endOf("month")},ed=["FROM","WHERE","LIMIT","GROUP","FLATTEN"];function nT(t){let e=-1;for(;(e=t.indexOf("|",e+1))>=0;)if(!(e>0&&t[e-1]=="\\"))return[t.substring(0,e).replace(/\\\|/g,"|"),t.substring(e+1)];return[t.replace(/\\\|/g,"|"),void 0]}function rT(t){let[e,n]=nT(t);return qe.infer(e,!1,n)}function xs(t,e,n){return w.seqMap(t,w.seq(w.optWhitespace,e,w.optWhitespace,t).many(),(r,o)=>{if(o.length==0)return r;let i=n(r,o[0][1],o[0][3]);for(let s=1;s<o.length;s++)i=n(i,o[s][1],o[s][3]);return i})}function oT(t,...e){return w.custom((n,r)=>(o,i)=>{let s=t._(o,i);if(!s.status)return s;for(let a of e){let l=a(s.value)._(o,s.index);if(!l.status)return s;s=l}return s})}var fn=w.createLanguage({number:t=>w.regexp(/-?[0-9]+(\.[0-9]+)?/).map(e=>Number.parseFloat(e)).desc("number"),string:t=>w.string('"').then(w.alt(t.escapeCharacter,w.noneOf('"\\')).atLeast(0).map(e=>e.join(""))).skip(w.string('"')).desc("string"),escapeCharacter:t=>w.string("\\").then(w.any).map(e=>e==='"'?'"':e==="\\"?"\\":"\\"+e),bool:t=>w.regexp(/true|false|True|False/).map(e=>e.toLowerCase()=="true").desc("boolean ('true' or 'false')"),tag:t=>w.seqMap(w.string("#"),w.alt(w.regexp(/[^\u2000-\u206F\u2E00-\u2E7F'!"#$%&()*+,.:;<=>?@^`{|}~\[\]\\\s]/).desc("text")).many(),(e,n)=>e+n.join("")).desc("tag ('#hello/stuff')"),identifier:t=>w.seqMap(w.alt(w.regexp(/\p{Letter}/u),w.regexp(qy).desc("text")),w.alt(w.regexp(/[0-9\p{Letter}_-]/u),w.regexp(qy).desc("text")).many(),(e,n)=>e+n.join("")).desc("variable identifier"),link:t=>w.regexp(/\[\[([^\[\]]*?)\]\]/u,1).map(e=>rT(e)).desc("file link"),embedLink:t=>w.seqMap(w.string("!").atMost(1),t.link,(e,n)=>(e.length>0&&(n.embed=!0),n)).desc("file link"),binaryPlusMinus:t=>w.regexp(/\+|-/).map(e=>e).desc("'+' or '-'"),binaryMulDiv:t=>w.regexp(/\*|\/|%/).map(e=>e).desc("'*' or '/' or '%'"),binaryCompareOp:t=>w.regexp(/>=|<=|!=|>|<|=/).map(e=>e).desc("'>=' or '<=' or '!=' or '=' or '>' or '<'"),binaryBooleanOp:t=>w.regexp(/and|or|&|\|/i).map(e=>e.toLowerCase()=="and"?"&":e.toLowerCase()=="or"?"|":e).desc("'and' or 'or'"),rootDate:t=>w.seqMap(w.regexp(/\d{4}/),w.string("-"),w.regexp(/\d{2}/),(e,n,r)=>q.fromObject({year:Number.parseInt(e),month:Number.parseInt(r)})).desc("date in format YYYY-MM[-DDTHH-MM-SS.MS]"),dateShorthand:t=>w.alt(...Object.keys(Xf).sort((e,n)=>n.length-e.length).map(w.string)),date:t=>oT(t.rootDate,e=>w.seqMap(w.string("-"),w.regexp(/\d{2}/),(n,r)=>e.set({day:Number.parseInt(r)})),e=>w.seqMap(w.string("T"),w.regexp(/\d{2}/),(n,r)=>e.set({hour:Number.parseInt(r)})),e=>w.seqMap(w.string(":"),w.regexp(/\d{2}/),(n,r)=>e.set({minute:Number.parseInt(r)})),e=>w.seqMap(w.string(":"),w.regexp(/\d{2}/),(n,r)=>e.set({second:Number.parseInt(r)})),e=>w.alt(w.seqMap(w.string("."),w.regexp(/\d{3}/),(n,r)=>e.set({millisecond:Number.parseInt(r)})),w.succeed(e)),e=>w.alt(w.seqMap(w.string("+").or(w.string("-")),w.regexp(/\d{1,2}(:\d{2})?/),(n,r)=>e.setZone("UTC"+n+r,{keepLocalTime:!0})),w.seqMap(w.string("Z"),()=>e.setZone("utc",{keepLocalTime:!0})),w.seqMap(w.string("["),w.regexp(/[0-9A-Za-z+-\/]+/u),w.string("]"),(n,r,o)=>e.setZone(r,{keepLocalTime:!0})))).assert(e=>e.isValid,"valid date").desc("date in format YYYY-MM[-DDTHH-MM-SS.MS]"),datePlus:t=>w.alt(t.dateShorthand.map(e=>Xf[e]()),t.date).desc("date in format YYYY-MM[-DDTHH-MM-SS.MS] or in shorthand"),durationType:t=>w.alt(...Object.keys(Jf).sort((e,n)=>n.length-e.length).map(w.string)),duration:t=>w.seqMap(t.number,w.optWhitespace,t.durationType,(e,n,r)=>Jf[r].mapUnits(o=>o*e)).sepBy1(w.string(",").trim(w.optWhitespace).or(w.optWhitespace)).map(e=>e.reduce((n,r)=>n.plus(r))).desc("duration like 4hr2min"),rawNull:t=>w.string("null"),tagSource:t=>t.tag.map(e=>In.tag(e)),csvSource:t=>w.seqMap(w.string("csv(").skip(w.optWhitespace),t.string,w.string(")"),(e,n,r)=>In.csv(n)),linkIncomingSource:t=>t.link.map(e=>In.link(e.path,!0)),linkOutgoingSource:t=>w.seqMap(w.string("outgoing(").skip(w.optWhitespace),t.link,w.string(")"),(e,n,r)=>In.link(n.path,!1)),folderSource:t=>t.string.map(e=>In.folder(e)),parensSource:t=>w.seqMap(w.string("("),w.optWhitespace,t.source,w.optWhitespace,w.string(")"),(e,n,r,o,i)=>r),negateSource:t=>w.seqMap(w.alt(w.string("-"),w.string("!")),t.atomSource,(e,n)=>In.negate(n)),atomSource:t=>w.alt(t.parensSource,t.negateSource,t.linkOutgoingSource,t.linkIncomingSource,t.folderSource,t.tagSource,t.csvSource),binaryOpSource:t=>xs(t.atomSource,t.binaryBooleanOp.map(e=>e),In.binaryOp),source:t=>t.binaryOpSource,variableField:t=>t.identifier.chain(e=>ed.includes(e.toUpperCase())?w.fail("Variable fields cannot be a keyword ("+ed.join(" or ")+")"):w.succeed(Me.variable(e))).desc("variable"),numberField:t=>t.number.map(e=>Me.literal(e)).desc("number"),stringField:t=>t.string.map(e=>Me.literal(e)).desc("string"),boolField:t=>t.bool.map(e=>Me.literal(e)).desc("boolean"),dateField:t=>w.seqMap(w.string("date("),w.optWhitespace,t.datePlus,w.optWhitespace,w.string(")"),(e,n,r,o,i)=>Me.literal(r)).desc("date"),durationField:t=>w.seqMap(w.string("dur("),w.optWhitespace,t.duration,w.optWhitespace,w.string(")"),(e,n,r,o,i)=>Me.literal(r)).desc("duration"),nullField:t=>t.rawNull.map(e=>Me.NULL),linkField:t=>t.link.map(e=>Me.literal(e)),listField:t=>t.field.sepBy(w.string(",").trim(w.optWhitespace)).wrap(w.string("[").skip(w.optWhitespace),w.optWhitespace.then(w.string("]"))).map(e=>Me.list(e)).desc("list ('[1, 2, 3]')"),objectField:t=>w.seqMap(t.identifier.or(t.string),w.string(":").trim(w.optWhitespace),t.field,(e,n,r)=>({name:e,value:r})).sepBy(w.string(",").trim(w.optWhitespace)).wrap(w.string("{").skip(w.optWhitespace),w.optWhitespace.then(w.string("}"))).map(e=>{let n={};for(let r of e)n[r.name]=r.value;return Me.object(n)}).desc("object ('{ a: 1, b: 2 }')"),atomInlineField:t=>w.alt(t.date,t.duration.map(e=>qg(e)),t.string,t.tag,t.embedLink,t.bool,t.number,t.rawNull),inlineFieldList:t=>t.atomInlineField.sepBy(w.string(",").trim(w.optWhitespace).lookahead(t.atomInlineField)),inlineField:t=>w.alt(w.seqMap(t.atomInlineField,w.string(",").trim(w.optWhitespace),t.inlineFieldList,(e,n,r)=>[e].concat(r)),t.atomInlineField),atomField:t=>w.alt(t.embedLink.map(e=>Me.literal(e)),t.negatedField,t.linkField,t.listField,t.objectField,t.lambdaField,t.parensField,t.boolField,t.numberField,t.stringField,t.dateField,t.durationField,t.nullField,t.variableField),indexField:t=>w.seqMap(t.atomField,w.alt(t.dotPostfix,t.indexPostfix,t.functionPostfix).many(),(e,n)=>{let r=e;for(let o of n)switch(o.type){case"dot":r=Me.index(r,Me.literal(o.field));break;case"index":r=Me.index(r,o.field);break;case"function":r=Me.func(r,o.fields);break}return r}),negatedField:t=>w.seqMap(w.string("!"),t.indexField,(e,n)=>Me.negate(n)).desc("negated field"),parensField:t=>w.seqMap(w.string("("),w.optWhitespace,t.field,w.optWhitespace,w.string(")"),(e,n,r,o,i)=>r),lambdaField:t=>w.seqMap(t.identifier.sepBy(w.string(",").trim(w.optWhitespace)).wrap(w.string("(").trim(w.optWhitespace),w.string(")").trim(w.optWhitespace)),w.string("=>").trim(w.optWhitespace),t.field,(e,n,r)=>({type:"lambda",arguments:e,value:r})),dotPostfix:t=>w.seqMap(w.string("."),t.identifier,(e,n)=>({type:"dot",field:n})),indexPostfix:t=>w.seqMap(w.string("["),w.optWhitespace,t.field,w.optWhitespace,w.string("]"),(e,n,r,o,i)=>({type:"index",field:r})),functionPostfix:t=>w.seqMap(w.string("("),w.optWhitespace,t.field.sepBy(w.string(",").trim(w.optWhitespace)),w.optWhitespace,w.string(")"),(e,n,r,o,i)=>({type:"function",fields:r})),binaryMulDivField:t=>xs(t.indexField,t.binaryMulDiv,Me.binaryOp),binaryPlusMinusField:t=>xs(t.binaryMulDivField,t.binaryPlusMinus,Me.binaryOp),binaryCompareField:t=>xs(t.binaryPlusMinusField,t.binaryCompareOp,Me.binaryOp),binaryBooleanField:t=>xs(t.binaryCompareField,t.binaryBooleanOp,Me.binaryOp),binaryOpField:t=>t.binaryBooleanField,field:t=>t.binaryOpField});function iT(t){try{return Xl.success(fn.field.tryParse(t))}catch(e){return Xl.failure(""+e)}}var tu;(function(t){function e(r,o){return{name:r,field:o}}t.named=e;function n(r,o){return{field:r,direction:o}}t.sortBy=n})(tu||(tu={}));function sT(t){return w.custom((e,n)=>(r,o)=>{let i=t._(r,o);return i.status?Object.assign({},i,{value:[i.value,r.substring(o,i.index)]}):i})}function aT(t){return t.split(/[\r\n]+/).map(e=>e.trim()).join("")}function Ky(t,e){return w.eof.map(t).or(w.whitespace.then(e))}var lT=w.createLanguage({queryType:t=>w.alt(w.regexp(/TABLE|LIST|TASK|CALENDAR/i)).map(e=>e.toLowerCase()).desc("query type ('TABLE', 'LIST', 'TASK', or 'CALENDAR')"),explicitNamedField:t=>w.seqMap(fn.field.skip(w.whitespace),w.regexp(/AS/i).skip(w.whitespace),fn.identifier.or(fn.string),(e,n,r)=>tu.named(r,e)),namedField:t=>w.alt(t.explicitNamedField,sT(fn.field).map(([e,n])=>tu.named(aT(n),e))),sortField:t=>w.seqMap(fn.field.skip(w.optWhitespace),w.regexp(/ASCENDING|DESCENDING|ASC|DESC/i).atMost(1),(e,n)=>{let r=n.length==0?"ascending":n[0].toLowerCase();return r=="desc"&&(r="descending"),r=="asc"&&(r="ascending"),{field:e,direction:r}}),headerClause:t=>t.queryType.chain(e=>{switch(e){case"table":return Ky(()=>({type:e,fields:[],showId:!0}),w.seqMap(w.regexp(/WITHOUT\s+ID/i).skip(w.optWhitespace).atMost(1),w.sepBy(t.namedField,w.string(",").trim(w.optWhitespace)),(n,r)=>({type:e,fields:r,showId:n.length==0})));case"list":return Ky(()=>({type:e,format:void 0,showId:!0}),w.seqMap(w.regexp(/WITHOUT\s+ID/i).skip(w.optWhitespace).atMost(1),fn.field.atMost(1),(n,r)=>({type:e,format:r.length==1?r[0]:void 0,showId:n.length==0})));case"task":return w.succeed({type:e});case"calendar":return w.whitespace.then(w.seqMap(t.namedField,n=>({type:e,showId:!0,field:n})));default:return w.fail(`Unrecognized query type '${e}'`)}}).desc("TABLE or LIST or TASK or CALENDAR"),fromClause:t=>w.seqMap(w.regexp(/FROM/i),w.whitespace,fn.source,(e,n,r)=>r),whereClause:t=>w.seqMap(w.regexp(/WHERE/i),w.whitespace,fn.field,(e,n,r)=>({type:"where",clause:r})).desc("WHERE <expression>"),sortByClause:t=>w.seqMap(w.regexp(/SORT/i),w.whitespace,t.sortField.sepBy1(w.string(",").trim(w.optWhitespace)),(e,n,r)=>({type:"sort",fields:r})).desc("SORT field [ASC/DESC]"),limitClause:t=>w.seqMap(w.regexp(/LIMIT/i),w.whitespace,fn.field,(e,n,r)=>({type:"limit",amount:r})).desc("LIMIT <value>"),flattenClause:t=>w.seqMap(w.regexp(/FLATTEN/i).skip(w.whitespace),t.namedField,(e,n)=>({type:"flatten",field:n})).desc("FLATTEN <value> [AS <name>]"),groupByClause:t=>w.seqMap(w.regexp(/GROUP BY/i).skip(w.whitespace),t.namedField,(e,n)=>({type:"group",field:n})).desc("GROUP BY <value> [AS <name>]"),clause:t=>w.alt(t.fromClause,t.whereClause,t.sortByClause,t.limitClause,t.groupByClause,t.flattenClause),query:t=>w.seqMap(t.headerClause.trim(w.optWhitespace),t.fromClause.trim(w.optWhitespace).atMost(1),t.clause.trim(w.optWhitespace).many(),(e,n,r)=>({header:e,source:n.length==0?In.folder(""):n[0],operations:r,settings:ad}))}),uT=t=>{var e;return t?(e=t.plugins.plugins.dataview)===null||e===void 0?void 0:e.api:window.DataviewAPI},cT=t=>t.plugins.enabledPlugins.has("dataview");An.DATE_SHORTHANDS=Xf;An.DURATION_TYPES=Jf;An.EXPRESSION=fn;An.KEYWORDS=ed;An.QUERY_LANGUAGE=lT;An.getAPI=uT;An.isPluginEnabled=cT;An.parseField=iT});var ED=Rn(ae=>{"use strict";var ca=Symbol.for("react.element"),BF=Symbol.for("react.portal"),VF=Symbol.for("react.fragment"),WF=Symbol.for("react.strict_mode"),HF=Symbol.for("react.profiler"),zF=Symbol.for("react.provider"),UF=Symbol.for("react.context"),jF=Symbol.for("react.forward_ref"),$F=Symbol.for("react.suspense"),GF=Symbol.for("react.memo"),YF=Symbol.for("react.lazy"),cD=Symbol.iterator;function ZF(t){return t===null||typeof t!="object"?null:(t=cD&&t[cD]||t["@@iterator"],typeof t=="function"?t:null)}var mD={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},pD=Object.assign,hD={};function Fi(t,e,n){this.props=t,this.context=e,this.refs=hD,this.updater=n||mD}Fi.prototype.isReactComponent={};Fi.prototype.setState=function(t,e){if(typeof t!="object"&&typeof t!="function"&&t!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,t,e,"setState")};Fi.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this,t,"forceUpdate")};function yD(){}yD.prototype=Fi.prototype;function Jd(t,e,n){this.props=t,this.context=e,this.refs=hD,this.updater=n||mD}var Xd=Jd.prototype=new yD;Xd.constructor=Jd;pD(Xd,Fi.prototype);Xd.isPureReactComponent=!0;var fD=Array.isArray,gD=Object.prototype.hasOwnProperty,em={current:null},DD={key:!0,ref:!0,__self:!0,__source:!0};function vD(t,e,n){var r,o={},i=null,s=null;if(e!=null)for(r in e.ref!==void 0&&(s=e.ref),e.key!==void 0&&(i=""+e.key),e)gD.call(e,r)&&!DD.hasOwnProperty(r)&&(o[r]=e[r]);var a=arguments.length-2;if(a===1)o.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];o.children=l}if(t&&t.defaultProps)for(r in a=t.defaultProps,a)o[r]===void 0&&(o[r]=a[r]);return{$$typeof:ca,type:t,key:i,ref:s,props:o,_owner:em.current}}function qF(t,e){return{$$typeof:ca,type:t.type,key:e,ref:t.ref,props:t.props,_owner:t._owner}}function tm(t){return typeof t=="object"&&t!==null&&t.$$typeof===ca}function KF(t){var e={"=":"=0",":":"=2"};return"$"+t.replace(/[=:]/g,function(n){return e[n]})}var dD=/\/+/g;function Qd(t,e){return typeof t=="object"&&t!==null&&t.key!=null?KF(""+t.key):e.toString(36)}function ku(t,e,n,r,o){var i=typeof t;(i==="undefined"||i==="boolean")&&(t=null);var s=!1;if(t===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(t.$$typeof){case ca:case BF:s=!0}}if(s)return s=t,o=o(s),t=r===""?"."+Qd(s,0):r,fD(o)?(n="",t!=null&&(n=t.replace(dD,"$&/")+"/"),ku(o,e,n,"",function(u){return u})):o!=null&&(tm(o)&&(o=qF(o,n+(!o.key||s&&s.key===o.key?"":(""+o.key).replace(dD,"$&/")+"/")+t)),e.push(o)),1;if(s=0,r=r===""?".":r+":",fD(t))for(var a=0;a<t.length;a++){i=t[a];var l=r+Qd(i,a);s+=ku(i,e,n,l,o)}else if(l=ZF(t),typeof l=="function")for(t=l.call(t),a=0;!(i=t.next()).done;)i=i.value,l=r+Qd(i,a++),s+=ku(i,e,n,l,o);else if(i==="object")throw e=String(t),Error("Objects are not valid as a React child (found: "+(e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)+"). If you meant to render a collection of children, use an array instead.");return s}function _u(t,e,n){if(t==null)return t;var r=[],o=0;return ku(t,r,"","",function(i){return e.call(n,i,o++)}),r}function QF(t){if(t._status===-1){var e=t._result;e=e(),e.then(function(n){(t._status===0||t._status===-1)&&(t._status=1,t._result=n)},function(n){(t._status===0||t._status===-1)&&(t._status=2,t._result=n)}),t._status===-1&&(t._status=0,t._result=e)}if(t._status===1)return t._result.default;throw t._result}var pt={current:null},Ou={transition:null},JF={ReactCurrentDispatcher:pt,ReactCurrentBatchConfig:Ou,ReactCurrentOwner:em};ae.Children={map:_u,forEach:function(t,e,n){_u(t,function(){e.apply(this,arguments)},n)},count:function(t){var e=0;return _u(t,function(){e++}),e},toArray:function(t){return _u(t,function(e){return e})||[]},only:function(t){if(!tm(t))throw Error("React.Children.only expected to receive a single React element child.");return t}};ae.Component=Fi;ae.Fragment=VF;ae.Profiler=HF;ae.PureComponent=Jd;ae.StrictMode=WF;ae.Suspense=$F;ae.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=JF;ae.cloneElement=function(t,e,n){if(t==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+t+".");var r=pD({},t.props),o=t.key,i=t.ref,s=t._owner;if(e!=null){if(e.ref!==void 0&&(i=e.ref,s=em.current),e.key!==void 0&&(o=""+e.key),t.type&&t.type.defaultProps)var a=t.type.defaultProps;for(l in e)gD.call(e,l)&&!DD.hasOwnProperty(l)&&(r[l]=e[l]===void 0&&a!==void 0?a[l]:e[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:ca,type:t.type,key:o,ref:i,props:r,_owner:s}};ae.createContext=function(t){return t={$$typeof:UF,_currentValue:t,_currentValue2:t,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},t.Provider={$$typeof:zF,_context:t},t.Consumer=t};ae.createElement=vD;ae.createFactory=function(t){var e=vD.bind(null,t);return e.type=t,e};ae.createRef=function(){return{current:null}};ae.forwardRef=function(t){return{$$typeof:jF,render:t}};ae.isValidElement=tm;ae.lazy=function(t){return{$$typeof:YF,_payload:{_status:-1,_result:t},_init:QF}};ae.memo=function(t,e){return{$$typeof:GF,type:t,compare:e===void 0?null:e}};ae.startTransition=function(t){var e=Ou.transition;Ou.transition={};try{t()}finally{Ou.transition=e}};ae.unstable_act=function(){throw Error("act(...) is not supported in production builds of React.")};ae.useCallback=function(t,e){return pt.current.useCallback(t,e)};ae.useContext=function(t){return pt.current.useContext(t)};ae.useDebugValue=function(){};ae.useDeferredValue=function(t){return pt.current.useDeferredValue(t)};ae.useEffect=function(t,e){return pt.current.useEffect(t,e)};ae.useId=function(){return pt.current.useId()};ae.useImperativeHandle=function(t,e,n){return pt.current.useImperativeHandle(t,e,n)};ae.useInsertionEffect=function(t,e){return pt.current.useInsertionEffect(t,e)};ae.useLayoutEffect=function(t,e){return pt.current.useLayoutEffect(t,e)};ae.useMemo=function(t,e){return pt.current.useMemo(t,e)};ae.useReducer=function(t,e,n){return pt.current.useReducer(t,e,n)};ae.useRef=function(t){return pt.current.useRef(t)};ae.useState=function(t){return pt.current.useState(t)};ae.useSyncExternalStore=function(t,e,n){return pt.current.useSyncExternalStore(t,e,n)};ae.useTransition=function(){return pt.current.useTransition()};ae.version="18.2.0"});var se=Rn((GN,wD)=>{"use strict";wD.exports=ED()});var RD=Rn(De=>{"use strict";function im(t,e){var n=t.length;t.push(e);e:for(;0<n;){var r=n-1>>>1,o=t[r];if(0<bu(o,e))t[r]=e,t[n]=o,n=r;else break e}}function vn(t){return t.length===0?null:t[0]}function Nu(t){if(t.length===0)return null;var e=t[0],n=t.pop();if(n!==e){t[0]=n;e:for(var r=0,o=t.length,i=o>>>1;r<i;){var s=2*(r+1)-1,a=t[s],l=s+1,u=t[l];if(0>bu(a,n))l<o&&0>bu(u,a)?(t[r]=u,t[l]=n,r=l):(t[r]=a,t[s]=n,r=s);else if(l<o&&0>bu(u,n))t[r]=u,t[l]=n,r=l;else break e}}return e}function bu(t,e){var n=t.sortIndex-e.sortIndex;return n!==0?n:t.id-e.id}typeof performance=="object"&&typeof performance.now=="function"?(SD=performance,De.unstable_now=function(){return SD.now()}):(nm=Date,CD=nm.now(),De.unstable_now=function(){return nm.now()-CD});var SD,nm,CD,Vn=[],Ar=[],XF=1,qt=null,at=3,Mu=!1,xo=!1,da=!1,FD=typeof setTimeout=="function"?setTimeout:null,_D=typeof clearTimeout=="function"?clearTimeout:null,xD=typeof setImmediate!="undefined"?setImmediate:null;typeof navigator!="undefined"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function sm(t){for(var e=vn(Ar);e!==null;){if(e.callback===null)Nu(Ar);else if(e.startTime<=t)Nu(Ar),e.sortIndex=e.expirationTime,im(Vn,e);else break;e=vn(Ar)}}function am(t){if(da=!1,sm(t),!xo)if(vn(Vn)!==null)xo=!0,um(lm);else{var e=vn(Ar);e!==null&&cm(am,e.startTime-t)}}function lm(t,e){xo=!1,da&&(da=!1,_D(ma),ma=-1),Mu=!0;var n=at;try{for(sm(e),qt=vn(Vn);qt!==null&&(!(qt.expirationTime>e)||t&&!bD());){var r=qt.callback;if(typeof r=="function"){qt.callback=null,at=qt.priorityLevel;var o=r(qt.expirationTime<=e);e=De.unstable_now(),typeof o=="function"?qt.callback=o:qt===vn(Vn)&&Nu(Vn),sm(e)}else Nu(Vn);qt=vn(Vn)}if(qt!==null)var i=!0;else{var s=vn(Ar);s!==null&&cm(am,s.startTime-e),i=!1}return i}finally{qt=null,at=n,Mu=!1}}var Iu=!1,Ru=null,ma=-1,kD=5,OD=-1;function bD(){return!(De.unstable_now()-OD<kD)}function rm(){if(Ru!==null){var t=De.unstable_now();OD=t;var e=!0;try{e=Ru(!0,t)}finally{e?fa():(Iu=!1,Ru=null)}}else Iu=!1}var fa;typeof xD=="function"?fa=function(){xD(rm)}:typeof MessageChannel!="undefined"?(om=new MessageChannel,TD=om.port2,om.port1.onmessage=rm,fa=function(){TD.postMessage(null)}):fa=function(){FD(rm,0)};var om,TD;function um(t){Ru=t,Iu||(Iu=!0,fa())}function cm(t,e){ma=FD(function(){t(De.unstable_now())},e)}De.unstable_IdlePriority=5;De.unstable_ImmediatePriority=1;De.unstable_LowPriority=4;De.unstable_NormalPriority=3;De.unstable_Profiling=null;De.unstable_UserBlockingPriority=2;De.unstable_cancelCallback=function(t){t.callback=null};De.unstable_continueExecution=function(){xo||Mu||(xo=!0,um(lm))};De.unstable_forceFrameRate=function(t){0>t||125<t?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):kD=0<t?Math.floor(1e3/t):5};De.unstable_getCurrentPriorityLevel=function(){return at};De.unstable_getFirstCallbackNode=function(){return vn(Vn)};De.unstable_next=function(t){switch(at){case 1:case 2:case 3:var e=3;break;default:e=at}var n=at;at=e;try{return t()}finally{at=n}};De.unstable_pauseExecution=function(){};De.unstable_requestPaint=function(){};De.unstable_runWithPriority=function(t,e){switch(t){case 1:case 2:case 3:case 4:case 5:break;default:t=3}var n=at;at=t;try{return e()}finally{at=n}};De.unstable_scheduleCallback=function(t,e,n){var r=De.unstable_now();switch(typeof n=="object"&&n!==null?(n=n.delay,n=typeof n=="number"&&0<n?r+n:r):n=r,t){case 1:var o=-1;break;case 2:o=250;break;case 5:o=1073741823;break;case 4:o=1e4;break;default:o=5e3}return o=n+o,t={id:XF++,callback:e,priorityLevel:t,startTime:n,expirationTime:o,sortIndex:-1},n>r?(t.sortIndex=n,im(Ar,t),vn(Vn)===null&&t===vn(Ar)&&(da?(_D(ma),ma=-1):da=!0,cm(am,n-r))):(t.sortIndex=o,im(Vn,t),xo||Mu||(xo=!0,um(lm))),t};De.unstable_shouldYield=bD;De.unstable_wrapCallback=function(t){var e=at;return function(){var n=at;at=e;try{return t.apply(this,arguments)}finally{at=n}}}});var MD=Rn((ZN,ND)=>{"use strict";ND.exports=RD()});var Vw=Rn(Wt=>{"use strict";var Wv=se(),Bt=MD();function M(t){for(var e="https://reactjs.org/docs/error-decoder.html?invariant="+t,n=1;n<arguments.length;n++)e+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Hv=new Set,Aa={};function Po(t,e){Yi(t,e),Yi(t+"Capture",e)}function Yi(t,e){for(Aa[t]=e,t=0;t<e.length;t++)Hv.add(e[t])}var ur=!(typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"),Mm=Object.prototype.hasOwnProperty,e_=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ID={},AD={};function t_(t){return Mm.call(AD,t)?!0:Mm.call(ID,t)?!1:e_.test(t)?AD[t]=!0:(ID[t]=!0,!1)}function n_(t,e,n,r){if(n!==null&&n.type===0)return!1;switch(typeof e){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(t=t.toLowerCase().slice(0,5),t!=="data-"&&t!=="aria-");default:return!1}}function r_(t,e,n,r){if(e===null||typeof e=="undefined"||n_(t,e,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!e;case 4:return e===!1;case 5:return isNaN(e);case 6:return isNaN(e)||1>e}return!1}function gt(t,e,n,r,o,i,s){this.acceptsBooleans=e===2||e===3||e===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=t,this.type=e,this.sanitizeURL=i,this.removeEmptyString=s}var st={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(t){st[t]=new gt(t,0,!1,t,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(t){var e=t[0];st[e]=new gt(e,1,!1,t[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(t){st[t]=new gt(t,2,!1,t.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(t){st[t]=new gt(t,2,!1,t,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(t){st[t]=new gt(t,3,!1,t.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(t){st[t]=new gt(t,3,!0,t,null,!1,!1)});["capture","download"].forEach(function(t){st[t]=new gt(t,4,!1,t,null,!1,!1)});["cols","rows","size","span"].forEach(function(t){st[t]=new gt(t,6,!1,t,null,!1,!1)});["rowSpan","start"].forEach(function(t){st[t]=new gt(t,5,!1,t.toLowerCase(),null,!1,!1)});var Tp=/[\-:]([a-z])/g;function Fp(t){return t[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(t){var e=t.replace(Tp,Fp);st[e]=new gt(e,1,!1,t,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(t){var e=t.replace(Tp,Fp);st[e]=new gt(e,1,!1,t,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(t){var e=t.replace(Tp,Fp);st[e]=new gt(e,1,!1,t,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(t){st[t]=new gt(t,1,!1,t.toLowerCase(),null,!1,!1)});st.xlinkHref=new gt("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(t){st[t]=new gt(t,1,!1,t.toLowerCase(),null,!0,!0)});function _p(t,e,n,r){var o=st.hasOwnProperty(e)?st[e]:null;(o!==null?o.type!==0:r||!(2<e.length)||e[0]!=="o"&&e[0]!=="O"||e[1]!=="n"&&e[1]!=="N")&&(r_(e,n,o,r)&&(n=null),r||o===null?t_(e)&&(n===null?t.removeAttribute(e):t.setAttribute(e,""+n)):o.mustUseProperty?t[o.propertyName]=n===null?o.type===3?!1:"":n:(e=o.attributeName,r=o.attributeNamespace,n===null?t.removeAttribute(e):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?t.setAttributeNS(r,e,n):t.setAttribute(e,n))))}var mr=Wv.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Au=Symbol.for("react.element"),Oi=Symbol.for("react.portal"),bi=Symbol.for("react.fragment"),kp=Symbol.for("react.strict_mode"),Im=Symbol.for("react.profiler"),zv=Symbol.for("react.provider"),Uv=Symbol.for("react.context"),Op=Symbol.for("react.forward_ref"),Am=Symbol.for("react.suspense"),Lm=Symbol.for("react.suspense_list"),bp=Symbol.for("react.memo"),Pr=Symbol.for("react.lazy");Symbol.for("react.scope");Symbol.for("react.debug_trace_mode");var jv=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden");Symbol.for("react.cache");Symbol.for("react.tracing_marker");var LD=Symbol.iterator;function pa(t){return t===null||typeof t!="object"?null:(t=LD&&t[LD]||t["@@iterator"],typeof t=="function"?t:null)}var Re=Object.assign,fm;function Sa(t){if(fm===void 0)try{throw Error()}catch(n){var e=n.stack.trim().match(/\n( *(at )?)/);fm=e&&e[1]||""}return`
`+fm+t}var dm=!1;function mm(t,e){if(!t||dm)return"";dm=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(e)if(e=function(){throw Error()},Object.defineProperty(e.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(e,[])}catch(u){var r=u}Reflect.construct(t,[],e)}else{try{e.call()}catch(u){r=u}t.call(e.prototype)}else{try{throw Error()}catch(u){r=u}t()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),i=r.stack.split(`
`),s=o.length-1,a=i.length-1;1<=s&&0<=a&&o[s]!==i[a];)a--;for(;1<=s&&0<=a;s--,a--)if(o[s]!==i[a]){if(s!==1||a!==1)do if(s--,a--,0>a||o[s]!==i[a]){var l=`
`+o[s].replace(" at new "," at ");return t.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",t.displayName)),l}while(1<=s&&0<=a);break}}}finally{dm=!1,Error.prepareStackTrace=n}return(t=t?t.displayName||t.name:"")?Sa(t):""}function o_(t){switch(t.tag){case 5:return Sa(t.type);case 16:return Sa("Lazy");case 13:return Sa("Suspense");case 19:return Sa("SuspenseList");case 0:case 2:case 15:return t=mm(t.type,!1),t;case 11:return t=mm(t.type.render,!1),t;case 1:return t=mm(t.type,!0),t;default:return""}}function Pm(t){if(t==null)return null;if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case bi:return"Fragment";case Oi:return"Portal";case Im:return"Profiler";case kp:return"StrictMode";case Am:return"Suspense";case Lm:return"SuspenseList"}if(typeof t=="object")switch(t.$$typeof){case Uv:return(t.displayName||"Context")+".Consumer";case zv:return(t._context.displayName||"Context")+".Provider";case Op:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case bp:return e=t.displayName||null,e!==null?e:Pm(t.type)||"Memo";case Pr:e=t._payload,t=t._init;try{return Pm(t(e))}catch(n){}}return null}function i_(t){var e=t.type;switch(t.tag){case 24:return"Cache";case 9:return(e.displayName||"Context")+".Consumer";case 10:return(e._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return t=e.render,t=t.displayName||t.name||"",e.displayName||(t!==""?"ForwardRef("+t+")":"ForwardRef");case 7:return"Fragment";case 5:return e;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Pm(e);case 8:return e===kp?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e}return null}function Qr(t){switch(typeof t){case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function $v(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function s_(t){var e=$v(t)?"checked":"value",n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),r=""+t[e];if(!t.hasOwnProperty(e)&&typeof n!="undefined"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,i=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return o.call(this)},set:function(s){r=""+s,i.call(this,s)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function Lu(t){t._valueTracker||(t._valueTracker=s_(t))}function Gv(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var n=e.getValue(),r="";return t&&(r=$v(t)?t.checked?"true":"false":t.value),t=r,t!==n?(e.setValue(t),!0):!1}function cc(t){if(t=t||(typeof document!="undefined"?document:void 0),typeof t=="undefined")return null;try{return t.activeElement||t.body}catch(e){return t.body}}function Bm(t,e){var n=e.checked;return Re({},e,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n!=null?n:t._wrapperState.initialChecked})}function PD(t,e){var n=e.defaultValue==null?"":e.defaultValue,r=e.checked!=null?e.checked:e.defaultChecked;n=Qr(e.value!=null?e.value:n),t._wrapperState={initialChecked:r,initialValue:n,controlled:e.type==="checkbox"||e.type==="radio"?e.checked!=null:e.value!=null}}function Yv(t,e){e=e.checked,e!=null&&_p(t,"checked",e,!1)}function Vm(t,e){Yv(t,e);var n=Qr(e.value),r=e.type;if(n!=null)r==="number"?(n===0&&t.value===""||t.value!=n)&&(t.value=""+n):t.value!==""+n&&(t.value=""+n);else if(r==="submit"||r==="reset"){t.removeAttribute("value");return}e.hasOwnProperty("value")?Wm(t,e.type,n):e.hasOwnProperty("defaultValue")&&Wm(t,e.type,Qr(e.defaultValue)),e.checked==null&&e.defaultChecked!=null&&(t.defaultChecked=!!e.defaultChecked)}function BD(t,e,n){if(e.hasOwnProperty("value")||e.hasOwnProperty("defaultValue")){var r=e.type;if(!(r!=="submit"&&r!=="reset"||e.value!==void 0&&e.value!==null))return;e=""+t._wrapperState.initialValue,n||e===t.value||(t.value=e),t.defaultValue=e}n=t.name,n!==""&&(t.name=""),t.defaultChecked=!!t._wrapperState.initialChecked,n!==""&&(t.name=n)}function Wm(t,e,n){(e!=="number"||cc(t.ownerDocument)!==t)&&(n==null?t.defaultValue=""+t._wrapperState.initialValue:t.defaultValue!==""+n&&(t.defaultValue=""+n))}var Ca=Array.isArray;function Hi(t,e,n,r){if(t=t.options,e){e={};for(var o=0;o<n.length;o++)e["$"+n[o]]=!0;for(n=0;n<t.length;n++)o=e.hasOwnProperty("$"+t[n].value),t[n].selected!==o&&(t[n].selected=o),o&&r&&(t[n].defaultSelected=!0)}else{for(n=""+Qr(n),e=null,o=0;o<t.length;o++){if(t[o].value===n){t[o].selected=!0,r&&(t[o].defaultSelected=!0);return}e!==null||t[o].disabled||(e=t[o])}e!==null&&(e.selected=!0)}}function Hm(t,e){if(e.dangerouslySetInnerHTML!=null)throw Error(M(91));return Re({},e,{value:void 0,defaultValue:void 0,children:""+t._wrapperState.initialValue})}function VD(t,e){var n=e.value;if(n==null){if(n=e.children,e=e.defaultValue,n!=null){if(e!=null)throw Error(M(92));if(Ca(n)){if(1<n.length)throw Error(M(93));n=n[0]}e=n}e==null&&(e=""),n=e}t._wrapperState={initialValue:Qr(n)}}function Zv(t,e){var n=Qr(e.value),r=Qr(e.defaultValue);n!=null&&(n=""+n,n!==t.value&&(t.value=n),e.defaultValue==null&&t.defaultValue!==n&&(t.defaultValue=n)),r!=null&&(t.defaultValue=""+r)}function WD(t){var e=t.textContent;e===t._wrapperState.initialValue&&e!==""&&e!==null&&(t.value=e)}function qv(t){switch(t){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function zm(t,e){return t==null||t==="http://www.w3.org/1999/xhtml"?qv(e):t==="http://www.w3.org/2000/svg"&&e==="foreignObject"?"http://www.w3.org/1999/xhtml":t}var Pu,Kv=function(t){return typeof MSApp!="undefined"&&MSApp.execUnsafeLocalFunction?function(e,n,r,o){MSApp.execUnsafeLocalFunction(function(){return t(e,n,r,o)})}:t}(function(t,e){if(t.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in t)t.innerHTML=e;else{for(Pu=Pu||document.createElement("div"),Pu.innerHTML="<svg>"+e.valueOf().toString()+"</svg>",e=Pu.firstChild;t.firstChild;)t.removeChild(t.firstChild);for(;e.firstChild;)t.appendChild(e.firstChild)}});function La(t,e){if(e){var n=t.firstChild;if(n&&n===t.lastChild&&n.nodeType===3){n.nodeValue=e;return}}t.textContent=e}var Fa={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},a_=["Webkit","ms","Moz","O"];Object.keys(Fa).forEach(function(t){a_.forEach(function(e){e=e+t.charAt(0).toUpperCase()+t.substring(1),Fa[e]=Fa[t]})});function Qv(t,e,n){return e==null||typeof e=="boolean"||e===""?"":n||typeof e!="number"||e===0||Fa.hasOwnProperty(t)&&Fa[t]?(""+e).trim():e+"px"}function Jv(t,e){t=t.style;for(var n in e)if(e.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Qv(n,e[n],r);n==="float"&&(n="cssFloat"),r?t.setProperty(n,o):t[n]=o}}var l_=Re({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Um(t,e){if(e){if(l_[t]&&(e.children!=null||e.dangerouslySetInnerHTML!=null))throw Error(M(137,t));if(e.dangerouslySetInnerHTML!=null){if(e.children!=null)throw Error(M(60));if(typeof e.dangerouslySetInnerHTML!="object"||!("__html"in e.dangerouslySetInnerHTML))throw Error(M(61))}if(e.style!=null&&typeof e.style!="object")throw Error(M(62))}}function jm(t,e){if(t.indexOf("-")===-1)return typeof e.is=="string";switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var $m=null;function Rp(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var Gm=null,zi=null,Ui=null;function HD(t){if(t=el(t)){if(typeof Gm!="function")throw Error(M(280));var e=t.stateNode;e&&(e=Bc(e),Gm(t.stateNode,t.type,e))}}function Xv(t){zi?Ui?Ui.push(t):Ui=[t]:zi=t}function eE(){if(zi){var t=zi,e=Ui;if(Ui=zi=null,HD(t),e)for(t=0;t<e.length;t++)HD(e[t])}}function tE(t,e){return t(e)}function nE(){}var pm=!1;function rE(t,e,n){if(pm)return t(e,n);pm=!0;try{return tE(t,e,n)}finally{pm=!1,(zi!==null||Ui!==null)&&(nE(),eE())}}function Pa(t,e){var n=t.stateNode;if(n===null)return null;var r=Bc(n);if(r===null)return null;n=r[e];e:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(t=t.type,r=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!r;break e;default:t=!1}if(t)return null;if(n&&typeof n!="function")throw Error(M(231,e,typeof n));return n}var Ym=!1;if(ur)try{_i={},Object.defineProperty(_i,"passive",{get:function(){Ym=!0}}),window.addEventListener("test",_i,_i),window.removeEventListener("test",_i,_i)}catch(t){Ym=!1}var _i;function u_(t,e,n,r,o,i,s,a,l){var u=Array.prototype.slice.call(arguments,3);try{e.apply(n,u)}catch(d){this.onError(d)}}var _a=!1,fc=null,dc=!1,Zm=null,c_={onError:function(t){_a=!0,fc=t}};function f_(t,e,n,r,o,i,s,a,l){_a=!1,fc=null,u_.apply(c_,arguments)}function d_(t,e,n,r,o,i,s,a,l){if(f_.apply(this,arguments),_a){if(_a){var u=fc;_a=!1,fc=null}else throw Error(M(198));dc||(dc=!0,Zm=u)}}function Bo(t){var e=t,n=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,e.flags&4098&&(n=e.return),t=e.return;while(t)}return e.tag===3?n:null}function oE(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function zD(t){if(Bo(t)!==t)throw Error(M(188))}function m_(t){var e=t.alternate;if(!e){if(e=Bo(t),e===null)throw Error(M(188));return e!==t?null:t}for(var n=t,r=e;;){var o=n.return;if(o===null)break;var i=o.alternate;if(i===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return zD(o),t;if(i===r)return zD(o),e;i=i.sibling}throw Error(M(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,a=o.child;a;){if(a===n){s=!0,n=o,r=i;break}if(a===r){s=!0,r=o,n=i;break}a=a.sibling}if(!s){for(a=i.child;a;){if(a===n){s=!0,n=i,r=o;break}if(a===r){s=!0,r=i,n=o;break}a=a.sibling}if(!s)throw Error(M(189))}}if(n.alternate!==r)throw Error(M(190))}if(n.tag!==3)throw Error(M(188));return n.stateNode.current===n?t:e}function iE(t){return t=m_(t),t!==null?sE(t):null}function sE(t){if(t.tag===5||t.tag===6)return t;for(t=t.child;t!==null;){var e=sE(t);if(e!==null)return e;t=t.sibling}return null}var aE=Bt.unstable_scheduleCallback,UD=Bt.unstable_cancelCallback,p_=Bt.unstable_shouldYield,h_=Bt.unstable_requestPaint,Le=Bt.unstable_now,y_=Bt.unstable_getCurrentPriorityLevel,Np=Bt.unstable_ImmediatePriority,lE=Bt.unstable_UserBlockingPriority,mc=Bt.unstable_NormalPriority,g_=Bt.unstable_LowPriority,uE=Bt.unstable_IdlePriority,Ic=null,Un=null;function D_(t){if(Un&&typeof Un.onCommitFiberRoot=="function")try{Un.onCommitFiberRoot(Ic,t,void 0,(t.current.flags&128)===128)}catch(e){}}var xn=Math.clz32?Math.clz32:w_,v_=Math.log,E_=Math.LN2;function w_(t){return t>>>=0,t===0?32:31-(v_(t)/E_|0)|0}var Bu=64,Vu=4194304;function xa(t){switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return t&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return t}}function pc(t,e){var n=t.pendingLanes;if(n===0)return 0;var r=0,o=t.suspendedLanes,i=t.pingedLanes,s=n&268435455;if(s!==0){var a=s&~o;a!==0?r=xa(a):(i&=s,i!==0&&(r=xa(i)))}else s=n&~o,s!==0?r=xa(s):i!==0&&(r=xa(i));if(r===0)return 0;if(e!==0&&e!==r&&!(e&o)&&(o=r&-r,i=e&-e,o>=i||o===16&&(i&4194240)!==0))return e;if(r&4&&(r|=n&16),e=t.entangledLanes,e!==0)for(t=t.entanglements,e&=r;0<e;)n=31-xn(e),o=1<<n,r|=t[n],e&=~o;return r}function S_(t,e){switch(t){case 1:case 2:case 4:return e+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function C_(t,e){for(var n=t.suspendedLanes,r=t.pingedLanes,o=t.expirationTimes,i=t.pendingLanes;0<i;){var s=31-xn(i),a=1<<s,l=o[s];l===-1?(!(a&n)||a&r)&&(o[s]=S_(a,e)):l<=e&&(t.expiredLanes|=a),i&=~a}}function qm(t){return t=t.pendingLanes&-1073741825,t!==0?t:t&1073741824?1073741824:0}function cE(){var t=Bu;return Bu<<=1,!(Bu&4194240)&&(Bu=64),t}function hm(t){for(var e=[],n=0;31>n;n++)e.push(t);return e}function Ja(t,e,n){t.pendingLanes|=e,e!==536870912&&(t.suspendedLanes=0,t.pingedLanes=0),t=t.eventTimes,e=31-xn(e),t[e]=n}function x_(t,e){var n=t.pendingLanes&~e;t.pendingLanes=e,t.suspendedLanes=0,t.pingedLanes=0,t.expiredLanes&=e,t.mutableReadLanes&=e,t.entangledLanes&=e,e=t.entanglements;var r=t.eventTimes;for(t=t.expirationTimes;0<n;){var o=31-xn(n),i=1<<o;e[o]=0,r[o]=-1,t[o]=-1,n&=~i}}function Mp(t,e){var n=t.entangledLanes|=e;for(t=t.entanglements;n;){var r=31-xn(n),o=1<<r;o&e|t[r]&e&&(t[r]|=e),n&=~o}}var pe=0;function fE(t){return t&=-t,1<t?4<t?t&268435455?16:536870912:4:1}var dE,Ip,mE,pE,hE,Km=!1,Wu=[],Ur=null,jr=null,$r=null,Ba=new Map,Va=new Map,Vr=[],T_="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function jD(t,e){switch(t){case"focusin":case"focusout":Ur=null;break;case"dragenter":case"dragleave":jr=null;break;case"mouseover":case"mouseout":$r=null;break;case"pointerover":case"pointerout":Ba.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Va.delete(e.pointerId)}}function ha(t,e,n,r,o,i){return t===null||t.nativeEvent!==i?(t={blockedOn:e,domEventName:n,eventSystemFlags:r,nativeEvent:i,targetContainers:[o]},e!==null&&(e=el(e),e!==null&&Ip(e)),t):(t.eventSystemFlags|=r,e=t.targetContainers,o!==null&&e.indexOf(o)===-1&&e.push(o),t)}function F_(t,e,n,r,o){switch(e){case"focusin":return Ur=ha(Ur,t,e,n,r,o),!0;case"dragenter":return jr=ha(jr,t,e,n,r,o),!0;case"mouseover":return $r=ha($r,t,e,n,r,o),!0;case"pointerover":var i=o.pointerId;return Ba.set(i,ha(Ba.get(i)||null,t,e,n,r,o)),!0;case"gotpointercapture":return i=o.pointerId,Va.set(i,ha(Va.get(i)||null,t,e,n,r,o)),!0}return!1}function yE(t){var e=_o(t.target);if(e!==null){var n=Bo(e);if(n!==null){if(e=n.tag,e===13){if(e=oE(n),e!==null){t.blockedOn=e,hE(t.priority,function(){mE(n)});return}}else if(e===3&&n.stateNode.current.memoizedState.isDehydrated){t.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}t.blockedOn=null}function ec(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var n=Qm(t.domEventName,t.eventSystemFlags,e[0],t.nativeEvent);if(n===null){n=t.nativeEvent;var r=new n.constructor(n.type,n);$m=r,n.target.dispatchEvent(r),$m=null}else return e=el(n),e!==null&&Ip(e),t.blockedOn=n,!1;e.shift()}return!0}function $D(t,e,n){ec(t)&&n.delete(e)}function __(){Km=!1,Ur!==null&&ec(Ur)&&(Ur=null),jr!==null&&ec(jr)&&(jr=null),$r!==null&&ec($r)&&($r=null),Ba.forEach($D),Va.forEach($D)}function ya(t,e){t.blockedOn===e&&(t.blockedOn=null,Km||(Km=!0,Bt.unstable_scheduleCallback(Bt.unstable_NormalPriority,__)))}function Wa(t){function e(o){return ya(o,t)}if(0<Wu.length){ya(Wu[0],t);for(var n=1;n<Wu.length;n++){var r=Wu[n];r.blockedOn===t&&(r.blockedOn=null)}}for(Ur!==null&&ya(Ur,t),jr!==null&&ya(jr,t),$r!==null&&ya($r,t),Ba.forEach(e),Va.forEach(e),n=0;n<Vr.length;n++)r=Vr[n],r.blockedOn===t&&(r.blockedOn=null);for(;0<Vr.length&&(n=Vr[0],n.blockedOn===null);)yE(n),n.blockedOn===null&&Vr.shift()}var ji=mr.ReactCurrentBatchConfig,hc=!0;function k_(t,e,n,r){var o=pe,i=ji.transition;ji.transition=null;try{pe=1,Ap(t,e,n,r)}finally{pe=o,ji.transition=i}}function O_(t,e,n,r){var o=pe,i=ji.transition;ji.transition=null;try{pe=4,Ap(t,e,n,r)}finally{pe=o,ji.transition=i}}function Ap(t,e,n,r){if(hc){var o=Qm(t,e,n,r);if(o===null)Sm(t,e,r,yc,n),jD(t,r);else if(F_(o,t,e,n,r))r.stopPropagation();else if(jD(t,r),e&4&&-1<T_.indexOf(t)){for(;o!==null;){var i=el(o);if(i!==null&&dE(i),i=Qm(t,e,n,r),i===null&&Sm(t,e,r,yc,n),i===o)break;o=i}o!==null&&r.stopPropagation()}else Sm(t,e,r,null,n)}}var yc=null;function Qm(t,e,n,r){if(yc=null,t=Rp(r),t=_o(t),t!==null)if(e=Bo(t),e===null)t=null;else if(n=e.tag,n===13){if(t=oE(e),t!==null)return t;t=null}else if(n===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null);return yc=t,null}function gE(t){switch(t){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(y_()){case Np:return 1;case lE:return 4;case mc:case g_:return 16;case uE:return 536870912;default:return 16}default:return 16}}var Hr=null,Lp=null,tc=null;function DE(){if(tc)return tc;var t,e=Lp,n=e.length,r,o="value"in Hr?Hr.value:Hr.textContent,i=o.length;for(t=0;t<n&&e[t]===o[t];t++);var s=n-t;for(r=1;r<=s&&e[n-r]===o[i-r];r++);return tc=o.slice(t,1<r?1-r:void 0)}function nc(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Hu(){return!0}function GD(){return!1}function Vt(t){function e(n,r,o,i,s){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var a in t)t.hasOwnProperty(a)&&(n=t[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Hu:GD,this.isPropagationStopped=GD,this}return Re(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Hu)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Hu)},persist:function(){},isPersistent:Hu}),e}var es={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Pp=Vt(es),Xa=Re({},es,{view:0,detail:0}),b_=Vt(Xa),ym,gm,ga,Ac=Re({},Xa,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Bp,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==ga&&(ga&&t.type==="mousemove"?(ym=t.screenX-ga.screenX,gm=t.screenY-ga.screenY):gm=ym=0,ga=t),ym)},movementY:function(t){return"movementY"in t?t.movementY:gm}}),YD=Vt(Ac),R_=Re({},Ac,{dataTransfer:0}),N_=Vt(R_),M_=Re({},Xa,{relatedTarget:0}),Dm=Vt(M_),I_=Re({},es,{animationName:0,elapsedTime:0,pseudoElement:0}),A_=Vt(I_),L_=Re({},es,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),P_=Vt(L_),B_=Re({},es,{data:0}),ZD=Vt(B_),V_={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},W_={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},H_={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function z_(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=H_[t])?!!e[t]:!1}function Bp(){return z_}var U_=Re({},Xa,{key:function(t){if(t.key){var e=V_[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=nc(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?W_[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Bp,charCode:function(t){return t.type==="keypress"?nc(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?nc(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),j_=Vt(U_),$_=Re({},Ac,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),qD=Vt($_),G_=Re({},Xa,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Bp}),Y_=Vt(G_),Z_=Re({},es,{propertyName:0,elapsedTime:0,pseudoElement:0}),q_=Vt(Z_),K_=Re({},Ac,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),Q_=Vt(K_),J_=[9,13,27,32],Vp=ur&&"CompositionEvent"in window,ka=null;ur&&"documentMode"in document&&(ka=document.documentMode);var X_=ur&&"TextEvent"in window&&!ka,vE=ur&&(!Vp||ka&&8<ka&&11>=ka),KD=String.fromCharCode(32),QD=!1;function EE(t,e){switch(t){case"keyup":return J_.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function wE(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Ri=!1;function ek(t,e){switch(t){case"compositionend":return wE(e);case"keypress":return e.which!==32?null:(QD=!0,KD);case"textInput":return t=e.data,t===KD&&QD?null:t;default:return null}}function tk(t,e){if(Ri)return t==="compositionend"||!Vp&&EE(t,e)?(t=DE(),tc=Lp=Hr=null,Ri=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return vE&&e.locale!=="ko"?null:e.data;default:return null}}var nk={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function JD(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!nk[t.type]:e==="textarea"}function SE(t,e,n,r){Xv(r),e=gc(e,"onChange"),0<e.length&&(n=new Pp("onChange","change",null,n,r),t.push({event:n,listeners:e}))}var Oa=null,Ha=null;function rk(t){ME(t,0)}function Lc(t){var e=Ii(t);if(Gv(e))return t}function ok(t,e){if(t==="change")return e}var CE=!1;ur&&(ur?(Uu="oninput"in document,Uu||(vm=document.createElement("div"),vm.setAttribute("oninput","return;"),Uu=typeof vm.oninput=="function"),zu=Uu):zu=!1,CE=zu&&(!document.documentMode||9<document.documentMode));var zu,Uu,vm;function XD(){Oa&&(Oa.detachEvent("onpropertychange",xE),Ha=Oa=null)}function xE(t){if(t.propertyName==="value"&&Lc(Ha)){var e=[];SE(e,Ha,t,Rp(t)),rE(rk,e)}}function ik(t,e,n){t==="focusin"?(XD(),Oa=e,Ha=n,Oa.attachEvent("onpropertychange",xE)):t==="focusout"&&XD()}function sk(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Lc(Ha)}function ak(t,e){if(t==="click")return Lc(e)}function lk(t,e){if(t==="input"||t==="change")return Lc(e)}function uk(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Fn=typeof Object.is=="function"?Object.is:uk;function za(t,e){if(Fn(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var n=Object.keys(t),r=Object.keys(e);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Mm.call(e,o)||!Fn(t[o],e[o]))return!1}return!0}function ev(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function tv(t,e){var n=ev(t);t=0;for(var r;n;){if(n.nodeType===3){if(r=t+n.textContent.length,t<=e&&r>=e)return{node:n,offset:e-t};t=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=ev(n)}}function TE(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?TE(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function FE(){for(var t=window,e=cc();e instanceof t.HTMLIFrameElement;){try{var n=typeof e.contentWindow.location.href=="string"}catch(r){n=!1}if(n)t=e.contentWindow;else break;e=cc(t.document)}return e}function Wp(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}function ck(t){var e=FE(),n=t.focusedElem,r=t.selectionRange;if(e!==n&&n&&n.ownerDocument&&TE(n.ownerDocument.documentElement,n)){if(r!==null&&Wp(n)){if(e=r.start,t=r.end,t===void 0&&(t=e),"selectionStart"in n)n.selectionStart=e,n.selectionEnd=Math.min(t,n.value.length);else if(t=(e=n.ownerDocument||document)&&e.defaultView||window,t.getSelection){t=t.getSelection();var o=n.textContent.length,i=Math.min(r.start,o);r=r.end===void 0?i:Math.min(r.end,o),!t.extend&&i>r&&(o=r,r=i,i=o),o=tv(n,i);var s=tv(n,r);o&&s&&(t.rangeCount!==1||t.anchorNode!==o.node||t.anchorOffset!==o.offset||t.focusNode!==s.node||t.focusOffset!==s.offset)&&(e=e.createRange(),e.setStart(o.node,o.offset),t.removeAllRanges(),i>r?(t.addRange(e),t.extend(s.node,s.offset)):(e.setEnd(s.node,s.offset),t.addRange(e)))}}for(e=[],t=n;t=t.parentNode;)t.nodeType===1&&e.push({element:t,left:t.scrollLeft,top:t.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<e.length;n++)t=e[n],t.element.scrollLeft=t.left,t.element.scrollTop=t.top}}var fk=ur&&"documentMode"in document&&11>=document.documentMode,Ni=null,Jm=null,ba=null,Xm=!1;function nv(t,e,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Xm||Ni==null||Ni!==cc(r)||(r=Ni,"selectionStart"in r&&Wp(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),ba&&za(ba,r)||(ba=r,r=gc(Jm,"onSelect"),0<r.length&&(e=new Pp("onSelect","select",null,e,n),t.push({event:e,listeners:r}),e.target=Ni)))}function ju(t,e){var n={};return n[t.toLowerCase()]=e.toLowerCase(),n["Webkit"+t]="webkit"+e,n["Moz"+t]="moz"+e,n}var Mi={animationend:ju("Animation","AnimationEnd"),animationiteration:ju("Animation","AnimationIteration"),animationstart:ju("Animation","AnimationStart"),transitionend:ju("Transition","TransitionEnd")},Em={},_E={};ur&&(_E=document.createElement("div").style,"AnimationEvent"in window||(delete Mi.animationend.animation,delete Mi.animationiteration.animation,delete Mi.animationstart.animation),"TransitionEvent"in window||delete Mi.transitionend.transition);function Pc(t){if(Em[t])return Em[t];if(!Mi[t])return t;var e=Mi[t],n;for(n in e)if(e.hasOwnProperty(n)&&n in _E)return Em[t]=e[n];return t}var kE=Pc("animationend"),OE=Pc("animationiteration"),bE=Pc("animationstart"),RE=Pc("transitionend"),NE=new Map,rv="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Xr(t,e){NE.set(t,e),Po(e,[t])}for($u=0;$u<rv.length;$u++)Gu=rv[$u],ov=Gu.toLowerCase(),iv=Gu[0].toUpperCase()+Gu.slice(1),Xr(ov,"on"+iv);var Gu,ov,iv,$u;Xr(kE,"onAnimationEnd");Xr(OE,"onAnimationIteration");Xr(bE,"onAnimationStart");Xr("dblclick","onDoubleClick");Xr("focusin","onFocus");Xr("focusout","onBlur");Xr(RE,"onTransitionEnd");Yi("onMouseEnter",["mouseout","mouseover"]);Yi("onMouseLeave",["mouseout","mouseover"]);Yi("onPointerEnter",["pointerout","pointerover"]);Yi("onPointerLeave",["pointerout","pointerover"]);Po("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Po("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Po("onBeforeInput",["compositionend","keypress","textInput","paste"]);Po("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Po("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Po("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ta="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),dk=new Set("cancel close invalid load scroll toggle".split(" ").concat(Ta));function sv(t,e,n){var r=t.type||"unknown-event";t.currentTarget=n,d_(r,e,void 0,t),t.currentTarget=null}function ME(t,e){e=(e&4)!==0;for(var n=0;n<t.length;n++){var r=t[n],o=r.event;r=r.listeners;e:{var i=void 0;if(e)for(var s=r.length-1;0<=s;s--){var a=r[s],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==i&&o.isPropagationStopped())break e;sv(o,a,u),i=l}else for(s=0;s<r.length;s++){if(a=r[s],l=a.instance,u=a.currentTarget,a=a.listener,l!==i&&o.isPropagationStopped())break e;sv(o,a,u),i=l}}}if(dc)throw t=Zm,dc=!1,Zm=null,t}function Ee(t,e){var n=e[op];n===void 0&&(n=e[op]=new Set);var r=t+"__bubble";n.has(r)||(IE(e,t,2,!1),n.add(r))}function wm(t,e,n){var r=0;e&&(r|=4),IE(n,t,r,e)}var Yu="_reactListening"+Math.random().toString(36).slice(2);function Ua(t){if(!t[Yu]){t[Yu]=!0,Hv.forEach(function(n){n!=="selectionchange"&&(dk.has(n)||wm(n,!1,t),wm(n,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Yu]||(e[Yu]=!0,wm("selectionchange",!1,e))}}function IE(t,e,n,r){switch(gE(e)){case 1:var o=k_;break;case 4:o=O_;break;default:o=Ap}n=o.bind(null,e,n,t),o=void 0,!Ym||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(o=!0),r?o!==void 0?t.addEventListener(e,n,{capture:!0,passive:o}):t.addEventListener(e,n,!0):o!==void 0?t.addEventListener(e,n,{passive:o}):t.addEventListener(e,n,!1)}function Sm(t,e,n,r,o){var i=r;if(!(e&1)&&!(e&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===o||a.nodeType===8&&a.parentNode===o)break;if(s===4)for(s=r.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===o||l.nodeType===8&&l.parentNode===o))return;s=s.return}for(;a!==null;){if(s=_o(a),s===null)return;if(l=s.tag,l===5||l===6){r=i=s;continue e}a=a.parentNode}}r=r.return}rE(function(){var u=i,d=Rp(n),f=[];e:{var m=NE.get(t);if(m!==void 0){var c=Pp,y=t;switch(t){case"keypress":if(nc(n)===0)break e;case"keydown":case"keyup":c=j_;break;case"focusin":y="focus",c=Dm;break;case"focusout":y="blur",c=Dm;break;case"beforeblur":case"afterblur":c=Dm;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":c=YD;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":c=N_;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":c=Y_;break;case kE:case OE:case bE:c=A_;break;case RE:c=q_;break;case"scroll":c=b_;break;case"wheel":c=Q_;break;case"copy":case"cut":case"paste":c=P_;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":c=qD}var v=(e&4)!==0,S=!v&&t==="scroll",h=v?m!==null?m+"Capture":null:m;v=[];for(var p=u,E;p!==null;){E=p;var D=E.stateNode;if(E.tag===5&&D!==null&&(E=D,h!==null&&(D=Pa(p,h),D!=null&&v.push(ja(p,D,E)))),S)break;p=p.return}0<v.length&&(m=new c(m,y,null,n,d),f.push({event:m,listeners:v}))}}if(!(e&7)){e:{if(m=t==="mouseover"||t==="pointerover",c=t==="mouseout"||t==="pointerout",m&&n!==$m&&(y=n.relatedTarget||n.fromElement)&&(_o(y)||y[cr]))break e;if((c||m)&&(m=d.window===d?d:(m=d.ownerDocument)?m.defaultView||m.parentWindow:window,c?(y=n.relatedTarget||n.toElement,c=u,y=y?_o(y):null,y!==null&&(S=Bo(y),y!==S||y.tag!==5&&y.tag!==6)&&(y=null)):(c=null,y=u),c!==y)){if(v=YD,D="onMouseLeave",h="onMouseEnter",p="mouse",(t==="pointerout"||t==="pointerover")&&(v=qD,D="onPointerLeave",h="onPointerEnter",p="pointer"),S=c==null?m:Ii(c),E=y==null?m:Ii(y),m=new v(D,p+"leave",c,n,d),m.target=S,m.relatedTarget=E,D=null,_o(d)===u&&(v=new v(h,p+"enter",y,n,d),v.target=E,v.relatedTarget=S,D=v),S=D,c&&y)t:{for(v=c,h=y,p=0,E=v;E;E=ki(E))p++;for(E=0,D=h;D;D=ki(D))E++;for(;0<p-E;)v=ki(v),p--;for(;0<E-p;)h=ki(h),E--;for(;p--;){if(v===h||h!==null&&v===h.alternate)break t;v=ki(v),h=ki(h)}v=null}else v=null;c!==null&&av(f,m,c,v,!1),y!==null&&S!==null&&av(f,S,y,v,!0)}}e:{if(m=u?Ii(u):window,c=m.nodeName&&m.nodeName.toLowerCase(),c==="select"||c==="input"&&m.type==="file")var x=ok;else if(JD(m))if(CE)x=lk;else{x=sk;var O=ik}else(c=m.nodeName)&&c.toLowerCase()==="input"&&(m.type==="checkbox"||m.type==="radio")&&(x=ak);if(x&&(x=x(t,u))){SE(f,x,n,d);break e}O&&O(t,m,u),t==="focusout"&&(O=m._wrapperState)&&O.controlled&&m.type==="number"&&Wm(m,"number",m.value)}switch(O=u?Ii(u):window,t){case"focusin":(JD(O)||O.contentEditable==="true")&&(Ni=O,Jm=u,ba=null);break;case"focusout":ba=Jm=Ni=null;break;case"mousedown":Xm=!0;break;case"contextmenu":case"mouseup":case"dragend":Xm=!1,nv(f,n,d);break;case"selectionchange":if(fk)break;case"keydown":case"keyup":nv(f,n,d)}var _;if(Vp)e:{switch(t){case"compositionstart":var k="onCompositionStart";break e;case"compositionend":k="onCompositionEnd";break e;case"compositionupdate":k="onCompositionUpdate";break e}k=void 0}else Ri?EE(t,n)&&(k="onCompositionEnd"):t==="keydown"&&n.keyCode===229&&(k="onCompositionStart");k&&(vE&&n.locale!=="ko"&&(Ri||k!=="onCompositionStart"?k==="onCompositionEnd"&&Ri&&(_=DE()):(Hr=d,Lp="value"in Hr?Hr.value:Hr.textContent,Ri=!0)),O=gc(u,k),0<O.length&&(k=new ZD(k,t,null,n,d),f.push({event:k,listeners:O}),_?k.data=_:(_=wE(n),_!==null&&(k.data=_)))),(_=X_?ek(t,n):tk(t,n))&&(u=gc(u,"onBeforeInput"),0<u.length&&(d=new ZD("onBeforeInput","beforeinput",null,n,d),f.push({event:d,listeners:u}),d.data=_))}ME(f,e)})}function ja(t,e,n){return{instance:t,listener:e,currentTarget:n}}function gc(t,e){for(var n=e+"Capture",r=[];t!==null;){var o=t,i=o.stateNode;o.tag===5&&i!==null&&(o=i,i=Pa(t,n),i!=null&&r.unshift(ja(t,i,o)),i=Pa(t,e),i!=null&&r.push(ja(t,i,o))),t=t.return}return r}function ki(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5);return t||null}function av(t,e,n,r,o){for(var i=e._reactName,s=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,o?(l=Pa(n,i),l!=null&&s.unshift(ja(n,l,a))):o||(l=Pa(n,i),l!=null&&s.push(ja(n,l,a)))),n=n.return}s.length!==0&&t.push({event:e,listeners:s})}var mk=/\r\n?/g,pk=/\u0000|\uFFFD/g;function lv(t){return(typeof t=="string"?t:""+t).replace(mk,`
`).replace(pk,"")}function Zu(t,e,n){if(e=lv(e),lv(t)!==e&&n)throw Error(M(425))}function Dc(){}var ep=null,tp=null;function np(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var rp=typeof setTimeout=="function"?setTimeout:void 0,hk=typeof clearTimeout=="function"?clearTimeout:void 0,uv=typeof Promise=="function"?Promise:void 0,yk=typeof queueMicrotask=="function"?queueMicrotask:typeof uv!="undefined"?function(t){return uv.resolve(null).then(t).catch(gk)}:rp;function gk(t){setTimeout(function(){throw t})}function Cm(t,e){var n=e,r=0;do{var o=n.nextSibling;if(t.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){t.removeChild(o),Wa(e);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Wa(e)}function Gr(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?")break;if(e==="/$")return null}}return t}function cv(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="$"||n==="$!"||n==="$?"){if(e===0)return t;e--}else n==="/$"&&e++}t=t.previousSibling}return null}var ts=Math.random().toString(36).slice(2),zn="__reactFiber$"+ts,$a="__reactProps$"+ts,cr="__reactContainer$"+ts,op="__reactEvents$"+ts,Dk="__reactListeners$"+ts,vk="__reactHandles$"+ts;function _o(t){var e=t[zn];if(e)return e;for(var n=t.parentNode;n;){if(e=n[cr]||n[zn]){if(n=e.alternate,e.child!==null||n!==null&&n.child!==null)for(t=cv(t);t!==null;){if(n=t[zn])return n;t=cv(t)}return e}t=n,n=t.parentNode}return null}function el(t){return t=t[zn]||t[cr],!t||t.tag!==5&&t.tag!==6&&t.tag!==13&&t.tag!==3?null:t}function Ii(t){if(t.tag===5||t.tag===6)return t.stateNode;throw Error(M(33))}function Bc(t){return t[$a]||null}var ip=[],Ai=-1;function eo(t){return{current:t}}function we(t){0>Ai||(t.current=ip[Ai],ip[Ai]=null,Ai--)}function ve(t,e){Ai++,ip[Ai]=t.current,t.current=e}var Jr={},ft=eo(Jr),Tt=eo(!1),No=Jr;function Zi(t,e){var n=t.type.contextTypes;if(!n)return Jr;var r=t.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===e)return r.__reactInternalMemoizedMaskedChildContext;var o={},i;for(i in n)o[i]=e[i];return r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=e,t.__reactInternalMemoizedMaskedChildContext=o),o}function Ft(t){return t=t.childContextTypes,t!=null}function vc(){we(Tt),we(ft)}function fv(t,e,n){if(ft.current!==Jr)throw Error(M(168));ve(ft,e),ve(Tt,n)}function AE(t,e,n){var r=t.stateNode;if(e=e.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in e))throw Error(M(108,i_(t)||"Unknown",o));return Re({},n,r)}function Ec(t){return t=(t=t.stateNode)&&t.__reactInternalMemoizedMergedChildContext||Jr,No=ft.current,ve(ft,t),ve(Tt,Tt.current),!0}function dv(t,e,n){var r=t.stateNode;if(!r)throw Error(M(169));n?(t=AE(t,e,No),r.__reactInternalMemoizedMergedChildContext=t,we(Tt),we(ft),ve(ft,t)):we(Tt),ve(Tt,n)}var ir=null,Vc=!1,xm=!1;function LE(t){ir===null?ir=[t]:ir.push(t)}function Ek(t){Vc=!0,LE(t)}function to(){if(!xm&&ir!==null){xm=!0;var t=0,e=pe;try{var n=ir;for(pe=1;t<n.length;t++){var r=n[t];do r=r(!0);while(r!==null)}ir=null,Vc=!1}catch(o){throw ir!==null&&(ir=ir.slice(t+1)),aE(Np,to),o}finally{pe=e,xm=!1}}return null}var Li=[],Pi=0,wc=null,Sc=0,Kt=[],Qt=0,Mo=null,sr=1,ar="";function To(t,e){Li[Pi++]=Sc,Li[Pi++]=wc,wc=t,Sc=e}function PE(t,e,n){Kt[Qt++]=sr,Kt[Qt++]=ar,Kt[Qt++]=Mo,Mo=t;var r=sr;t=ar;var o=32-xn(r)-1;r&=~(1<<o),n+=1;var i=32-xn(e)+o;if(30<i){var s=o-o%5;i=(r&(1<<s)-1).toString(32),r>>=s,o-=s,sr=1<<32-xn(e)+o|n<<o|r,ar=i+t}else sr=1<<i|n<<o|r,ar=t}function Hp(t){t.return!==null&&(To(t,1),PE(t,1,0))}function zp(t){for(;t===wc;)wc=Li[--Pi],Li[Pi]=null,Sc=Li[--Pi],Li[Pi]=null;for(;t===Mo;)Mo=Kt[--Qt],Kt[Qt]=null,ar=Kt[--Qt],Kt[Qt]=null,sr=Kt[--Qt],Kt[Qt]=null}var Pt=null,Lt=null,xe=!1,Cn=null;function BE(t,e){var n=Jt(5,null,null,0);n.elementType="DELETED",n.stateNode=e,n.return=t,e=t.deletions,e===null?(t.deletions=[n],t.flags|=16):e.push(n)}function mv(t,e){switch(t.tag){case 5:var n=t.type;return e=e.nodeType!==1||n.toLowerCase()!==e.nodeName.toLowerCase()?null:e,e!==null?(t.stateNode=e,Pt=t,Lt=Gr(e.firstChild),!0):!1;case 6:return e=t.pendingProps===""||e.nodeType!==3?null:e,e!==null?(t.stateNode=e,Pt=t,Lt=null,!0):!1;case 13:return e=e.nodeType!==8?null:e,e!==null?(n=Mo!==null?{id:sr,overflow:ar}:null,t.memoizedState={dehydrated:e,treeContext:n,retryLane:1073741824},n=Jt(18,null,null,0),n.stateNode=e,n.return=t,t.child=n,Pt=t,Lt=null,!0):!1;default:return!1}}function sp(t){return(t.mode&1)!==0&&(t.flags&128)===0}function ap(t){if(xe){var e=Lt;if(e){var n=e;if(!mv(t,e)){if(sp(t))throw Error(M(418));e=Gr(n.nextSibling);var r=Pt;e&&mv(t,e)?BE(r,n):(t.flags=t.flags&-4097|2,xe=!1,Pt=t)}}else{if(sp(t))throw Error(M(418));t.flags=t.flags&-4097|2,xe=!1,Pt=t}}}function pv(t){for(t=t.return;t!==null&&t.tag!==5&&t.tag!==3&&t.tag!==13;)t=t.return;Pt=t}function qu(t){if(t!==Pt)return!1;if(!xe)return pv(t),xe=!0,!1;var e;if((e=t.tag!==3)&&!(e=t.tag!==5)&&(e=t.type,e=e!=="head"&&e!=="body"&&!np(t.type,t.memoizedProps)),e&&(e=Lt)){if(sp(t))throw VE(),Error(M(418));for(;e;)BE(t,e),e=Gr(e.nextSibling)}if(pv(t),t.tag===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(M(317));e:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8){var n=t.data;if(n==="/$"){if(e===0){Lt=Gr(t.nextSibling);break e}e--}else n!=="$"&&n!=="$!"&&n!=="$?"||e++}t=t.nextSibling}Lt=null}}else Lt=Pt?Gr(t.stateNode.nextSibling):null;return!0}function VE(){for(var t=Lt;t;)t=Gr(t.nextSibling)}function qi(){Lt=Pt=null,xe=!1}function Up(t){Cn===null?Cn=[t]:Cn.push(t)}var wk=mr.ReactCurrentBatchConfig;function wn(t,e){if(t&&t.defaultProps){e=Re({},e),t=t.defaultProps;for(var n in t)e[n]===void 0&&(e[n]=t[n]);return e}return e}var Cc=eo(null),xc=null,Bi=null,jp=null;function $p(){jp=Bi=xc=null}function Gp(t){var e=Cc.current;we(Cc),t._currentValue=e}function lp(t,e,n){for(;t!==null;){var r=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,r!==null&&(r.childLanes|=e)):r!==null&&(r.childLanes&e)!==e&&(r.childLanes|=e),t===n)break;t=t.return}}function $i(t,e){xc=t,jp=Bi=null,t=t.dependencies,t!==null&&t.firstContext!==null&&(t.lanes&e&&(xt=!0),t.firstContext=null)}function en(t){var e=t._currentValue;if(jp!==t)if(t={context:t,memoizedValue:e,next:null},Bi===null){if(xc===null)throw Error(M(308));Bi=t,xc.dependencies={lanes:0,firstContext:t}}else Bi=Bi.next=t;return e}var ko=null;function Yp(t){ko===null?ko=[t]:ko.push(t)}function WE(t,e,n,r){var o=e.interleaved;return o===null?(n.next=n,Yp(e)):(n.next=o.next,o.next=n),e.interleaved=n,fr(t,r)}function fr(t,e){t.lanes|=e;var n=t.alternate;for(n!==null&&(n.lanes|=e),n=t,t=t.return;t!==null;)t.childLanes|=e,n=t.alternate,n!==null&&(n.childLanes|=e),n=t,t=t.return;return n.tag===3?n.stateNode:null}var Br=!1;function Zp(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function HE(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,effects:t.effects})}function lr(t,e){return{eventTime:t,lane:e,tag:0,payload:null,callback:null,next:null}}function Yr(t,e,n){var r=t.updateQueue;if(r===null)return null;if(r=r.shared,ce&2){var o=r.pending;return o===null?e.next=e:(e.next=o.next,o.next=e),r.pending=e,fr(t,n)}return o=r.interleaved,o===null?(e.next=e,Yp(r)):(e.next=o.next,o.next=e),r.interleaved=e,fr(t,n)}function rc(t,e,n){if(e=e.updateQueue,e!==null&&(e=e.shared,(n&4194240)!==0)){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,Mp(t,n)}}function hv(t,e){var n=t.updateQueue,r=t.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?o=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?o=i=e:i=i.next=e}else o=i=e;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:i,shared:r.shared,effects:r.effects},t.updateQueue=n;return}t=n.lastBaseUpdate,t===null?n.firstBaseUpdate=e:t.next=e,n.lastBaseUpdate=e}function Tc(t,e,n,r){var o=t.updateQueue;Br=!1;var i=o.firstBaseUpdate,s=o.lastBaseUpdate,a=o.shared.pending;if(a!==null){o.shared.pending=null;var l=a,u=l.next;l.next=null,s===null?i=u:s.next=u,s=l;var d=t.alternate;d!==null&&(d=d.updateQueue,a=d.lastBaseUpdate,a!==s&&(a===null?d.firstBaseUpdate=u:a.next=u,d.lastBaseUpdate=l))}if(i!==null){var f=o.baseState;s=0,d=u=l=null,a=i;do{var m=a.lane,c=a.eventTime;if((r&m)===m){d!==null&&(d=d.next={eventTime:c,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var y=t,v=a;switch(m=e,c=n,v.tag){case 1:if(y=v.payload,typeof y=="function"){f=y.call(c,f,m);break e}f=y;break e;case 3:y.flags=y.flags&-65537|128;case 0:if(y=v.payload,m=typeof y=="function"?y.call(c,f,m):y,m==null)break e;f=Re({},f,m);break e;case 2:Br=!0}}a.callback!==null&&a.lane!==0&&(t.flags|=64,m=o.effects,m===null?o.effects=[a]:m.push(a))}else c={eventTime:c,lane:m,tag:a.tag,payload:a.payload,callback:a.callback,next:null},d===null?(u=d=c,l=f):d=d.next=c,s|=m;if(a=a.next,a===null){if(a=o.shared.pending,a===null)break;m=a,a=m.next,m.next=null,o.lastBaseUpdate=m,o.shared.pending=null}}while(1);if(d===null&&(l=f),o.baseState=l,o.firstBaseUpdate=u,o.lastBaseUpdate=d,e=o.shared.interleaved,e!==null){o=e;do s|=o.lane,o=o.next;while(o!==e)}else i===null&&(o.shared.lanes=0);Ao|=s,t.lanes=s,t.memoizedState=f}}function yv(t,e,n){if(t=e.effects,e.effects=null,t!==null)for(e=0;e<t.length;e++){var r=t[e],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(M(191,o));o.call(r)}}}var zE=new Wv.Component().refs;function up(t,e,n,r){e=t.memoizedState,n=n(r,e),n=n==null?e:Re({},e,n),t.memoizedState=n,t.lanes===0&&(t.updateQueue.baseState=n)}var Wc={isMounted:function(t){return(t=t._reactInternals)?Bo(t)===t:!1},enqueueSetState:function(t,e,n){t=t._reactInternals;var r=yt(),o=qr(t),i=lr(r,o);i.payload=e,n!=null&&(i.callback=n),e=Yr(t,i,o),e!==null&&(Tn(e,t,o,r),rc(e,t,o))},enqueueReplaceState:function(t,e,n){t=t._reactInternals;var r=yt(),o=qr(t),i=lr(r,o);i.tag=1,i.payload=e,n!=null&&(i.callback=n),e=Yr(t,i,o),e!==null&&(Tn(e,t,o,r),rc(e,t,o))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var n=yt(),r=qr(t),o=lr(n,r);o.tag=2,e!=null&&(o.callback=e),e=Yr(t,o,r),e!==null&&(Tn(e,t,r,n),rc(e,t,r))}};function gv(t,e,n,r,o,i,s){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(r,i,s):e.prototype&&e.prototype.isPureReactComponent?!za(n,r)||!za(o,i):!0}function UE(t,e,n){var r=!1,o=Jr,i=e.contextType;return typeof i=="object"&&i!==null?i=en(i):(o=Ft(e)?No:ft.current,r=e.contextTypes,i=(r=r!=null)?Zi(t,o):Jr),e=new e(n,i),t.memoizedState=e.state!==null&&e.state!==void 0?e.state:null,e.updater=Wc,t.stateNode=e,e._reactInternals=t,r&&(t=t.stateNode,t.__reactInternalMemoizedUnmaskedChildContext=o,t.__reactInternalMemoizedMaskedChildContext=i),e}function Dv(t,e,n,r){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(n,r),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(n,r),e.state!==t&&Wc.enqueueReplaceState(e,e.state,null)}function cp(t,e,n,r){var o=t.stateNode;o.props=n,o.state=t.memoizedState,o.refs=zE,Zp(t);var i=e.contextType;typeof i=="object"&&i!==null?o.context=en(i):(i=Ft(e)?No:ft.current,o.context=Zi(t,i)),o.state=t.memoizedState,i=e.getDerivedStateFromProps,typeof i=="function"&&(up(t,e,i,n),o.state=t.memoizedState),typeof e.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(e=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),e!==o.state&&Wc.enqueueReplaceState(o,o.state,null),Tc(t,n,o,r),o.state=t.memoizedState),typeof o.componentDidMount=="function"&&(t.flags|=4194308)}function Da(t,e,n){if(t=n.ref,t!==null&&typeof t!="function"&&typeof t!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(M(309));var r=n.stateNode}if(!r)throw Error(M(147,t));var o=r,i=""+t;return e!==null&&e.ref!==null&&typeof e.ref=="function"&&e.ref._stringRef===i?e.ref:(e=function(s){var a=o.refs;a===zE&&(a=o.refs={}),s===null?delete a[i]:a[i]=s},e._stringRef=i,e)}if(typeof t!="string")throw Error(M(284));if(!n._owner)throw Error(M(290,t))}return t}function Ku(t,e){throw t=Object.prototype.toString.call(e),Error(M(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t))}function vv(t){var e=t._init;return e(t._payload)}function jE(t){function e(h,p){if(t){var E=h.deletions;E===null?(h.deletions=[p],h.flags|=16):E.push(p)}}function n(h,p){if(!t)return null;for(;p!==null;)e(h,p),p=p.sibling;return null}function r(h,p){for(h=new Map;p!==null;)p.key!==null?h.set(p.key,p):h.set(p.index,p),p=p.sibling;return h}function o(h,p){return h=Kr(h,p),h.index=0,h.sibling=null,h}function i(h,p,E){return h.index=E,t?(E=h.alternate,E!==null?(E=E.index,E<p?(h.flags|=2,p):E):(h.flags|=2,p)):(h.flags|=1048576,p)}function s(h){return t&&h.alternate===null&&(h.flags|=2),h}function a(h,p,E,D){return p===null||p.tag!==6?(p=Rm(E,h.mode,D),p.return=h,p):(p=o(p,E),p.return=h,p)}function l(h,p,E,D){var x=E.type;return x===bi?d(h,p,E.props.children,D,E.key):p!==null&&(p.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===Pr&&vv(x)===p.type)?(D=o(p,E.props),D.ref=Da(h,p,E),D.return=h,D):(D=uc(E.type,E.key,E.props,null,h.mode,D),D.ref=Da(h,p,E),D.return=h,D)}function u(h,p,E,D){return p===null||p.tag!==4||p.stateNode.containerInfo!==E.containerInfo||p.stateNode.implementation!==E.implementation?(p=Nm(E,h.mode,D),p.return=h,p):(p=o(p,E.children||[]),p.return=h,p)}function d(h,p,E,D,x){return p===null||p.tag!==7?(p=Ro(E,h.mode,D,x),p.return=h,p):(p=o(p,E),p.return=h,p)}function f(h,p,E){if(typeof p=="string"&&p!==""||typeof p=="number")return p=Rm(""+p,h.mode,E),p.return=h,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case Au:return E=uc(p.type,p.key,p.props,null,h.mode,E),E.ref=Da(h,null,p),E.return=h,E;case Oi:return p=Nm(p,h.mode,E),p.return=h,p;case Pr:var D=p._init;return f(h,D(p._payload),E)}if(Ca(p)||pa(p))return p=Ro(p,h.mode,E,null),p.return=h,p;Ku(h,p)}return null}function m(h,p,E,D){var x=p!==null?p.key:null;if(typeof E=="string"&&E!==""||typeof E=="number")return x!==null?null:a(h,p,""+E,D);if(typeof E=="object"&&E!==null){switch(E.$$typeof){case Au:return E.key===x?l(h,p,E,D):null;case Oi:return E.key===x?u(h,p,E,D):null;case Pr:return x=E._init,m(h,p,x(E._payload),D)}if(Ca(E)||pa(E))return x!==null?null:d(h,p,E,D,null);Ku(h,E)}return null}function c(h,p,E,D,x){if(typeof D=="string"&&D!==""||typeof D=="number")return h=h.get(E)||null,a(p,h,""+D,x);if(typeof D=="object"&&D!==null){switch(D.$$typeof){case Au:return h=h.get(D.key===null?E:D.key)||null,l(p,h,D,x);case Oi:return h=h.get(D.key===null?E:D.key)||null,u(p,h,D,x);case Pr:var O=D._init;return c(h,p,E,O(D._payload),x)}if(Ca(D)||pa(D))return h=h.get(E)||null,d(p,h,D,x,null);Ku(p,D)}return null}function y(h,p,E,D){for(var x=null,O=null,_=p,k=p=0,L=null;_!==null&&k<E.length;k++){_.index>k?(L=_,_=null):L=_.sibling;var N=m(h,_,E[k],D);if(N===null){_===null&&(_=L);break}t&&_&&N.alternate===null&&e(h,_),p=i(N,p,k),O===null?x=N:O.sibling=N,O=N,_=L}if(k===E.length)return n(h,_),xe&&To(h,k),x;if(_===null){for(;k<E.length;k++)_=f(h,E[k],D),_!==null&&(p=i(_,p,k),O===null?x=_:O.sibling=_,O=_);return xe&&To(h,k),x}for(_=r(h,_);k<E.length;k++)L=c(_,h,k,E[k],D),L!==null&&(t&&L.alternate!==null&&_.delete(L.key===null?k:L.key),p=i(L,p,k),O===null?x=L:O.sibling=L,O=L);return t&&_.forEach(function(ee){return e(h,ee)}),xe&&To(h,k),x}function v(h,p,E,D){var x=pa(E);if(typeof x!="function")throw Error(M(150));if(E=x.call(E),E==null)throw Error(M(151));for(var O=x=null,_=p,k=p=0,L=null,N=E.next();_!==null&&!N.done;k++,N=E.next()){_.index>k?(L=_,_=null):L=_.sibling;var ee=m(h,_,N.value,D);if(ee===null){_===null&&(_=L);break}t&&_&&ee.alternate===null&&e(h,_),p=i(ee,p,k),O===null?x=ee:O.sibling=ee,O=ee,_=L}if(N.done)return n(h,_),xe&&To(h,k),x;if(_===null){for(;!N.done;k++,N=E.next())N=f(h,N.value,D),N!==null&&(p=i(N,p,k),O===null?x=N:O.sibling=N,O=N);return xe&&To(h,k),x}for(_=r(h,_);!N.done;k++,N=E.next())N=c(_,h,k,N.value,D),N!==null&&(t&&N.alternate!==null&&_.delete(N.key===null?k:N.key),p=i(N,p,k),O===null?x=N:O.sibling=N,O=N);return t&&_.forEach(function(oe){return e(h,oe)}),xe&&To(h,k),x}function S(h,p,E,D){if(typeof E=="object"&&E!==null&&E.type===bi&&E.key===null&&(E=E.props.children),typeof E=="object"&&E!==null){switch(E.$$typeof){case Au:e:{for(var x=E.key,O=p;O!==null;){if(O.key===x){if(x=E.type,x===bi){if(O.tag===7){n(h,O.sibling),p=o(O,E.props.children),p.return=h,h=p;break e}}else if(O.elementType===x||typeof x=="object"&&x!==null&&x.$$typeof===Pr&&vv(x)===O.type){n(h,O.sibling),p=o(O,E.props),p.ref=Da(h,O,E),p.return=h,h=p;break e}n(h,O);break}else e(h,O);O=O.sibling}E.type===bi?(p=Ro(E.props.children,h.mode,D,E.key),p.return=h,h=p):(D=uc(E.type,E.key,E.props,null,h.mode,D),D.ref=Da(h,p,E),D.return=h,h=D)}return s(h);case Oi:e:{for(O=E.key;p!==null;){if(p.key===O)if(p.tag===4&&p.stateNode.containerInfo===E.containerInfo&&p.stateNode.implementation===E.implementation){n(h,p.sibling),p=o(p,E.children||[]),p.return=h,h=p;break e}else{n(h,p);break}else e(h,p);p=p.sibling}p=Nm(E,h.mode,D),p.return=h,h=p}return s(h);case Pr:return O=E._init,S(h,p,O(E._payload),D)}if(Ca(E))return y(h,p,E,D);if(pa(E))return v(h,p,E,D);Ku(h,E)}return typeof E=="string"&&E!==""||typeof E=="number"?(E=""+E,p!==null&&p.tag===6?(n(h,p.sibling),p=o(p,E),p.return=h,h=p):(n(h,p),p=Rm(E,h.mode,D),p.return=h,h=p),s(h)):n(h,p)}return S}var Ki=jE(!0),$E=jE(!1),tl={},jn=eo(tl),Ga=eo(tl),Ya=eo(tl);function Oo(t){if(t===tl)throw Error(M(174));return t}function qp(t,e){switch(ve(Ya,e),ve(Ga,t),ve(jn,tl),t=e.nodeType,t){case 9:case 11:e=(e=e.documentElement)?e.namespaceURI:zm(null,"");break;default:t=t===8?e.parentNode:e,e=t.namespaceURI||null,t=t.tagName,e=zm(e,t)}we(jn),ve(jn,e)}function Qi(){we(jn),we(Ga),we(Ya)}function GE(t){Oo(Ya.current);var e=Oo(jn.current),n=zm(e,t.type);e!==n&&(ve(Ga,t),ve(jn,n))}function Kp(t){Ga.current===t&&(we(jn),we(Ga))}var Oe=eo(0);function Fc(t){for(var e=t;e!==null;){if(e.tag===13){var n=e.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if(e.flags&128)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}var Tm=[];function Qp(){for(var t=0;t<Tm.length;t++)Tm[t]._workInProgressVersionPrimary=null;Tm.length=0}var oc=mr.ReactCurrentDispatcher,Fm=mr.ReactCurrentBatchConfig,Io=0,be=null,Ge=null,Je=null,_c=!1,Ra=!1,Za=0,Sk=0;function lt(){throw Error(M(321))}function Jp(t,e){if(e===null)return!1;for(var n=0;n<e.length&&n<t.length;n++)if(!Fn(t[n],e[n]))return!1;return!0}function Xp(t,e,n,r,o,i){if(Io=i,be=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,oc.current=t===null||t.memoizedState===null?Fk:_k,t=n(r,o),Ra){i=0;do{if(Ra=!1,Za=0,25<=i)throw Error(M(301));i+=1,Je=Ge=null,e.updateQueue=null,oc.current=kk,t=n(r,o)}while(Ra)}if(oc.current=kc,e=Ge!==null&&Ge.next!==null,Io=0,Je=Ge=be=null,_c=!1,e)throw Error(M(300));return t}function eh(){var t=Za!==0;return Za=0,t}function Hn(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Je===null?be.memoizedState=Je=t:Je=Je.next=t,Je}function tn(){if(Ge===null){var t=be.alternate;t=t!==null?t.memoizedState:null}else t=Ge.next;var e=Je===null?be.memoizedState:Je.next;if(e!==null)Je=e,Ge=t;else{if(t===null)throw Error(M(310));Ge=t,t={memoizedState:Ge.memoizedState,baseState:Ge.baseState,baseQueue:Ge.baseQueue,queue:Ge.queue,next:null},Je===null?be.memoizedState=Je=t:Je=Je.next=t}return Je}function qa(t,e){return typeof e=="function"?e(t):e}function _m(t){var e=tn(),n=e.queue;if(n===null)throw Error(M(311));n.lastRenderedReducer=t;var r=Ge,o=r.baseQueue,i=n.pending;if(i!==null){if(o!==null){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(o!==null){i=o.next,r=r.baseState;var a=s=null,l=null,u=i;do{var d=u.lane;if((Io&d)===d)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:t(r,u.action);else{var f={lane:d,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=f,s=r):l=l.next=f,be.lanes|=d,Ao|=d}u=u.next}while(u!==null&&u!==i);l===null?s=r:l.next=a,Fn(r,e.memoizedState)||(xt=!0),e.memoizedState=r,e.baseState=s,e.baseQueue=l,n.lastRenderedState=r}if(t=n.interleaved,t!==null){o=t;do i=o.lane,be.lanes|=i,Ao|=i,o=o.next;while(o!==t)}else o===null&&(n.lanes=0);return[e.memoizedState,n.dispatch]}function km(t){var e=tn(),n=e.queue;if(n===null)throw Error(M(311));n.lastRenderedReducer=t;var r=n.dispatch,o=n.pending,i=e.memoizedState;if(o!==null){n.pending=null;var s=o=o.next;do i=t(i,s.action),s=s.next;while(s!==o);Fn(i,e.memoizedState)||(xt=!0),e.memoizedState=i,e.baseQueue===null&&(e.baseState=i),n.lastRenderedState=i}return[i,r]}function YE(){}function ZE(t,e){var n=be,r=tn(),o=e(),i=!Fn(r.memoizedState,o);if(i&&(r.memoizedState=o,xt=!0),r=r.queue,th(QE.bind(null,n,r,t),[t]),r.getSnapshot!==e||i||Je!==null&&Je.memoizedState.tag&1){if(n.flags|=2048,Ka(9,KE.bind(null,n,r,o,e),void 0,null),Xe===null)throw Error(M(349));Io&30||qE(n,e,o)}return o}function qE(t,e,n){t.flags|=16384,t={getSnapshot:e,value:n},e=be.updateQueue,e===null?(e={lastEffect:null,stores:null},be.updateQueue=e,e.stores=[t]):(n=e.stores,n===null?e.stores=[t]:n.push(t))}function KE(t,e,n,r){e.value=n,e.getSnapshot=r,JE(e)&&XE(t)}function QE(t,e,n){return n(function(){JE(e)&&XE(t)})}function JE(t){var e=t.getSnapshot;t=t.value;try{var n=e();return!Fn(t,n)}catch(r){return!0}}function XE(t){var e=fr(t,1);e!==null&&Tn(e,t,1,-1)}function Ev(t){var e=Hn();return typeof t=="function"&&(t=t()),e.memoizedState=e.baseState=t,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:qa,lastRenderedState:t},e.queue=t,t=t.dispatch=Tk.bind(null,be,t),[e.memoizedState,t]}function Ka(t,e,n,r){return t={tag:t,create:e,destroy:n,deps:r,next:null},e=be.updateQueue,e===null?(e={lastEffect:null,stores:null},be.updateQueue=e,e.lastEffect=t.next=t):(n=e.lastEffect,n===null?e.lastEffect=t.next=t:(r=n.next,n.next=t,t.next=r,e.lastEffect=t)),t}function ew(){return tn().memoizedState}function ic(t,e,n,r){var o=Hn();be.flags|=t,o.memoizedState=Ka(1|e,n,void 0,r===void 0?null:r)}function Hc(t,e,n,r){var o=tn();r=r===void 0?null:r;var i=void 0;if(Ge!==null){var s=Ge.memoizedState;if(i=s.destroy,r!==null&&Jp(r,s.deps)){o.memoizedState=Ka(e,n,i,r);return}}be.flags|=t,o.memoizedState=Ka(1|e,n,i,r)}function wv(t,e){return ic(8390656,8,t,e)}function th(t,e){return Hc(2048,8,t,e)}function tw(t,e){return Hc(4,2,t,e)}function nw(t,e){return Hc(4,4,t,e)}function rw(t,e){if(typeof e=="function")return t=t(),e(t),function(){e(null)};if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function ow(t,e,n){return n=n!=null?n.concat([t]):null,Hc(4,4,rw.bind(null,e,t),n)}function nh(){}function iw(t,e){var n=tn();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&Jp(e,r[1])?r[0]:(n.memoizedState=[t,e],t)}function sw(t,e){var n=tn();e=e===void 0?null:e;var r=n.memoizedState;return r!==null&&e!==null&&Jp(e,r[1])?r[0]:(t=t(),n.memoizedState=[t,e],t)}function aw(t,e,n){return Io&21?(Fn(n,e)||(n=cE(),be.lanes|=n,Ao|=n,t.baseState=!0),e):(t.baseState&&(t.baseState=!1,xt=!0),t.memoizedState=n)}function Ck(t,e){var n=pe;pe=n!==0&&4>n?n:4,t(!0);var r=Fm.transition;Fm.transition={};try{t(!1),e()}finally{pe=n,Fm.transition=r}}function lw(){return tn().memoizedState}function xk(t,e,n){var r=qr(t);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},uw(t))cw(e,n);else if(n=WE(t,e,n,r),n!==null){var o=yt();Tn(n,t,r,o),fw(n,e,r)}}function Tk(t,e,n){var r=qr(t),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(uw(t))cw(e,o);else{var i=t.alternate;if(t.lanes===0&&(i===null||i.lanes===0)&&(i=e.lastRenderedReducer,i!==null))try{var s=e.lastRenderedState,a=i(s,n);if(o.hasEagerState=!0,o.eagerState=a,Fn(a,s)){var l=e.interleaved;l===null?(o.next=o,Yp(e)):(o.next=l.next,l.next=o),e.interleaved=o;return}}catch(u){}finally{}n=WE(t,e,o,r),n!==null&&(o=yt(),Tn(n,t,r,o),fw(n,e,r))}}function uw(t){var e=t.alternate;return t===be||e!==null&&e===be}function cw(t,e){Ra=_c=!0;var n=t.pending;n===null?e.next=e:(e.next=n.next,n.next=e),t.pending=e}function fw(t,e,n){if(n&4194240){var r=e.lanes;r&=t.pendingLanes,n|=r,e.lanes=n,Mp(t,n)}}var kc={readContext:en,useCallback:lt,useContext:lt,useEffect:lt,useImperativeHandle:lt,useInsertionEffect:lt,useLayoutEffect:lt,useMemo:lt,useReducer:lt,useRef:lt,useState:lt,useDebugValue:lt,useDeferredValue:lt,useTransition:lt,useMutableSource:lt,useSyncExternalStore:lt,useId:lt,unstable_isNewReconciler:!1},Fk={readContext:en,useCallback:function(t,e){return Hn().memoizedState=[t,e===void 0?null:e],t},useContext:en,useEffect:wv,useImperativeHandle:function(t,e,n){return n=n!=null?n.concat([t]):null,ic(4194308,4,rw.bind(null,e,t),n)},useLayoutEffect:function(t,e){return ic(4194308,4,t,e)},useInsertionEffect:function(t,e){return ic(4,2,t,e)},useMemo:function(t,e){var n=Hn();return e=e===void 0?null:e,t=t(),n.memoizedState=[t,e],t},useReducer:function(t,e,n){var r=Hn();return e=n!==void 0?n(e):e,r.memoizedState=r.baseState=e,t={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:e},r.queue=t,t=t.dispatch=xk.bind(null,be,t),[r.memoizedState,t]},useRef:function(t){var e=Hn();return t={current:t},e.memoizedState=t},useState:Ev,useDebugValue:nh,useDeferredValue:function(t){return Hn().memoizedState=t},useTransition:function(){var t=Ev(!1),e=t[0];return t=Ck.bind(null,t[1]),Hn().memoizedState=t,[e,t]},useMutableSource:function(){},useSyncExternalStore:function(t,e,n){var r=be,o=Hn();if(xe){if(n===void 0)throw Error(M(407));n=n()}else{if(n=e(),Xe===null)throw Error(M(349));Io&30||qE(r,e,n)}o.memoizedState=n;var i={value:n,getSnapshot:e};return o.queue=i,wv(QE.bind(null,r,i,t),[t]),r.flags|=2048,Ka(9,KE.bind(null,r,i,n,e),void 0,null),n},useId:function(){var t=Hn(),e=Xe.identifierPrefix;if(xe){var n=ar,r=sr;n=(r&~(1<<32-xn(r)-1)).toString(32)+n,e=":"+e+"R"+n,n=Za++,0<n&&(e+="H"+n.toString(32)),e+=":"}else n=Sk++,e=":"+e+"r"+n.toString(32)+":";return t.memoizedState=e},unstable_isNewReconciler:!1},_k={readContext:en,useCallback:iw,useContext:en,useEffect:th,useImperativeHandle:ow,useInsertionEffect:tw,useLayoutEffect:nw,useMemo:sw,useReducer:_m,useRef:ew,useState:function(){return _m(qa)},useDebugValue:nh,useDeferredValue:function(t){var e=tn();return aw(e,Ge.memoizedState,t)},useTransition:function(){var t=_m(qa)[0],e=tn().memoizedState;return[t,e]},useMutableSource:YE,useSyncExternalStore:ZE,useId:lw,unstable_isNewReconciler:!1},kk={readContext:en,useCallback:iw,useContext:en,useEffect:th,useImperativeHandle:ow,useInsertionEffect:tw,useLayoutEffect:nw,useMemo:sw,useReducer:km,useRef:ew,useState:function(){return km(qa)},useDebugValue:nh,useDeferredValue:function(t){var e=tn();return Ge===null?e.memoizedState=t:aw(e,Ge.memoizedState,t)},useTransition:function(){var t=km(qa)[0],e=tn().memoizedState;return[t,e]},useMutableSource:YE,useSyncExternalStore:ZE,useId:lw,unstable_isNewReconciler:!1};function Ji(t,e){try{var n="",r=e;do n+=o_(r),r=r.return;while(r);var o=n}catch(i){o=`
Error generating stack: `+i.message+`
`+i.stack}return{value:t,source:e,stack:o,digest:null}}function Om(t,e,n){return{value:t,source:null,stack:n!=null?n:null,digest:e!=null?e:null}}function fp(t,e){try{console.error(e.value)}catch(n){setTimeout(function(){throw n})}}var Ok=typeof WeakMap=="function"?WeakMap:Map;function dw(t,e,n){n=lr(-1,n),n.tag=3,n.payload={element:null};var r=e.value;return n.callback=function(){bc||(bc=!0,wp=r),fp(t,e)},n}function mw(t,e,n){n=lr(-1,n),n.tag=3;var r=t.type.getDerivedStateFromError;if(typeof r=="function"){var o=e.value;n.payload=function(){return r(o)},n.callback=function(){fp(t,e)}}var i=t.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){fp(t,e),typeof r!="function"&&(Zr===null?Zr=new Set([this]):Zr.add(this));var s=e.stack;this.componentDidCatch(e.value,{componentStack:s!==null?s:""})}),n}function Sv(t,e,n){var r=t.pingCache;if(r===null){r=t.pingCache=new Ok;var o=new Set;r.set(e,o)}else o=r.get(e),o===void 0&&(o=new Set,r.set(e,o));o.has(n)||(o.add(n),t=Uk.bind(null,t,e,n),e.then(t,t))}function Cv(t){do{var e;if((e=t.tag===13)&&(e=t.memoizedState,e=e!==null?e.dehydrated!==null:!0),e)return t;t=t.return}while(t!==null);return null}function xv(t,e,n,r,o){return t.mode&1?(t.flags|=65536,t.lanes=o,t):(t===e?t.flags|=65536:(t.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(e=lr(-1,1),e.tag=2,Yr(n,e,1))),n.lanes|=1),t)}var bk=mr.ReactCurrentOwner,xt=!1;function ht(t,e,n,r){e.child=t===null?$E(e,null,n,r):Ki(e,t.child,n,r)}function Tv(t,e,n,r,o){n=n.render;var i=e.ref;return $i(e,o),r=Xp(t,e,n,r,i,o),n=eh(),t!==null&&!xt?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~o,dr(t,e,o)):(xe&&n&&Hp(e),e.flags|=1,ht(t,e,r,o),e.child)}function Fv(t,e,n,r,o){if(t===null){var i=n.type;return typeof i=="function"&&!ch(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(e.tag=15,e.type=i,pw(t,e,i,r,o)):(t=uc(n.type,null,r,e,e.mode,o),t.ref=e.ref,t.return=e,e.child=t)}if(i=t.child,!(t.lanes&o)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:za,n(s,r)&&t.ref===e.ref)return dr(t,e,o)}return e.flags|=1,t=Kr(i,r),t.ref=e.ref,t.return=e,e.child=t}function pw(t,e,n,r,o){if(t!==null){var i=t.memoizedProps;if(za(i,r)&&t.ref===e.ref)if(xt=!1,e.pendingProps=r=i,(t.lanes&o)!==0)t.flags&131072&&(xt=!0);else return e.lanes=t.lanes,dr(t,e,o)}return dp(t,e,n,r,o)}function hw(t,e,n){var r=e.pendingProps,o=r.children,i=t!==null?t.memoizedState:null;if(r.mode==="hidden")if(!(e.mode&1))e.memoizedState={baseLanes:0,cachePool:null,transitions:null},ve(Wi,At),At|=n;else{if(!(n&1073741824))return t=i!==null?i.baseLanes|n:n,e.lanes=e.childLanes=1073741824,e.memoizedState={baseLanes:t,cachePool:null,transitions:null},e.updateQueue=null,ve(Wi,At),At|=t,null;e.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=i!==null?i.baseLanes:n,ve(Wi,At),At|=r}else i!==null?(r=i.baseLanes|n,e.memoizedState=null):r=n,ve(Wi,At),At|=r;return ht(t,e,o,n),e.child}function yw(t,e){var n=e.ref;(t===null&&n!==null||t!==null&&t.ref!==n)&&(e.flags|=512,e.flags|=2097152)}function dp(t,e,n,r,o){var i=Ft(n)?No:ft.current;return i=Zi(e,i),$i(e,o),n=Xp(t,e,n,r,i,o),r=eh(),t!==null&&!xt?(e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~o,dr(t,e,o)):(xe&&r&&Hp(e),e.flags|=1,ht(t,e,n,o),e.child)}function _v(t,e,n,r,o){if(Ft(n)){var i=!0;Ec(e)}else i=!1;if($i(e,o),e.stateNode===null)sc(t,e),UE(e,n,r),cp(e,n,r,o),r=!0;else if(t===null){var s=e.stateNode,a=e.memoizedProps;s.props=a;var l=s.context,u=n.contextType;typeof u=="object"&&u!==null?u=en(u):(u=Ft(n)?No:ft.current,u=Zi(e,u));var d=n.getDerivedStateFromProps,f=typeof d=="function"||typeof s.getSnapshotBeforeUpdate=="function";f||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||l!==u)&&Dv(e,s,r,u),Br=!1;var m=e.memoizedState;s.state=m,Tc(e,r,s,o),l=e.memoizedState,a!==r||m!==l||Tt.current||Br?(typeof d=="function"&&(up(e,n,d,r),l=e.memoizedState),(a=Br||gv(e,n,a,r,m,l,u))?(f||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(e.flags|=4194308)):(typeof s.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=r,e.memoizedState=l),s.props=r,s.state=l,s.context=u,r=a):(typeof s.componentDidMount=="function"&&(e.flags|=4194308),r=!1)}else{s=e.stateNode,HE(t,e),a=e.memoizedProps,u=e.type===e.elementType?a:wn(e.type,a),s.props=u,f=e.pendingProps,m=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=en(l):(l=Ft(n)?No:ft.current,l=Zi(e,l));var c=n.getDerivedStateFromProps;(d=typeof c=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==f||m!==l)&&Dv(e,s,r,l),Br=!1,m=e.memoizedState,s.state=m,Tc(e,r,s,o);var y=e.memoizedState;a!==f||m!==y||Tt.current||Br?(typeof c=="function"&&(up(e,n,c,r),y=e.memoizedState),(u=Br||gv(e,n,u,r,m,y,l)||!1)?(d||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,y,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,y,l)),typeof s.componentDidUpdate=="function"&&(e.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===t.memoizedProps&&m===t.memoizedState||(e.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&m===t.memoizedState||(e.flags|=1024),e.memoizedProps=r,e.memoizedState=y),s.props=r,s.state=y,s.context=l,r=u):(typeof s.componentDidUpdate!="function"||a===t.memoizedProps&&m===t.memoizedState||(e.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===t.memoizedProps&&m===t.memoizedState||(e.flags|=1024),r=!1)}return mp(t,e,n,r,i,o)}function mp(t,e,n,r,o,i){yw(t,e);var s=(e.flags&128)!==0;if(!r&&!s)return o&&dv(e,n,!1),dr(t,e,i);r=e.stateNode,bk.current=e;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return e.flags|=1,t!==null&&s?(e.child=Ki(e,t.child,null,i),e.child=Ki(e,null,a,i)):ht(t,e,a,i),e.memoizedState=r.state,o&&dv(e,n,!0),e.child}function gw(t){var e=t.stateNode;e.pendingContext?fv(t,e.pendingContext,e.pendingContext!==e.context):e.context&&fv(t,e.context,!1),qp(t,e.containerInfo)}function kv(t,e,n,r,o){return qi(),Up(o),e.flags|=256,ht(t,e,n,r),e.child}var pp={dehydrated:null,treeContext:null,retryLane:0};function hp(t){return{baseLanes:t,cachePool:null,transitions:null}}function Dw(t,e,n){var r=e.pendingProps,o=Oe.current,i=!1,s=(e.flags&128)!==0,a;if((a=s)||(a=t!==null&&t.memoizedState===null?!1:(o&2)!==0),a?(i=!0,e.flags&=-129):(t===null||t.memoizedState!==null)&&(o|=1),ve(Oe,o&1),t===null)return ap(e),t=e.memoizedState,t!==null&&(t=t.dehydrated,t!==null)?(e.mode&1?t.data==="$!"?e.lanes=8:e.lanes=1073741824:e.lanes=1,null):(s=r.children,t=r.fallback,i?(r=e.mode,i=e.child,s={mode:"hidden",children:s},!(r&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=jc(s,r,0,null),t=Ro(t,r,n,null),i.return=e,t.return=e,i.sibling=t,e.child=i,e.child.memoizedState=hp(n),e.memoizedState=pp,t):rh(e,s));if(o=t.memoizedState,o!==null&&(a=o.dehydrated,a!==null))return Rk(t,e,s,r,a,o,n);if(i){i=r.fallback,s=e.mode,o=t.child,a=o.sibling;var l={mode:"hidden",children:r.children};return!(s&1)&&e.child!==o?(r=e.child,r.childLanes=0,r.pendingProps=l,e.deletions=null):(r=Kr(o,l),r.subtreeFlags=o.subtreeFlags&14680064),a!==null?i=Kr(a,i):(i=Ro(i,s,n,null),i.flags|=2),i.return=e,r.return=e,r.sibling=i,e.child=r,r=i,i=e.child,s=t.child.memoizedState,s=s===null?hp(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=t.childLanes&~n,e.memoizedState=pp,r}return i=t.child,t=i.sibling,r=Kr(i,{mode:"visible",children:r.children}),!(e.mode&1)&&(r.lanes=n),r.return=e,r.sibling=null,t!==null&&(n=e.deletions,n===null?(e.deletions=[t],e.flags|=16):n.push(t)),e.child=r,e.memoizedState=null,r}function rh(t,e){return e=jc({mode:"visible",children:e},t.mode,0,null),e.return=t,t.child=e}function Qu(t,e,n,r){return r!==null&&Up(r),Ki(e,t.child,null,n),t=rh(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function Rk(t,e,n,r,o,i,s){if(n)return e.flags&256?(e.flags&=-257,r=Om(Error(M(422))),Qu(t,e,s,r)):e.memoizedState!==null?(e.child=t.child,e.flags|=128,null):(i=r.fallback,o=e.mode,r=jc({mode:"visible",children:r.children},o,0,null),i=Ro(i,o,s,null),i.flags|=2,r.return=e,i.return=e,r.sibling=i,e.child=r,e.mode&1&&Ki(e,t.child,null,s),e.child.memoizedState=hp(s),e.memoizedState=pp,i);if(!(e.mode&1))return Qu(t,e,s,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var a=r.dgst;return r=a,i=Error(M(419)),r=Om(i,r,void 0),Qu(t,e,s,r)}if(a=(s&t.childLanes)!==0,xt||a){if(r=Xe,r!==null){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|s)?0:o,o!==0&&o!==i.retryLane&&(i.retryLane=o,fr(t,o),Tn(r,t,o,-1))}return uh(),r=Om(Error(M(421))),Qu(t,e,s,r)}return o.data==="$?"?(e.flags|=128,e.child=t.child,e=jk.bind(null,t),o._reactRetry=e,null):(t=i.treeContext,Lt=Gr(o.nextSibling),Pt=e,xe=!0,Cn=null,t!==null&&(Kt[Qt++]=sr,Kt[Qt++]=ar,Kt[Qt++]=Mo,sr=t.id,ar=t.overflow,Mo=e),e=rh(e,r.children),e.flags|=4096,e)}function Ov(t,e,n){t.lanes|=e;var r=t.alternate;r!==null&&(r.lanes|=e),lp(t.return,e,n)}function bm(t,e,n,r,o){var i=t.memoizedState;i===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(i.isBackwards=e,i.rendering=null,i.renderingStartTime=0,i.last=r,i.tail=n,i.tailMode=o)}function vw(t,e,n){var r=e.pendingProps,o=r.revealOrder,i=r.tail;if(ht(t,e,r.children,n),r=Oe.current,r&2)r=r&1|2,e.flags|=128;else{if(t!==null&&t.flags&128)e:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&Ov(t,n,e);else if(t.tag===19)Ov(t,n,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;t=t.return}t.sibling.return=t.return,t=t.sibling}r&=1}if(ve(Oe,r),!(e.mode&1))e.memoizedState=null;else switch(o){case"forwards":for(n=e.child,o=null;n!==null;)t=n.alternate,t!==null&&Fc(t)===null&&(o=n),n=n.sibling;n=o,n===null?(o=e.child,e.child=null):(o=n.sibling,n.sibling=null),bm(e,!1,o,n,i);break;case"backwards":for(n=null,o=e.child,e.child=null;o!==null;){if(t=o.alternate,t!==null&&Fc(t)===null){e.child=o;break}t=o.sibling,o.sibling=n,n=o,o=t}bm(e,!0,n,null,i);break;case"together":bm(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function sc(t,e){!(e.mode&1)&&t!==null&&(t.alternate=null,e.alternate=null,e.flags|=2)}function dr(t,e,n){if(t!==null&&(e.dependencies=t.dependencies),Ao|=e.lanes,!(n&e.childLanes))return null;if(t!==null&&e.child!==t.child)throw Error(M(153));if(e.child!==null){for(t=e.child,n=Kr(t,t.pendingProps),e.child=n,n.return=e;t.sibling!==null;)t=t.sibling,n=n.sibling=Kr(t,t.pendingProps),n.return=e;n.sibling=null}return e.child}function Nk(t,e,n){switch(e.tag){case 3:gw(e),qi();break;case 5:GE(e);break;case 1:Ft(e.type)&&Ec(e);break;case 4:qp(e,e.stateNode.containerInfo);break;case 10:var r=e.type._context,o=e.memoizedProps.value;ve(Cc,r._currentValue),r._currentValue=o;break;case 13:if(r=e.memoizedState,r!==null)return r.dehydrated!==null?(ve(Oe,Oe.current&1),e.flags|=128,null):n&e.child.childLanes?Dw(t,e,n):(ve(Oe,Oe.current&1),t=dr(t,e,n),t!==null?t.sibling:null);ve(Oe,Oe.current&1);break;case 19:if(r=(n&e.childLanes)!==0,t.flags&128){if(r)return vw(t,e,n);e.flags|=128}if(o=e.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ve(Oe,Oe.current),r)break;return null;case 22:case 23:return e.lanes=0,hw(t,e,n)}return dr(t,e,n)}var Ew,yp,ww,Sw;Ew=function(t,e){for(var n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};yp=function(){};ww=function(t,e,n,r){var o=t.memoizedProps;if(o!==r){t=e.stateNode,Oo(jn.current);var i=null;switch(n){case"input":o=Bm(t,o),r=Bm(t,r),i=[];break;case"select":o=Re({},o,{value:void 0}),r=Re({},r,{value:void 0}),i=[];break;case"textarea":o=Hm(t,o),r=Hm(t,r),i=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(t.onclick=Dc)}Um(n,r);var s;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var a=o[u];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Aa.hasOwnProperty(u)?i||(i=[]):(i=i||[]).push(u,null));for(u in r){var l=r[u];if(a=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(i||(i=[]),i.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(i=i||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Aa.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&Ee("scroll",t),i||a===l||(i=[])):(i=i||[]).push(u,l))}n&&(i=i||[]).push("style",n);var u=i;(e.updateQueue=u)&&(e.flags|=4)}};Sw=function(t,e,n,r){n!==r&&(e.flags|=4)};function va(t,e){if(!xe)switch(t.tailMode){case"hidden":e=t.tail;for(var n=null;e!==null;)e.alternate!==null&&(n=e),e=e.sibling;n===null?t.tail=null:n.sibling=null;break;case"collapsed":n=t.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:r.sibling=null}}function ut(t){var e=t.alternate!==null&&t.alternate.child===t.child,n=0,r=0;if(e)for(var o=t.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=t,o=o.sibling;else for(o=t.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=t,o=o.sibling;return t.subtreeFlags|=r,t.childLanes=n,e}function Mk(t,e,n){var r=e.pendingProps;switch(zp(e),e.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return ut(e),null;case 1:return Ft(e.type)&&vc(),ut(e),null;case 3:return r=e.stateNode,Qi(),we(Tt),we(ft),Qp(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(t===null||t.child===null)&&(qu(e)?e.flags|=4:t===null||t.memoizedState.isDehydrated&&!(e.flags&256)||(e.flags|=1024,Cn!==null&&(xp(Cn),Cn=null))),yp(t,e),ut(e),null;case 5:Kp(e);var o=Oo(Ya.current);if(n=e.type,t!==null&&e.stateNode!=null)ww(t,e,n,r,o),t.ref!==e.ref&&(e.flags|=512,e.flags|=2097152);else{if(!r){if(e.stateNode===null)throw Error(M(166));return ut(e),null}if(t=Oo(jn.current),qu(e)){r=e.stateNode,n=e.type;var i=e.memoizedProps;switch(r[zn]=e,r[$a]=i,t=(e.mode&1)!==0,n){case"dialog":Ee("cancel",r),Ee("close",r);break;case"iframe":case"object":case"embed":Ee("load",r);break;case"video":case"audio":for(o=0;o<Ta.length;o++)Ee(Ta[o],r);break;case"source":Ee("error",r);break;case"img":case"image":case"link":Ee("error",r),Ee("load",r);break;case"details":Ee("toggle",r);break;case"input":PD(r,i),Ee("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Ee("invalid",r);break;case"textarea":VD(r,i),Ee("invalid",r)}Um(n,i),o=null;for(var s in i)if(i.hasOwnProperty(s)){var a=i[s];s==="children"?typeof a=="string"?r.textContent!==a&&(i.suppressHydrationWarning!==!0&&Zu(r.textContent,a,t),o=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Zu(r.textContent,a,t),o=["children",""+a]):Aa.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&Ee("scroll",r)}switch(n){case"input":Lu(r),BD(r,i,!0);break;case"textarea":Lu(r),WD(r);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(r.onclick=Dc)}r=o,e.updateQueue=r,r!==null&&(e.flags|=4)}else{s=o.nodeType===9?o:o.ownerDocument,t==="http://www.w3.org/1999/xhtml"&&(t=qv(n)),t==="http://www.w3.org/1999/xhtml"?n==="script"?(t=s.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild)):typeof r.is=="string"?t=s.createElement(n,{is:r.is}):(t=s.createElement(n),n==="select"&&(s=t,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):t=s.createElementNS(t,n),t[zn]=e,t[$a]=r,Ew(t,e,!1,!1),e.stateNode=t;e:{switch(s=jm(n,r),n){case"dialog":Ee("cancel",t),Ee("close",t),o=r;break;case"iframe":case"object":case"embed":Ee("load",t),o=r;break;case"video":case"audio":for(o=0;o<Ta.length;o++)Ee(Ta[o],t);o=r;break;case"source":Ee("error",t),o=r;break;case"img":case"image":case"link":Ee("error",t),Ee("load",t),o=r;break;case"details":Ee("toggle",t),o=r;break;case"input":PD(t,r),o=Bm(t,r),Ee("invalid",t);break;case"option":o=r;break;case"select":t._wrapperState={wasMultiple:!!r.multiple},o=Re({},r,{value:void 0}),Ee("invalid",t);break;case"textarea":VD(t,r),o=Hm(t,r),Ee("invalid",t);break;default:o=r}Um(n,o),a=o;for(i in a)if(a.hasOwnProperty(i)){var l=a[i];i==="style"?Jv(t,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Kv(t,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&La(t,l):typeof l=="number"&&La(t,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Aa.hasOwnProperty(i)?l!=null&&i==="onScroll"&&Ee("scroll",t):l!=null&&_p(t,i,l,s))}switch(n){case"input":Lu(t),BD(t,r,!1);break;case"textarea":Lu(t),WD(t);break;case"option":r.value!=null&&t.setAttribute("value",""+Qr(r.value));break;case"select":t.multiple=!!r.multiple,i=r.value,i!=null?Hi(t,!!r.multiple,i,!1):r.defaultValue!=null&&Hi(t,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(t.onclick=Dc)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(e.flags|=4)}e.ref!==null&&(e.flags|=512,e.flags|=2097152)}return ut(e),null;case 6:if(t&&e.stateNode!=null)Sw(t,e,t.memoizedProps,r);else{if(typeof r!="string"&&e.stateNode===null)throw Error(M(166));if(n=Oo(Ya.current),Oo(jn.current),qu(e)){if(r=e.stateNode,n=e.memoizedProps,r[zn]=e,(i=r.nodeValue!==n)&&(t=Pt,t!==null))switch(t.tag){case 3:Zu(r.nodeValue,n,(t.mode&1)!==0);break;case 5:t.memoizedProps.suppressHydrationWarning!==!0&&Zu(r.nodeValue,n,(t.mode&1)!==0)}i&&(e.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[zn]=e,e.stateNode=r}return ut(e),null;case 13:if(we(Oe),r=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(xe&&Lt!==null&&e.mode&1&&!(e.flags&128))VE(),qi(),e.flags|=98560,i=!1;else if(i=qu(e),r!==null&&r.dehydrated!==null){if(t===null){if(!i)throw Error(M(318));if(i=e.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(M(317));i[zn]=e}else qi(),!(e.flags&128)&&(e.memoizedState=null),e.flags|=4;ut(e),i=!1}else Cn!==null&&(xp(Cn),Cn=null),i=!0;if(!i)return e.flags&65536?e:null}return e.flags&128?(e.lanes=n,e):(r=r!==null,r!==(t!==null&&t.memoizedState!==null)&&r&&(e.child.flags|=8192,e.mode&1&&(t===null||Oe.current&1?Ye===0&&(Ye=3):uh())),e.updateQueue!==null&&(e.flags|=4),ut(e),null);case 4:return Qi(),yp(t,e),t===null&&Ua(e.stateNode.containerInfo),ut(e),null;case 10:return Gp(e.type._context),ut(e),null;case 17:return Ft(e.type)&&vc(),ut(e),null;case 19:if(we(Oe),i=e.memoizedState,i===null)return ut(e),null;if(r=(e.flags&128)!==0,s=i.rendering,s===null)if(r)va(i,!1);else{if(Ye!==0||t!==null&&t.flags&128)for(t=e.child;t!==null;){if(s=Fc(t),s!==null){for(e.flags|=128,va(i,!1),r=s.updateQueue,r!==null&&(e.updateQueue=r,e.flags|=4),e.subtreeFlags=0,r=n,n=e.child;n!==null;)i=n,t=r,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=t,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,t=s.dependencies,i.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),n=n.sibling;return ve(Oe,Oe.current&1|2),e.child}t=t.sibling}i.tail!==null&&Le()>Xi&&(e.flags|=128,r=!0,va(i,!1),e.lanes=4194304)}else{if(!r)if(t=Fc(s),t!==null){if(e.flags|=128,r=!0,n=t.updateQueue,n!==null&&(e.updateQueue=n,e.flags|=4),va(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!xe)return ut(e),null}else 2*Le()-i.renderingStartTime>Xi&&n!==1073741824&&(e.flags|=128,r=!0,va(i,!1),e.lanes=4194304);i.isBackwards?(s.sibling=e.child,e.child=s):(n=i.last,n!==null?n.sibling=s:e.child=s,i.last=s)}return i.tail!==null?(e=i.tail,i.rendering=e,i.tail=e.sibling,i.renderingStartTime=Le(),e.sibling=null,n=Oe.current,ve(Oe,r?n&1|2:n&1),e):(ut(e),null);case 22:case 23:return lh(),r=e.memoizedState!==null,t!==null&&t.memoizedState!==null!==r&&(e.flags|=8192),r&&e.mode&1?At&1073741824&&(ut(e),e.subtreeFlags&6&&(e.flags|=8192)):ut(e),null;case 24:return null;case 25:return null}throw Error(M(156,e.tag))}function Ik(t,e){switch(zp(e),e.tag){case 1:return Ft(e.type)&&vc(),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Qi(),we(Tt),we(ft),Qp(),t=e.flags,t&65536&&!(t&128)?(e.flags=t&-65537|128,e):null;case 5:return Kp(e),null;case 13:if(we(Oe),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(M(340));qi()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return we(Oe),null;case 4:return Qi(),null;case 10:return Gp(e.type._context),null;case 22:case 23:return lh(),null;case 24:return null;default:return null}}var Ju=!1,ct=!1,Ak=typeof WeakSet=="function"?WeakSet:Set,G=null;function Vi(t,e){var n=t.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){Ae(t,e,r)}else n.current=null}function gp(t,e,n){try{n()}catch(r){Ae(t,e,r)}}var bv=!1;function Lk(t,e){if(ep=hc,t=FE(),Wp(t)){if("selectionStart"in t)var n={start:t.selectionStart,end:t.selectionEnd};else e:{n=(n=t.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(D){n=null;break e}var s=0,a=-1,l=-1,u=0,d=0,f=t,m=null;t:for(;;){for(var c;f!==n||o!==0&&f.nodeType!==3||(a=s+o),f!==i||r!==0&&f.nodeType!==3||(l=s+r),f.nodeType===3&&(s+=f.nodeValue.length),(c=f.firstChild)!==null;)m=f,f=c;for(;;){if(f===t)break t;if(m===n&&++u===o&&(a=s),m===i&&++d===r&&(l=s),(c=f.nextSibling)!==null)break;f=m,m=f.parentNode}f=c}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(tp={focusedElem:t,selectionRange:n},hc=!1,G=e;G!==null;)if(e=G,t=e.child,(e.subtreeFlags&1028)!==0&&t!==null)t.return=e,G=t;else for(;G!==null;){e=G;try{var y=e.alternate;if(e.flags&1024)switch(e.tag){case 0:case 11:case 15:break;case 1:if(y!==null){var v=y.memoizedProps,S=y.memoizedState,h=e.stateNode,p=h.getSnapshotBeforeUpdate(e.elementType===e.type?v:wn(e.type,v),S);h.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var E=e.stateNode.containerInfo;E.nodeType===1?E.textContent="":E.nodeType===9&&E.documentElement&&E.removeChild(E.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(M(163))}}catch(D){Ae(e,e.return,D)}if(t=e.sibling,t!==null){t.return=e.return,G=t;break}G=e.return}return y=bv,bv=!1,y}function Na(t,e,n){var r=e.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&t)===t){var i=o.destroy;o.destroy=void 0,i!==void 0&&gp(e,n,i)}o=o.next}while(o!==r)}}function zc(t,e){if(e=e.updateQueue,e=e!==null?e.lastEffect:null,e!==null){var n=e=e.next;do{if((n.tag&t)===t){var r=n.create;n.destroy=r()}n=n.next}while(n!==e)}}function Dp(t){var e=t.ref;if(e!==null){var n=t.stateNode;switch(t.tag){case 5:t=n;break;default:t=n}typeof e=="function"?e(t):e.current=t}}function Cw(t){var e=t.alternate;e!==null&&(t.alternate=null,Cw(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&(delete e[zn],delete e[$a],delete e[op],delete e[Dk],delete e[vk])),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}function xw(t){return t.tag===5||t.tag===3||t.tag===4}function Rv(t){e:for(;;){for(;t.sibling===null;){if(t.return===null||xw(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.flags&2||t.child===null||t.tag===4)continue e;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function vp(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.nodeType===8?n.parentNode.insertBefore(t,e):n.insertBefore(t,e):(n.nodeType===8?(e=n.parentNode,e.insertBefore(t,n)):(e=n,e.appendChild(t)),n=n._reactRootContainer,n!=null||e.onclick!==null||(e.onclick=Dc));else if(r!==4&&(t=t.child,t!==null))for(vp(t,e,n),t=t.sibling;t!==null;)vp(t,e,n),t=t.sibling}function Ep(t,e,n){var r=t.tag;if(r===5||r===6)t=t.stateNode,e?n.insertBefore(t,e):n.appendChild(t);else if(r!==4&&(t=t.child,t!==null))for(Ep(t,e,n),t=t.sibling;t!==null;)Ep(t,e,n),t=t.sibling}var ot=null,Sn=!1;function Lr(t,e,n){for(n=n.child;n!==null;)Tw(t,e,n),n=n.sibling}function Tw(t,e,n){if(Un&&typeof Un.onCommitFiberUnmount=="function")try{Un.onCommitFiberUnmount(Ic,n)}catch(a){}switch(n.tag){case 5:ct||Vi(n,e);case 6:var r=ot,o=Sn;ot=null,Lr(t,e,n),ot=r,Sn=o,ot!==null&&(Sn?(t=ot,n=n.stateNode,t.nodeType===8?t.parentNode.removeChild(n):t.removeChild(n)):ot.removeChild(n.stateNode));break;case 18:ot!==null&&(Sn?(t=ot,n=n.stateNode,t.nodeType===8?Cm(t.parentNode,n):t.nodeType===1&&Cm(t,n),Wa(t)):Cm(ot,n.stateNode));break;case 4:r=ot,o=Sn,ot=n.stateNode.containerInfo,Sn=!0,Lr(t,e,n),ot=r,Sn=o;break;case 0:case 11:case 14:case 15:if(!ct&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var i=o,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&gp(n,e,s),o=o.next}while(o!==r)}Lr(t,e,n);break;case 1:if(!ct&&(Vi(n,e),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){Ae(n,e,a)}Lr(t,e,n);break;case 21:Lr(t,e,n);break;case 22:n.mode&1?(ct=(r=ct)||n.memoizedState!==null,Lr(t,e,n),ct=r):Lr(t,e,n);break;default:Lr(t,e,n)}}function Nv(t){var e=t.updateQueue;if(e!==null){t.updateQueue=null;var n=t.stateNode;n===null&&(n=t.stateNode=new Ak),e.forEach(function(r){var o=$k.bind(null,t,r);n.has(r)||(n.add(r),r.then(o,o))})}}function En(t,e){var n=e.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var i=t,s=e,a=s;e:for(;a!==null;){switch(a.tag){case 5:ot=a.stateNode,Sn=!1;break e;case 3:ot=a.stateNode.containerInfo,Sn=!0;break e;case 4:ot=a.stateNode.containerInfo,Sn=!0;break e}a=a.return}if(ot===null)throw Error(M(160));Tw(i,s,o),ot=null,Sn=!1;var l=o.alternate;l!==null&&(l.return=null),o.return=null}catch(u){Ae(o,e,u)}}if(e.subtreeFlags&12854)for(e=e.child;e!==null;)Fw(e,t),e=e.sibling}function Fw(t,e){var n=t.alternate,r=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:if(En(e,t),Wn(t),r&4){try{Na(3,t,t.return),zc(3,t)}catch(v){Ae(t,t.return,v)}try{Na(5,t,t.return)}catch(v){Ae(t,t.return,v)}}break;case 1:En(e,t),Wn(t),r&512&&n!==null&&Vi(n,n.return);break;case 5:if(En(e,t),Wn(t),r&512&&n!==null&&Vi(n,n.return),t.flags&32){var o=t.stateNode;try{La(o,"")}catch(v){Ae(t,t.return,v)}}if(r&4&&(o=t.stateNode,o!=null)){var i=t.memoizedProps,s=n!==null?n.memoizedProps:i,a=t.type,l=t.updateQueue;if(t.updateQueue=null,l!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&Yv(o,i),jm(a,s);var u=jm(a,i);for(s=0;s<l.length;s+=2){var d=l[s],f=l[s+1];d==="style"?Jv(o,f):d==="dangerouslySetInnerHTML"?Kv(o,f):d==="children"?La(o,f):_p(o,d,f,u)}switch(a){case"input":Vm(o,i);break;case"textarea":Zv(o,i);break;case"select":var m=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var c=i.value;c!=null?Hi(o,!!i.multiple,c,!1):m!==!!i.multiple&&(i.defaultValue!=null?Hi(o,!!i.multiple,i.defaultValue,!0):Hi(o,!!i.multiple,i.multiple?[]:"",!1))}o[$a]=i}catch(v){Ae(t,t.return,v)}}break;case 6:if(En(e,t),Wn(t),r&4){if(t.stateNode===null)throw Error(M(162));o=t.stateNode,i=t.memoizedProps;try{o.nodeValue=i}catch(v){Ae(t,t.return,v)}}break;case 3:if(En(e,t),Wn(t),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Wa(e.containerInfo)}catch(v){Ae(t,t.return,v)}break;case 4:En(e,t),Wn(t);break;case 13:En(e,t),Wn(t),o=t.child,o.flags&8192&&(i=o.memoizedState!==null,o.stateNode.isHidden=i,!i||o.alternate!==null&&o.alternate.memoizedState!==null||(sh=Le())),r&4&&Nv(t);break;case 22:if(d=n!==null&&n.memoizedState!==null,t.mode&1?(ct=(u=ct)||d,En(e,t),ct=u):En(e,t),Wn(t),r&8192){if(u=t.memoizedState!==null,(t.stateNode.isHidden=u)&&!d&&t.mode&1)for(G=t,d=t.child;d!==null;){for(f=G=d;G!==null;){switch(m=G,c=m.child,m.tag){case 0:case 11:case 14:case 15:Na(4,m,m.return);break;case 1:Vi(m,m.return);var y=m.stateNode;if(typeof y.componentWillUnmount=="function"){r=m,n=m.return;try{e=r,y.props=e.memoizedProps,y.state=e.memoizedState,y.componentWillUnmount()}catch(v){Ae(r,n,v)}}break;case 5:Vi(m,m.return);break;case 22:if(m.memoizedState!==null){Iv(f);continue}}c!==null?(c.return=m,G=c):Iv(f)}d=d.sibling}e:for(d=null,f=t;;){if(f.tag===5){if(d===null){d=f;try{o=f.stateNode,u?(i=o.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=f.stateNode,l=f.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=Qv("display",s))}catch(v){Ae(t,t.return,v)}}}else if(f.tag===6){if(d===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(v){Ae(t,t.return,v)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===t)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===t)break e;for(;f.sibling===null;){if(f.return===null||f.return===t)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:En(e,t),Wn(t),r&4&&Nv(t);break;case 21:break;default:En(e,t),Wn(t)}}function Wn(t){var e=t.flags;if(e&2){try{e:{for(var n=t.return;n!==null;){if(xw(n)){var r=n;break e}n=n.return}throw Error(M(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(La(o,""),r.flags&=-33);var i=Rv(t);Ep(t,i,o);break;case 3:case 4:var s=r.stateNode.containerInfo,a=Rv(t);vp(t,a,s);break;default:throw Error(M(161))}}catch(l){Ae(t,t.return,l)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Pk(t,e,n){G=t,_w(t,e,n)}function _w(t,e,n){for(var r=(t.mode&1)!==0;G!==null;){var o=G,i=o.child;if(o.tag===22&&r){var s=o.memoizedState!==null||Ju;if(!s){var a=o.alternate,l=a!==null&&a.memoizedState!==null||ct;a=Ju;var u=ct;if(Ju=s,(ct=l)&&!u)for(G=o;G!==null;)s=G,l=s.child,s.tag===22&&s.memoizedState!==null?Av(o):l!==null?(l.return=s,G=l):Av(o);for(;i!==null;)G=i,_w(i,e,n),i=i.sibling;G=o,Ju=a,ct=u}Mv(t,e,n)}else o.subtreeFlags&8772&&i!==null?(i.return=o,G=i):Mv(t,e,n)}}function Mv(t){for(;G!==null;){var e=G;if(e.flags&8772){var n=e.alternate;try{if(e.flags&8772)switch(e.tag){case 0:case 11:case 15:ct||zc(5,e);break;case 1:var r=e.stateNode;if(e.flags&4&&!ct)if(n===null)r.componentDidMount();else{var o=e.elementType===e.type?n.memoizedProps:wn(e.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=e.updateQueue;i!==null&&yv(e,i,r);break;case 3:var s=e.updateQueue;if(s!==null){if(n=null,e.child!==null)switch(e.child.tag){case 5:n=e.child.stateNode;break;case 1:n=e.child.stateNode}yv(e,s,n)}break;case 5:var a=e.stateNode;if(n===null&&e.flags&4){n=a;var l=e.memoizedProps;switch(e.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(e.memoizedState===null){var u=e.alternate;if(u!==null){var d=u.memoizedState;if(d!==null){var f=d.dehydrated;f!==null&&Wa(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(M(163))}ct||e.flags&512&&Dp(e)}catch(m){Ae(e,e.return,m)}}if(e===t){G=null;break}if(n=e.sibling,n!==null){n.return=e.return,G=n;break}G=e.return}}function Iv(t){for(;G!==null;){var e=G;if(e===t){G=null;break}var n=e.sibling;if(n!==null){n.return=e.return,G=n;break}G=e.return}}function Av(t){for(;G!==null;){var e=G;try{switch(e.tag){case 0:case 11:case 15:var n=e.return;try{zc(4,e)}catch(l){Ae(e,n,l)}break;case 1:var r=e.stateNode;if(typeof r.componentDidMount=="function"){var o=e.return;try{r.componentDidMount()}catch(l){Ae(e,o,l)}}var i=e.return;try{Dp(e)}catch(l){Ae(e,i,l)}break;case 5:var s=e.return;try{Dp(e)}catch(l){Ae(e,s,l)}}}catch(l){Ae(e,e.return,l)}if(e===t){G=null;break}var a=e.sibling;if(a!==null){a.return=e.return,G=a;break}G=e.return}}var Bk=Math.ceil,Oc=mr.ReactCurrentDispatcher,oh=mr.ReactCurrentOwner,Xt=mr.ReactCurrentBatchConfig,ce=0,Xe=null,He=null,it=0,At=0,Wi=eo(0),Ye=0,Qa=null,Ao=0,Uc=0,ih=0,Ma=null,Ct=null,sh=0,Xi=1/0,or=null,bc=!1,wp=null,Zr=null,Xu=!1,zr=null,Rc=0,Ia=0,Sp=null,ac=-1,lc=0;function yt(){return ce&6?Le():ac!==-1?ac:ac=Le()}function qr(t){return t.mode&1?ce&2&&it!==0?it&-it:wk.transition!==null?(lc===0&&(lc=cE()),lc):(t=pe,t!==0||(t=window.event,t=t===void 0?16:gE(t.type)),t):1}function Tn(t,e,n,r){if(50<Ia)throw Ia=0,Sp=null,Error(M(185));Ja(t,n,r),(!(ce&2)||t!==Xe)&&(t===Xe&&(!(ce&2)&&(Uc|=n),Ye===4&&Wr(t,it)),_t(t,r),n===1&&ce===0&&!(e.mode&1)&&(Xi=Le()+500,Vc&&to()))}function _t(t,e){var n=t.callbackNode;C_(t,e);var r=pc(t,t===Xe?it:0);if(r===0)n!==null&&UD(n),t.callbackNode=null,t.callbackPriority=0;else if(e=r&-r,t.callbackPriority!==e){if(n!=null&&UD(n),e===1)t.tag===0?Ek(Lv.bind(null,t)):LE(Lv.bind(null,t)),yk(function(){!(ce&6)&&to()}),n=null;else{switch(fE(r)){case 1:n=Np;break;case 4:n=lE;break;case 16:n=mc;break;case 536870912:n=uE;break;default:n=mc}n=Aw(n,kw.bind(null,t))}t.callbackPriority=e,t.callbackNode=n}}function kw(t,e){if(ac=-1,lc=0,ce&6)throw Error(M(327));var n=t.callbackNode;if(Gi()&&t.callbackNode!==n)return null;var r=pc(t,t===Xe?it:0);if(r===0)return null;if(r&30||r&t.expiredLanes||e)e=Nc(t,r);else{e=r;var o=ce;ce|=2;var i=bw();(Xe!==t||it!==e)&&(or=null,Xi=Le()+500,bo(t,e));do try{Hk();break}catch(a){Ow(t,a)}while(1);$p(),Oc.current=i,ce=o,He!==null?e=0:(Xe=null,it=0,e=Ye)}if(e!==0){if(e===2&&(o=qm(t),o!==0&&(r=o,e=Cp(t,o))),e===1)throw n=Qa,bo(t,0),Wr(t,r),_t(t,Le()),n;if(e===6)Wr(t,r);else{if(o=t.current.alternate,!(r&30)&&!Vk(o)&&(e=Nc(t,r),e===2&&(i=qm(t),i!==0&&(r=i,e=Cp(t,i))),e===1))throw n=Qa,bo(t,0),Wr(t,r),_t(t,Le()),n;switch(t.finishedWork=o,t.finishedLanes=r,e){case 0:case 1:throw Error(M(345));case 2:Fo(t,Ct,or);break;case 3:if(Wr(t,r),(r&130023424)===r&&(e=sh+500-Le(),10<e)){if(pc(t,0)!==0)break;if(o=t.suspendedLanes,(o&r)!==r){yt(),t.pingedLanes|=t.suspendedLanes&o;break}t.timeoutHandle=rp(Fo.bind(null,t,Ct,or),e);break}Fo(t,Ct,or);break;case 4:if(Wr(t,r),(r&4194240)===r)break;for(e=t.eventTimes,o=-1;0<r;){var s=31-xn(r);i=1<<s,s=e[s],s>o&&(o=s),r&=~i}if(r=o,r=Le()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Bk(r/1960))-r,10<r){t.timeoutHandle=rp(Fo.bind(null,t,Ct,or),r);break}Fo(t,Ct,or);break;case 5:Fo(t,Ct,or);break;default:throw Error(M(329))}}}return _t(t,Le()),t.callbackNode===n?kw.bind(null,t):null}function Cp(t,e){var n=Ma;return t.current.memoizedState.isDehydrated&&(bo(t,e).flags|=256),t=Nc(t,e),t!==2&&(e=Ct,Ct=n,e!==null&&xp(e)),t}function xp(t){Ct===null?Ct=t:Ct.push.apply(Ct,t)}function Vk(t){for(var e=t;;){if(e.flags&16384){var n=e.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],i=o.getSnapshot;o=o.value;try{if(!Fn(i(),o))return!1}catch(s){return!1}}}if(n=e.child,e.subtreeFlags&16384&&n!==null)n.return=e,e=n;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Wr(t,e){for(e&=~ih,e&=~Uc,t.suspendedLanes|=e,t.pingedLanes&=~e,t=t.expirationTimes;0<e;){var n=31-xn(e),r=1<<n;t[n]=-1,e&=~r}}function Lv(t){if(ce&6)throw Error(M(327));Gi();var e=pc(t,0);if(!(e&1))return _t(t,Le()),null;var n=Nc(t,e);if(t.tag!==0&&n===2){var r=qm(t);r!==0&&(e=r,n=Cp(t,r))}if(n===1)throw n=Qa,bo(t,0),Wr(t,e),_t(t,Le()),n;if(n===6)throw Error(M(345));return t.finishedWork=t.current.alternate,t.finishedLanes=e,Fo(t,Ct,or),_t(t,Le()),null}function ah(t,e){var n=ce;ce|=1;try{return t(e)}finally{ce=n,ce===0&&(Xi=Le()+500,Vc&&to())}}function Lo(t){zr!==null&&zr.tag===0&&!(ce&6)&&Gi();var e=ce;ce|=1;var n=Xt.transition,r=pe;try{if(Xt.transition=null,pe=1,t)return t()}finally{pe=r,Xt.transition=n,ce=e,!(ce&6)&&to()}}function lh(){At=Wi.current,we(Wi)}function bo(t,e){t.finishedWork=null,t.finishedLanes=0;var n=t.timeoutHandle;if(n!==-1&&(t.timeoutHandle=-1,hk(n)),He!==null)for(n=He.return;n!==null;){var r=n;switch(zp(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&vc();break;case 3:Qi(),we(Tt),we(ft),Qp();break;case 5:Kp(r);break;case 4:Qi();break;case 13:we(Oe);break;case 19:we(Oe);break;case 10:Gp(r.type._context);break;case 22:case 23:lh()}n=n.return}if(Xe=t,He=t=Kr(t.current,null),it=At=e,Ye=0,Qa=null,ih=Uc=Ao=0,Ct=Ma=null,ko!==null){for(e=0;e<ko.length;e++)if(n=ko[e],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,i=n.pending;if(i!==null){var s=i.next;i.next=o,r.next=s}n.pending=r}ko=null}return t}function Ow(t,e){do{var n=He;try{if($p(),oc.current=kc,_c){for(var r=be.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}_c=!1}if(Io=0,Je=Ge=be=null,Ra=!1,Za=0,oh.current=null,n===null||n.return===null){Ye=1,Qa=e,He=null;break}e:{var i=t,s=n.return,a=n,l=e;if(e=it,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,d=a,f=d.tag;if(!(d.mode&1)&&(f===0||f===11||f===15)){var m=d.alternate;m?(d.updateQueue=m.updateQueue,d.memoizedState=m.memoizedState,d.lanes=m.lanes):(d.updateQueue=null,d.memoizedState=null)}var c=Cv(s);if(c!==null){c.flags&=-257,xv(c,s,a,i,e),c.mode&1&&Sv(i,u,e),e=c,l=u;var y=e.updateQueue;if(y===null){var v=new Set;v.add(l),e.updateQueue=v}else y.add(l);break e}else{if(!(e&1)){Sv(i,u,e),uh();break e}l=Error(M(426))}}else if(xe&&a.mode&1){var S=Cv(s);if(S!==null){!(S.flags&65536)&&(S.flags|=256),xv(S,s,a,i,e),Up(Ji(l,a));break e}}i=l=Ji(l,a),Ye!==4&&(Ye=2),Ma===null?Ma=[i]:Ma.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,e&=-e,i.lanes|=e;var h=dw(i,l,e);hv(i,h);break e;case 1:a=l;var p=i.type,E=i.stateNode;if(!(i.flags&128)&&(typeof p.getDerivedStateFromError=="function"||E!==null&&typeof E.componentDidCatch=="function"&&(Zr===null||!Zr.has(E)))){i.flags|=65536,e&=-e,i.lanes|=e;var D=mw(i,a,e);hv(i,D);break e}}i=i.return}while(i!==null)}Nw(n)}catch(x){e=x,He===n&&n!==null&&(He=n=n.return);continue}break}while(1)}function bw(){var t=Oc.current;return Oc.current=kc,t===null?kc:t}function uh(){(Ye===0||Ye===3||Ye===2)&&(Ye=4),Xe===null||!(Ao&268435455)&&!(Uc&268435455)||Wr(Xe,it)}function Nc(t,e){var n=ce;ce|=2;var r=bw();(Xe!==t||it!==e)&&(or=null,bo(t,e));do try{Wk();break}catch(o){Ow(t,o)}while(1);if($p(),ce=n,Oc.current=r,He!==null)throw Error(M(261));return Xe=null,it=0,Ye}function Wk(){for(;He!==null;)Rw(He)}function Hk(){for(;He!==null&&!p_();)Rw(He)}function Rw(t){var e=Iw(t.alternate,t,At);t.memoizedProps=t.pendingProps,e===null?Nw(t):He=e,oh.current=null}function Nw(t){var e=t;do{var n=e.alternate;if(t=e.return,e.flags&32768){if(n=Ik(n,e),n!==null){n.flags&=32767,He=n;return}if(t!==null)t.flags|=32768,t.subtreeFlags=0,t.deletions=null;else{Ye=6,He=null;return}}else if(n=Mk(n,e,At),n!==null){He=n;return}if(e=e.sibling,e!==null){He=e;return}He=e=t}while(e!==null);Ye===0&&(Ye=5)}function Fo(t,e,n){var r=pe,o=Xt.transition;try{Xt.transition=null,pe=1,zk(t,e,n,r)}finally{Xt.transition=o,pe=r}return null}function zk(t,e,n,r){do Gi();while(zr!==null);if(ce&6)throw Error(M(327));n=t.finishedWork;var o=t.finishedLanes;if(n===null)return null;if(t.finishedWork=null,t.finishedLanes=0,n===t.current)throw Error(M(177));t.callbackNode=null,t.callbackPriority=0;var i=n.lanes|n.childLanes;if(x_(t,i),t===Xe&&(He=Xe=null,it=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Xu||(Xu=!0,Aw(mc,function(){return Gi(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=Xt.transition,Xt.transition=null;var s=pe;pe=1;var a=ce;ce|=4,oh.current=null,Lk(t,n),Fw(n,t),ck(tp),hc=!!ep,tp=ep=null,t.current=n,Pk(n,t,o),h_(),ce=a,pe=s,Xt.transition=i}else t.current=n;if(Xu&&(Xu=!1,zr=t,Rc=o),i=t.pendingLanes,i===0&&(Zr=null),D_(n.stateNode,r),_t(t,Le()),e!==null)for(r=t.onRecoverableError,n=0;n<e.length;n++)o=e[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(bc)throw bc=!1,t=wp,wp=null,t;return Rc&1&&t.tag!==0&&Gi(),i=t.pendingLanes,i&1?t===Sp?Ia++:(Ia=0,Sp=t):Ia=0,to(),null}function Gi(){if(zr!==null){var t=fE(Rc),e=Xt.transition,n=pe;try{if(Xt.transition=null,pe=16>t?16:t,zr===null)var r=!1;else{if(t=zr,zr=null,Rc=0,ce&6)throw Error(M(331));var o=ce;for(ce|=4,G=t.current;G!==null;){var i=G,s=i.child;if(G.flags&16){var a=i.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(G=u;G!==null;){var d=G;switch(d.tag){case 0:case 11:case 15:Na(8,d,i)}var f=d.child;if(f!==null)f.return=d,G=f;else for(;G!==null;){d=G;var m=d.sibling,c=d.return;if(Cw(d),d===u){G=null;break}if(m!==null){m.return=c,G=m;break}G=c}}}var y=i.alternate;if(y!==null){var v=y.child;if(v!==null){y.child=null;do{var S=v.sibling;v.sibling=null,v=S}while(v!==null)}}G=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,G=s;else e:for(;G!==null;){if(i=G,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Na(9,i,i.return)}var h=i.sibling;if(h!==null){h.return=i.return,G=h;break e}G=i.return}}var p=t.current;for(G=p;G!==null;){s=G;var E=s.child;if(s.subtreeFlags&2064&&E!==null)E.return=s,G=E;else e:for(s=p;G!==null;){if(a=G,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:zc(9,a)}}catch(x){Ae(a,a.return,x)}if(a===s){G=null;break e}var D=a.sibling;if(D!==null){D.return=a.return,G=D;break e}G=a.return}}if(ce=o,to(),Un&&typeof Un.onPostCommitFiberRoot=="function")try{Un.onPostCommitFiberRoot(Ic,t)}catch(x){}r=!0}return r}finally{pe=n,Xt.transition=e}}return!1}function Pv(t,e,n){e=Ji(n,e),e=dw(t,e,1),t=Yr(t,e,1),e=yt(),t!==null&&(Ja(t,1,e),_t(t,e))}function Ae(t,e,n){if(t.tag===3)Pv(t,t,n);else for(;e!==null;){if(e.tag===3){Pv(e,t,n);break}else if(e.tag===1){var r=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Zr===null||!Zr.has(r))){t=Ji(n,t),t=mw(e,t,1),e=Yr(e,t,1),t=yt(),e!==null&&(Ja(e,1,t),_t(e,t));break}}e=e.return}}function Uk(t,e,n){var r=t.pingCache;r!==null&&r.delete(e),e=yt(),t.pingedLanes|=t.suspendedLanes&n,Xe===t&&(it&n)===n&&(Ye===4||Ye===3&&(it&130023424)===it&&500>Le()-sh?bo(t,0):ih|=n),_t(t,e)}function Mw(t,e){e===0&&(t.mode&1?(e=Vu,Vu<<=1,!(Vu&130023424)&&(Vu=4194304)):e=1);var n=yt();t=fr(t,e),t!==null&&(Ja(t,e,n),_t(t,n))}function jk(t){var e=t.memoizedState,n=0;e!==null&&(n=e.retryLane),Mw(t,n)}function $k(t,e){var n=0;switch(t.tag){case 13:var r=t.stateNode,o=t.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=t.stateNode;break;default:throw Error(M(314))}r!==null&&r.delete(e),Mw(t,n)}var Iw;Iw=function(t,e,n){if(t!==null)if(t.memoizedProps!==e.pendingProps||Tt.current)xt=!0;else{if(!(t.lanes&n)&&!(e.flags&128))return xt=!1,Nk(t,e,n);xt=!!(t.flags&131072)}else xt=!1,xe&&e.flags&1048576&&PE(e,Sc,e.index);switch(e.lanes=0,e.tag){case 2:var r=e.type;sc(t,e),t=e.pendingProps;var o=Zi(e,ft.current);$i(e,n),o=Xp(null,e,r,t,o,n);var i=eh();return e.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(e.tag=1,e.memoizedState=null,e.updateQueue=null,Ft(r)?(i=!0,Ec(e)):i=!1,e.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Zp(e),o.updater=Wc,e.stateNode=o,o._reactInternals=e,cp(e,r,t,n),e=mp(null,e,r,!0,i,n)):(e.tag=0,xe&&i&&Hp(e),ht(null,e,o,n),e=e.child),e;case 16:r=e.elementType;e:{switch(sc(t,e),t=e.pendingProps,o=r._init,r=o(r._payload),e.type=r,o=e.tag=Yk(r),t=wn(r,t),o){case 0:e=dp(null,e,r,t,n);break e;case 1:e=_v(null,e,r,t,n);break e;case 11:e=Tv(null,e,r,t,n);break e;case 14:e=Fv(null,e,r,wn(r.type,t),n);break e}throw Error(M(306,r,""))}return e;case 0:return r=e.type,o=e.pendingProps,o=e.elementType===r?o:wn(r,o),dp(t,e,r,o,n);case 1:return r=e.type,o=e.pendingProps,o=e.elementType===r?o:wn(r,o),_v(t,e,r,o,n);case 3:e:{if(gw(e),t===null)throw Error(M(387));r=e.pendingProps,i=e.memoizedState,o=i.element,HE(t,e),Tc(e,r,null,n);var s=e.memoizedState;if(r=s.element,i.isDehydrated)if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},e.updateQueue.baseState=i,e.memoizedState=i,e.flags&256){o=Ji(Error(M(423)),e),e=kv(t,e,r,n,o);break e}else if(r!==o){o=Ji(Error(M(424)),e),e=kv(t,e,r,n,o);break e}else for(Lt=Gr(e.stateNode.containerInfo.firstChild),Pt=e,xe=!0,Cn=null,n=$E(e,null,r,n),e.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(qi(),r===o){e=dr(t,e,n);break e}ht(t,e,r,n)}e=e.child}return e;case 5:return GE(e),t===null&&ap(e),r=e.type,o=e.pendingProps,i=t!==null?t.memoizedProps:null,s=o.children,np(r,o)?s=null:i!==null&&np(r,i)&&(e.flags|=32),yw(t,e),ht(t,e,s,n),e.child;case 6:return t===null&&ap(e),null;case 13:return Dw(t,e,n);case 4:return qp(e,e.stateNode.containerInfo),r=e.pendingProps,t===null?e.child=Ki(e,null,r,n):ht(t,e,r,n),e.child;case 11:return r=e.type,o=e.pendingProps,o=e.elementType===r?o:wn(r,o),Tv(t,e,r,o,n);case 7:return ht(t,e,e.pendingProps,n),e.child;case 8:return ht(t,e,e.pendingProps.children,n),e.child;case 12:return ht(t,e,e.pendingProps.children,n),e.child;case 10:e:{if(r=e.type._context,o=e.pendingProps,i=e.memoizedProps,s=o.value,ve(Cc,r._currentValue),r._currentValue=s,i!==null)if(Fn(i.value,s)){if(i.children===o.children&&!Tt.current){e=dr(t,e,n);break e}}else for(i=e.child,i!==null&&(i.return=e);i!==null;){var a=i.dependencies;if(a!==null){s=i.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(i.tag===1){l=lr(-1,n&-n),l.tag=2;var u=i.updateQueue;if(u!==null){u=u.shared;var d=u.pending;d===null?l.next=l:(l.next=d.next,d.next=l),u.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),lp(i.return,n,e),a.lanes|=n;break}l=l.next}}else if(i.tag===10)s=i.type===e.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(M(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),lp(s,n,e),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===e){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}ht(t,e,o.children,n),e=e.child}return e;case 9:return o=e.type,r=e.pendingProps.children,$i(e,n),o=en(o),r=r(o),e.flags|=1,ht(t,e,r,n),e.child;case 14:return r=e.type,o=wn(r,e.pendingProps),o=wn(r.type,o),Fv(t,e,r,o,n);case 15:return pw(t,e,e.type,e.pendingProps,n);case 17:return r=e.type,o=e.pendingProps,o=e.elementType===r?o:wn(r,o),sc(t,e),e.tag=1,Ft(r)?(t=!0,Ec(e)):t=!1,$i(e,n),UE(e,r,o),cp(e,r,o,n),mp(null,e,r,!0,t,n);case 19:return vw(t,e,n);case 22:return hw(t,e,n)}throw Error(M(156,e.tag))};function Aw(t,e){return aE(t,e)}function Gk(t,e,n,r){this.tag=t,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Jt(t,e,n,r){return new Gk(t,e,n,r)}function ch(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Yk(t){if(typeof t=="function")return ch(t)?1:0;if(t!=null){if(t=t.$$typeof,t===Op)return 11;if(t===bp)return 14}return 2}function Kr(t,e){var n=t.alternate;return n===null?(n=Jt(t.tag,e,t.key,t.mode),n.elementType=t.elementType,n.type=t.type,n.stateNode=t.stateNode,n.alternate=t,t.alternate=n):(n.pendingProps=e,n.type=t.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=t.flags&14680064,n.childLanes=t.childLanes,n.lanes=t.lanes,n.child=t.child,n.memoizedProps=t.memoizedProps,n.memoizedState=t.memoizedState,n.updateQueue=t.updateQueue,e=t.dependencies,n.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},n.sibling=t.sibling,n.index=t.index,n.ref=t.ref,n}function uc(t,e,n,r,o,i){var s=2;if(r=t,typeof t=="function")ch(t)&&(s=1);else if(typeof t=="string")s=5;else e:switch(t){case bi:return Ro(n.children,o,i,e);case kp:s=8,o|=8;break;case Im:return t=Jt(12,n,e,o|2),t.elementType=Im,t.lanes=i,t;case Am:return t=Jt(13,n,e,o),t.elementType=Am,t.lanes=i,t;case Lm:return t=Jt(19,n,e,o),t.elementType=Lm,t.lanes=i,t;case jv:return jc(n,o,i,e);default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case zv:s=10;break e;case Uv:s=9;break e;case Op:s=11;break e;case bp:s=14;break e;case Pr:s=16,r=null;break e}throw Error(M(130,t==null?t:typeof t,""))}return e=Jt(s,n,e,o),e.elementType=t,e.type=r,e.lanes=i,e}function Ro(t,e,n,r){return t=Jt(7,t,r,e),t.lanes=n,t}function jc(t,e,n,r){return t=Jt(22,t,r,e),t.elementType=jv,t.lanes=n,t.stateNode={isHidden:!1},t}function Rm(t,e,n){return t=Jt(6,t,null,e),t.lanes=n,t}function Nm(t,e,n){return e=Jt(4,t.children!==null?t.children:[],t.key,e),e.lanes=n,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}function Zk(t,e,n,r,o){this.tag=e,this.containerInfo=t,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=hm(0),this.expirationTimes=hm(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=hm(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function fh(t,e,n,r,o,i,s,a,l){return t=new Zk(t,e,n,a,l),e===1?(e=1,i===!0&&(e|=8)):e=0,i=Jt(3,null,null,e),t.current=i,i.stateNode=t,i.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Zp(i),t}function qk(t,e,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Oi,key:r==null?null:""+r,children:t,containerInfo:e,implementation:n}}function Lw(t){if(!t)return Jr;t=t._reactInternals;e:{if(Bo(t)!==t||t.tag!==1)throw Error(M(170));var e=t;do{switch(e.tag){case 3:e=e.stateNode.context;break e;case 1:if(Ft(e.type)){e=e.stateNode.__reactInternalMemoizedMergedChildContext;break e}}e=e.return}while(e!==null);throw Error(M(171))}if(t.tag===1){var n=t.type;if(Ft(n))return AE(t,n,e)}return e}function Pw(t,e,n,r,o,i,s,a,l){return t=fh(n,r,!0,t,o,i,s,a,l),t.context=Lw(null),n=t.current,r=yt(),o=qr(n),i=lr(r,o),i.callback=e!=null?e:null,Yr(n,i,o),t.current.lanes=o,Ja(t,o,r),_t(t,r),t}function $c(t,e,n,r){var o=e.current,i=yt(),s=qr(o);return n=Lw(n),e.context===null?e.context=n:e.pendingContext=n,e=lr(i,s),e.payload={element:t},r=r===void 0?null:r,r!==null&&(e.callback=r),t=Yr(o,e,s),t!==null&&(Tn(t,o,s,i),rc(t,o,s)),s}function Mc(t){if(t=t.current,!t.child)return null;switch(t.child.tag){case 5:return t.child.stateNode;default:return t.child.stateNode}}function Bv(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var n=t.retryLane;t.retryLane=n!==0&&n<e?n:e}}function dh(t,e){Bv(t,e),(t=t.alternate)&&Bv(t,e)}function Kk(){return null}var Bw=typeof reportError=="function"?reportError:function(t){console.error(t)};function mh(t){this._internalRoot=t}Gc.prototype.render=mh.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(M(409));$c(t,e,null,null)};Gc.prototype.unmount=mh.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Lo(function(){$c(null,t,null,null)}),e[cr]=null}};function Gc(t){this._internalRoot=t}Gc.prototype.unstable_scheduleHydration=function(t){if(t){var e=pE();t={blockedOn:null,target:t,priority:e};for(var n=0;n<Vr.length&&e!==0&&e<Vr[n].priority;n++);Vr.splice(n,0,t),n===0&&yE(t)}};function ph(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function Yc(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11&&(t.nodeType!==8||t.nodeValue!==" react-mount-point-unstable "))}function Vv(){}function Qk(t,e,n,r,o){if(o){if(typeof r=="function"){var i=r;r=function(){var u=Mc(s);i.call(u)}}var s=Pw(e,r,t,0,null,!1,!1,"",Vv);return t._reactRootContainer=s,t[cr]=s.current,Ua(t.nodeType===8?t.parentNode:t),Lo(),s}for(;o=t.lastChild;)t.removeChild(o);if(typeof r=="function"){var a=r;r=function(){var u=Mc(l);a.call(u)}}var l=fh(t,0,!1,null,null,!1,!1,"",Vv);return t._reactRootContainer=l,t[cr]=l.current,Ua(t.nodeType===8?t.parentNode:t),Lo(function(){$c(e,l,n,r)}),l}function Zc(t,e,n,r,o){var i=n._reactRootContainer;if(i){var s=i;if(typeof o=="function"){var a=o;o=function(){var l=Mc(s);a.call(l)}}$c(e,s,t,o)}else s=Qk(n,e,t,o,r);return Mc(s)}dE=function(t){switch(t.tag){case 3:var e=t.stateNode;if(e.current.memoizedState.isDehydrated){var n=xa(e.pendingLanes);n!==0&&(Mp(e,n|1),_t(e,Le()),!(ce&6)&&(Xi=Le()+500,to()))}break;case 13:Lo(function(){var r=fr(t,1);if(r!==null){var o=yt();Tn(r,t,1,o)}}),dh(t,1)}};Ip=function(t){if(t.tag===13){var e=fr(t,134217728);if(e!==null){var n=yt();Tn(e,t,134217728,n)}dh(t,134217728)}};mE=function(t){if(t.tag===13){var e=qr(t),n=fr(t,e);if(n!==null){var r=yt();Tn(n,t,e,r)}dh(t,e)}};pE=function(){return pe};hE=function(t,e){var n=pe;try{return pe=t,e()}finally{pe=n}};Gm=function(t,e,n){switch(e){case"input":if(Vm(t,n),e=n.name,n.type==="radio"&&e!=null){for(n=t;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+e)+'][type="radio"]'),e=0;e<n.length;e++){var r=n[e];if(r!==t&&r.form===t.form){var o=Bc(r);if(!o)throw Error(M(90));Gv(r),Vm(r,o)}}}break;case"textarea":Zv(t,n);break;case"select":e=n.value,e!=null&&Hi(t,!!n.multiple,e,!1)}};tE=ah;nE=Lo;var Jk={usingClientEntryPoint:!1,Events:[el,Ii,Bc,Xv,eE,ah]},Ea={findFiberByHostInstance:_o,bundleType:0,version:"18.2.0",rendererPackageName:"react-dom"},Xk={bundleType:Ea.bundleType,version:Ea.version,rendererPackageName:Ea.rendererPackageName,rendererConfig:Ea.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:mr.ReactCurrentDispatcher,findHostInstanceByFiber:function(t){return t=iE(t),t===null?null:t.stateNode},findFiberByHostInstance:Ea.findFiberByHostInstance||Kk,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.2.0-next-9e3b772b8-20220608"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"&&(wa=__REACT_DEVTOOLS_GLOBAL_HOOK__,!wa.isDisabled&&wa.supportsFiber))try{Ic=wa.inject(Xk),Un=wa}catch(t){}var wa;Wt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Jk;Wt.createPortal=function(t,e){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ph(e))throw Error(M(200));return qk(t,e,null,n)};Wt.createRoot=function(t,e){if(!ph(t))throw Error(M(299));var n=!1,r="",o=Bw;return e!=null&&(e.unstable_strictMode===!0&&(n=!0),e.identifierPrefix!==void 0&&(r=e.identifierPrefix),e.onRecoverableError!==void 0&&(o=e.onRecoverableError)),e=fh(t,1,!1,null,null,n,!1,r,o),t[cr]=e.current,Ua(t.nodeType===8?t.parentNode:t),new mh(e)};Wt.findDOMNode=function(t){if(t==null)return null;if(t.nodeType===1)return t;var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(M(188)):(t=Object.keys(t).join(","),Error(M(268,t)));return t=iE(e),t=t===null?null:t.stateNode,t};Wt.flushSync=function(t){return Lo(t)};Wt.hydrate=function(t,e,n){if(!Yc(e))throw Error(M(200));return Zc(null,t,e,!0,n)};Wt.hydrateRoot=function(t,e,n){if(!ph(t))throw Error(M(405));var r=n!=null&&n.hydratedSources||null,o=!1,i="",s=Bw;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),e=Pw(e,null,t,1,n!=null?n:null,o,!1,i,s),t[cr]=e.current,Ua(t),r)for(t=0;t<r.length;t++)n=r[t],o=n._getVersion,o=o(n._source),e.mutableSourceEagerHydrationData==null?e.mutableSourceEagerHydrationData=[n,o]:e.mutableSourceEagerHydrationData.push(n,o);return new Gc(e)};Wt.render=function(t,e,n){if(!Yc(e))throw Error(M(200));return Zc(null,t,e,!1,n)};Wt.unmountComponentAtNode=function(t){if(!Yc(t))throw Error(M(40));return t._reactRootContainer?(Lo(function(){Zc(null,null,t,!1,function(){t._reactRootContainer=null,t[cr]=null})}),!0):!1};Wt.unstable_batchedUpdates=ah;Wt.unstable_renderSubtreeIntoContainer=function(t,e,n,r){if(!Yc(n))throw Error(M(200));if(t==null||t._reactInternals===void 0)throw Error(M(38));return Zc(t,e,n,!1,r)};Wt.version="18.2.0-next-9e3b772b8-20220608"});var qc=Rn((KN,Hw)=>{"use strict";function Ww(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Ww)}catch(t){console.error(t)}}Ww(),Hw.exports=Vw()});var Uw=Rn(hh=>{"use strict";var zw=qc();hh.createRoot=zw.createRoot,hh.hydrateRoot=zw.hydrateRoot;var QN});var $w=Rn(Kc=>{"use strict";var e2=se(),t2=Symbol.for("react.element"),n2=Symbol.for("react.fragment"),r2=Object.prototype.hasOwnProperty,o2=e2.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,i2={key:!0,ref:!0,__self:!0,__source:!0};function jw(t,e,n){var r,o={},i=null,s=null;n!==void 0&&(i=""+n),e.key!==void 0&&(i=""+e.key),e.ref!==void 0&&(s=e.ref);for(r in e)r2.call(e,r)&&!i2.hasOwnProperty(r)&&(o[r]=e[r]);if(t&&t.defaultProps)for(r in e=t.defaultProps,e)o[r]===void 0&&(o[r]=e[r]);return{$$typeof:t2,type:t,key:i,ref:s,props:o,_owner:o2.current}}Kc.Fragment=n2;Kc.jsx=jw;Kc.jsxs=jw});var X=Rn((eM,Gw)=>{"use strict";Gw.exports=$w()});var gO={};rC(gO,{default:()=>wf});module.exports=oC(gO);var HS=require("obsidian");function Ff(t){return typeof t=="string"?new Date(t):t}function ay(t,e){let n=Math.abs(e.getTime()-t.getTime());return Math.ceil(n/(1e3*60*60*24))}function Nn(t){return`${t.getFullYear()}-${t.getMonth()<9?"0"+(t.getMonth()+1):t.getMonth()+1}-${t.getDate()<10?"0"+t.getDate():t.getDate()}`}function ly(t,e){return new Date(t,e+1,0).getDate()}function _l(t,e){return(e-t+7)%7}function _f(t,e){return(t-e+6)%7}function uy(t){let e=new Date,n=t<=1?1:t,r=new Date(e.getFullYear()-n+1,0,1),o=new Date(e.getFullYear(),12,0);return{start:r,end:o}}function cy(t){let e=new Date,n=t<=1?1:t,r=new Date(e.getFullYear(),e.getMonth()-n+1,1),o=new Date(e.getFullYear(),e.getMonth()+1,0);return{start:r,end:o}}function Qo(t,e,n,r){let o=new Map;for(let i of t){let s=e(i);o.has(s)?o.set(s,r(o.get(s),n(i))):o.set(s,n(i))}return o}function fy(t,e){for(let n=0;n<e.length;n++)if(t>=e[n].min&&t<e[n].max)return e[n];return null}function dy(t){let e=t.trim();if(e==="")return null;let n=Number(e);return Number.isNaN(n)?null:n}var kl=[{id:"default_b",color:"#9be9a8",min:1,max:2},{id:"default_c",color:"#40c463",min:2,max:5},{id:"default_d",color:"#30a14e",min:5,max:10},{id:"default_e",color:"#216e39",min:10,max:999}];function my(t){if(!t||t.length===0)return[];let n=t.map(i=>i.date instanceof Date?{...i,timestamp:i.date.getTime()}:{...i,date:new Date(i.date),timestamp:new Date(i.date).getTime()}).sort((i,s)=>s.timestamp-i.timestamp),r=n[n.length-1].timestamp,o=n[0].timestamp;return Ol(new Date(r),new Date(o),t)}function Ol(t,e,n){let r=ay(t,e)+1,o=iC(n),i=[];for(let s=0;s<r;s++){let a=new Date(e);a.setDate(a.getDate()-s);let l=Nn(a),u=o.get(l);i.unshift({date:l,weekDay:a.getDay(),month:a.getMonth(),monthDate:a.getDate(),year:a.getFullYear(),value:u?u.value:0,summary:u?u.summary:void 0,items:u?u.items||[]:[]})}return i}function py(t,e=[]){let n=new Date;return n.setDate(n.getDate()-t+1),Ol(n,new Date,e)}function iC(t){let e=new Map;for(let n of t){let r;if(typeof n.date=="string"?r=n.date:r=Nn(n.date),e.has(r)){let o={...n,value:e.get(r).value+n.value};e.set(r,o)}else e.set(r,n)}return e}var Il=require("obsidian");var bl=class{constructor(){this.default="default";this.click_to_reset="click to reset";this.context_menu_create="Add Heatmap";this.form_basic_settings="Basic Settings";this.form_style_settings="Style Settings";this.form_about="About";this.form_contact_me="Contact me";this.form_project_url="Project";this.form_sponsor="Sponsor";this.form_title="Title";this.form_title_placeholder="Input title";this.form_graph_type="Graph Type";this.form_graph_type_git="Git Style";this.form_graph_type_month_track="Month Track";this.form_graph_type_calendar="Calendar";this.form_date_range="Date Range";this.form_date_range_latest_days="Latest Days";this.form_date_range_latest_month="Latest Whole Month";this.form_date_range_latest_year="Latest Whole Year";this.form_date_range_input_placeholder="Input number here";this.form_date_range_fixed_date="Fixed Date";this.form_date_range_start_date="Start Date";this.form_start_of_week="Start of Week";this.form_data_source_value="Source";this.form_data_source_filter_label="Filter";this.form_datasource_filter_type_none="None";this.form_datasource_filter_type_status_is="Status Is";this.form_datasource_filter_type_contains_any_tag="Contains Any Tag";this.form_datasource_filter_type_status_in="Status In";this.form_datasource_filter_task_none="None";this.form_datasource_filter_task_status_completed="Completed";this.form_datasource_filter_task_status_fully_completed="Fully completed";this.form_datasource_filter_task_status_any="Any Status";this.form_datasource_filter_task_status_incomplete="Incomplete";this.form_datasource_filter_task_status_canceled="Canceled";this.form_datasource_filter_contains_tag="Contains Any Tag";this.form_datasource_filter_contains_tag_input_placeholder="Please input tag, such as #todo";this.form_datasource_filter_customize="Customize";this.form_query_placeholder=' such as #tag or "folder"';this.form_date_field="Date Field";this.form_date_field_type_file_name="File Name";this.form_date_field_type_file_ctime="File Create Time";this.form_date_field_type_file_mtime="File Modify Time";this.form_date_field_type_file_specific_page_property="Specific Page Property";this.form_date_field_type_file_specific_task_property="Specific Task Property";this.form_date_field_placeholder="default is file's create time";this.form_date_field_format="Date Field Format";this.form_date_field_format_sample="Sample";this.form_date_field_format_description="If your date property value is not a standard format, you need to specify this field so that the system knows how to recognize your date format";this.form_date_field_format_placeholder="such as yyyy-MM-dd HH:mm:ss";this.form_date_field_format_type_smart="Auto Detect";this.form_date_field_format_type_manual="Specify Format";this.form_count_field_count_field_label="Count Field";this.form_count_field_count_field_input_placeholder="Please input property name";this.form_count_field_count_field_type_default="Default";this.form_count_field_count_field_type_page_prop="Page Property";this.form_count_field_count_field_type_task_prop="Task Property";this.form_title_font_size_label="Title font Size";this.form_number_input_min_warning="allow min value is {value}";this.form_number_input_max_warning="allow max value is {value}";this.form_fill_the_screen_label="Fill The Screen";this.form_main_container_bg_color="Background Color";this.form_enable_main_container_shadow="Enable Shadow";this.form_show_cell_indicators="Show Cell Indicators";this.form_cell_shape="Cell Shape";this.form_cell_shape_circle="Circle";this.form_cell_shape_square="Square";this.form_cell_shape_rounded="Rounded";this.form_cell_min_height="Min Height";this.form_cell_min_width="Min Width";this.form_datasource_type_page="Page";this.form_datasource_type_all_task="All Task";this.form_datasource_type_task_in_specific_page="Task in Specific Page";this.form_theme="Theme";this.form_theme_placeholder="Select theme or customize style";this.form_cell_style_rules="Cell Style Rules";this.form_button_preview="Preview";this.form_button_save="Save";this.weekday_sunday="Sunday";this.weekday_monday="Monday";this.weekday_tuesday="Tuesday";this.weekday_wednesday="Wednesday";this.weekday_thursday="Thursday";this.weekday_friday="Friday";this.weekday_saturday="Saturday";this.you_have_no_contributions_on="No contributions on {date}";this.you_have_contributed_to="{value} contributions on {date}";this.click_to_load_more="Click to load more..."}};var Rl=class{constructor(){this.default="\u9ED8\u8BA4";this.click_to_reset="\u70B9\u51FB\u91CD\u7F6E";this.context_menu_create="\u65B0\u5EFA\u70ED\u529B\u56FE";this.form_basic_settings="\u57FA\u7840\u8BBE\u7F6E";this.form_style_settings="\u6837\u5F0F\u8BBE\u7F6E";this.form_about="\u5173\u4E8E";this.form_contact_me="\u8054\u7CFB\u6211";this.form_project_url="\u9879\u76EE\u5730\u5740";this.form_sponsor="\u8D5E\u52A9";this.form_title="\u6807\u9898";this.form_title_placeholder="\u8F93\u5165\u6807\u9898";this.form_graph_type="\u56FE\u8868\u7C7B\u578B";this.form_graph_type_git="Git \u89C6\u56FE";this.form_graph_type_month_track="\u6708\u8FFD\u8E2A\u89C6\u56FE";this.form_graph_type_calendar="\u65E5\u5386\u89C6\u56FE";this.form_date_range="\u65E5\u671F\u8303\u56F4";this.form_date_range_latest_days="\u6700\u8FD1\u51E0\u5929";this.form_date_range_latest_month="\u6700\u8FD1\u51E0\u4E2A\u6574\u6708";this.form_date_range_latest_year="\u6700\u8FD1\u51E0\u4E2A\u6574\u5E74";this.form_date_range_input_placeholder="\u5728\u8FD9\u91CC\u8F93\u5165\u6570\u503C";this.form_date_range_fixed_date="\u56FA\u5B9A\u65E5\u671F";this.form_date_range_start_date="\u5F00\u59CB\u65E5\u671F";this.form_start_of_week="\u6BCF\u5468\u5F00\u59CB\u4E8E";this.form_data_source_value="\u6765\u6E90";this.form_data_source_filter_label="\u7B5B\u9009";this.form_datasource_filter_type_none="\u65E0";this.form_datasource_filter_type_status_is="\u72B6\u6001\u7B49\u4E8E";this.form_datasource_filter_type_contains_any_tag="\u5305\u542B\u4EFB\u610F\u6807\u7B7E";this.form_datasource_filter_type_status_in="\u5305\u542B\u4EFB\u610F\u4E00\u4E2A\u72B6\u6001";this.form_datasource_filter_task_none="\u65E0";this.form_datasource_filter_task_status_completed="\u5DF2\u5B8C\u6210\uFF08\u4E0D\u5305\u542B\u5B50\u4EFB\u52A1\uFF09";this.form_datasource_filter_task_status_fully_completed="\u5DF2\u5B8C\u6210\uFF08\u5305\u542B\u5B50\u4EFB\u52A1\uFF09";this.form_datasource_filter_task_status_canceled="\u5DF2\u53D6\u6D88";this.form_datasource_filter_task_status_any="\u4EFB\u610F\u72B6\u6001";this.form_datasource_filter_task_status_incomplete="\u672A\u5B8C\u6210";this.form_datasource_filter_contains_tag="\u5305\u542B\u4EFB\u610F\u4E00\u4E2A\u6807\u7B7E";this.form_datasource_filter_contains_tag_input_placeholder="\u8BF7\u8F93\u5165\u6807\u7B7E\uFF0C\u6BD4\u5982 #todo";this.form_datasource_filter_customize="\u81EA\u5B9A\u4E49";this.form_query_placeholder='\u6BD4\u5982 #tag \u6216 "folder"';this.form_date_field="\u65E5\u671F\u5B57\u6BB5";this.form_date_field_type_file_name="\u6587\u4EF6\u540D\u79F0";this.form_date_field_type_file_ctime="\u6587\u4EF6\u521B\u5EFA\u65E5\u671F";this.form_date_field_type_file_mtime="\u6587\u4EF6\u4FEE\u6539\u65E5\u671F";this.form_date_field_type_file_specific_page_property="\u6307\u5B9A\u6587\u6863\u5C5E\u6027";this.form_date_field_type_file_specific_task_property="\u6307\u5B9A\u4EFB\u52A1\u5C5E\u6027";this.form_date_field_placeholder="\u9ED8\u8BA4\u4E3A\u6587\u4EF6\u7684\u521B\u5EFA\u65E5\u671F";this.form_date_field_format="\u65E5\u671F\u683C\u5F0F";this.form_date_field_format_sample="\u793A\u4F8B\u503C";this.form_date_field_format_description="\u5982\u679C\u4F60\u7684\u65E5\u671F\u5C5E\u6027\u503C\u4E0D\u662F\u6807\u51C6\u7684\u683C\u5F0F\uFF0C\u9700\u8981\u6307\u5B9A\u8BE5\u5B57\u6BB5\u8BA9\u7CFB\u7EDF\u77E5\u9053\u5982\u4F55\u8BC6\u522B\u4F60\u7684\u65E5\u671F\u683C\u5F0F";this.form_date_field_format_placeholder="\u6BD4\u5982 yyyy-MM-dd HH:mm:ss";this.form_date_field_format_type_smart="\u81EA\u52A8\u8BC6\u522B";this.form_date_field_format_type_manual="\u6307\u5B9A\u683C\u5F0F";this.form_count_field_count_field_label="\u6253\u5206\u5C5E\u6027";this.form_count_field_count_field_input_placeholder="\u8BF7\u8F93\u5165\u5C5E\u6027\u540D\u79F0";this.form_count_field_count_field_type_default="\u9ED8\u8BA4";this.form_count_field_count_field_type_page_prop="\u6587\u6863\u5C5E\u6027";this.form_count_field_count_field_type_task_prop="\u4EFB\u52A1\u5C5E\u6027";this.form_title_font_size_label="\u6807\u9898\u5B57\u4F53\u5927\u5C0F";this.form_number_input_min_warning="\u5141\u8BB8\u7684\u6700\u5C0F\u503C\u4E3A {value}";this.form_number_input_max_warning="\u5141\u8BB8\u7684\u6700\u5927\u503C\u4E3A {value}";this.form_fill_the_screen_label="\u5145\u6EE1\u5C4F\u5E55";this.form_main_container_bg_color="\u80CC\u666F\u989C\u8272";this.form_enable_main_container_shadow="\u542F\u7528\u9634\u5F71";this.form_show_cell_indicators="\u663E\u793A\u5355\u5143\u683C\u6307\u793A\u5668";this.form_cell_shape="\u5355\u5143\u683C\u5F62\u72B6";this.form_cell_shape_circle="\u5706\u5F62";this.form_cell_shape_square="\u65B9\u5757";this.form_cell_shape_rounded="\u5706\u89D2";this.form_cell_min_height="\u5355\u5143\u683C\u6700\u5C0F\u9AD8\u5EA6";this.form_cell_min_width="\u5355\u5143\u683C\u6700\u5C0F\u5BBD\u5EA6";this.form_datasource_type_page="\u6587\u6863";this.form_datasource_type_all_task="\u6240\u6709\u4EFB\u52A1";this.form_datasource_type_task_in_specific_page="\u6307\u5B9A\u6587\u6863\u4E2D\u7684\u4EFB\u52A1";this.form_theme="\u4E3B\u9898";this.form_theme_placeholder="\u9009\u62E9\u4E3B\u9898\u6216\u81EA\u5B9A\u4E49\u6837\u5F0F";this.form_cell_style_rules="\u5355\u5143\u683C\u6837\u5F0F\u89C4\u5219";this.form_button_preview="\u9884\u89C8";this.form_button_save="\u4FDD\u5B58";this.weekday_sunday="\u5468\u65E5";this.weekday_monday="\u5468\u4E00";this.weekday_tuesday="\u5468\u4E8C";this.weekday_wednesday="\u5468\u4E09";this.weekday_thursday="\u5468\u56DB";this.weekday_friday="\u5468\u4E94";this.weekday_saturday="\u5468\u516D";this.you_have_no_contributions_on="\u4F60\u5728 {date} \u6CA1\u6709\u4EFB\u4F55\u8D21\u732E";this.you_have_contributed_to="\u4F60\u5728 {date} \u6709 {value} \u6B21\u8D21\u732E";this.click_to_load_more="\u70B9\u51FB\u52A0\u8F7D\u66F4\u591A......"}};var Q=class{static get(){return window.localStorage.getItem("language")==="zh"?new Rl:new bl}};function ws(){return window.localStorage.getItem("language")==="zh"}var sC=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],aC=["\u65E5","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D"],hy=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function Jo(t){return window.localStorage.getItem("language")==="zh"?`${t+1}\u6708`:hy[t]}function Nl(t,e){let n=window.localStorage.getItem("language"),r;return n==="zh"?r=aC[t]:r=sC[t],e?r.substring(0,e):r}function Ml(t,e){return window.localStorage.getItem("language")==="zh"?`${t}\u5E74${e+1}\u6708`:`${hy[e]} ${t}`}var xr=class{constructor(){}render(e,n){throw new Error("Method not implemented.")}createGraphEl(e){return createDiv({cls:"contribution-graph",parent:e})}createMainEl(e,n){let r="main";n.fillTheScreen&&this.graphType()!="calendar"&&(r=`main ${n.fillTheScreen?"fill-the-screen":""}`);let o=createDiv({cls:r,parent:e});return n.mainContainerStyle&&Object.assign(o.style,n.mainContainerStyle),o}renderTitle(e,n){let r=document.createElement("div");return r.className="title",e.title&&(r.innerText=e.title),e.titleStyle&&Object.assign(r.style,e.titleStyle),n.appendChild(r),r}renderCellRuleIndicator(e,n){if(e.showCellRuleIndicators===!1)return;let r=createDiv({cls:"cell-rule-indicator-container",parent:n}),o=this.getCellRules(e);createDiv({cls:"cell text",text:"less",parent:r}),o.sort((i,s)=>i.min-s.min).forEach(i=>{let s=createDiv({cls:["cell"],parent:r});s.className="cell",s.style.backgroundColor=i.color,s.innerText=i.text||"";let a=`${i.min} \u2264 contributions \uFF1C ${i.max}`;(0,Il.setTooltip)(s,a)}),createDiv({cls:"cell text",text:"more",parent:r})}renderActivityContainer(e,n){return createDiv({cls:"activity-container",parent:n})}renderActivity(e,n,r){r.empty();let o=createEl("button",{cls:"close-button",text:"x",parent:r});o.onclick=()=>{r.empty()};let i;if(n.value>0?i=Q.get().you_have_contributed_to.replace("{date}",n.date).replace("{value}",n.value.toString()):i=Q.get().you_have_no_contributions_on.replace("{date}",n.date).replace("{value}","0"),createDiv({cls:"activity-summary",parent:r,text:i}),(n.items||[]).length===0)return;let s=createDiv({cls:"activity-content",parent:r}),a=createDiv({cls:"activity-list",parent:s}),l=10,u=n.items||[];yy(u.slice(0,l),a);let d=createDiv({cls:"activity-navigation",parent:s}),f=1;if(u.length>l){let m=createEl("a",{text:Q.get().click_to_load_more,href:"#",parent:d});m.onclick=c=>{c.preventDefault(),f++,yy(u.slice((f-1)*l,f*l),a),f*l>=u.length&&m.remove()}}}generateContributionData(e){if(e.days)return py(e.days,e.data);if(e.fromDate&&e.toDate){let n=Ff(e.fromDate),r=Ff(e.toDate);return Ol(n,r,e.data)}else return my(e.data)}getCellRules(e){return e.cellStyleRules&&e.cellStyleRules.length>0?e.cellStyleRules:kl}bindMonthTips(e,n,r){let o=`${n.year}-${n.month+1}`,i=r.get(o)||0;(0,Il.setTooltip)(e,`${i} contributions on ${o}.`)}applyCellGlobalStyle(e,n){n.cellStyle&&Object.assign(e.style,n.cellStyle)}applyCellGlobalStylePartial(e,n,r){if(n.cellStyle){let o=r.reduce((i,s)=>(i[s]=n.cellStyle[s],i),{});Object.assign(e.style,o)}}applyCellStyleRule(e,n,r,o){let i=fy(n.value,r);if(i!=null){e.style.backgroundColor=i.color,e.innerText=i.text||"";return}if(o){let s=o();e.style.backgroundColor=s.color,e.innerText=s.text||""}}bindCellAttribute(e,n){e.setAttribute("data-year",n.year.toString()),e.setAttribute("data-month",n.month.toString()),e.setAttribute("data-date",n.date.toString())}bindCellClickEvent(e,n,r,o){e.onclick=i=>{r.onCellClick&&r.onCellClick(n,i),o&&this.renderActivity(r,n,o)}}bindCellTips(e,n){e.addEventListener("mouseenter",r=>{let o=n.summary?n.summary:`${n.value} contributions on ${n.date}.`;(0,Il.setTooltip)(e,o)})}};function yy(t,e){(t||[]).slice(0,10).forEach(n=>{let r=createDiv({cls:"activity-item",parent:e}),o=createEl("a",{text:n.label,href:"#",parent:r,cls:"label"});o.onclick=i=>{i.preventDefault(),n.open&&n.open(i)}})}var Al=class extends xr{constructor(){super()}graphType(){return"calendar"}render(e,n){let r=this.createGraphEl(e),o=this.createMainEl(r,n);n.title&&n.title.trim()!=""&&this.renderTitle(n,o);let i=createDiv({cls:["charts","calendar"],parent:o});this.renderCellRuleIndicator(n,o);let s=this.renderActivityContainer(n,o),a=this.generateContributionData(n).filter(c=>c.date!="$HOLE$");if(a.length>0){let c=a[0],y=c.monthDate-1,v=new Date(c.date);for(let S=0;S<y;S++)v.setDate(v.getDate()-1),a.unshift({date:"$HOLE$",weekDay:v.getDay(),month:v.getMonth(),monthDate:v.getDate(),year:v.getFullYear(),value:0})}if(a.length>0){let c=a[a.length-1],v=ly(c.year,c.month)-c.monthDate,S=new Date(c.date);for(let h=0;h<v;h++)S.setDate(S.getDate()+1),a.push({date:Nn(S),weekDay:S.getDay(),month:S.getMonth(),monthDate:S.getDate(),year:S.getFullYear(),value:0})}let l=Qo(a,c=>`${c.year}-${c.month+1}`,c=>c.value,(c,y)=>c+y),u=this.getCellRules(n),d="",f,m=null;for(let c=0;c<a.length;c++){let y=a[c],v=`${y.year}-${y.month+1}`;if(v!=d){d=v,f=document.createElement("div"),f.className="month-container",i.appendChild(f);let h=document.createElement("div");h.className="month-indicator",y.month==0?h.innerText=Ml(y.year,y.month):h.innerText=Jo(y.month),f.appendChild(h),this.bindMonthTips(h,y,l);let p=createDiv({cls:["row","week-indicator-container"],parent:f});for(let D=0;D<7;D++){let x=document.createElement("div");x.className="cell week-indicator",this.applyCellGlobalStylePartial(x,n,["minWidth","minHeight"]);let O=Nl(((n.startOfWeek||0)+7+D)%7,2);x.innerText=O,p.appendChild(x)}m=document.createElement("div"),m.className="row",f==null||f.appendChild(m);let E=_l(n.startOfWeek||0,y.weekDay);for(let D=0;D<E;D++){let x=document.createElement("div");x.className="cell",this.applyCellGlobalStylePartial(x,n,["minWidth","minHeight"]),m==null||m.appendChild(x)}}(m==null||y.weekDay==(n.startOfWeek||0))&&(m=document.createElement("div"),m.className="row",f==null||f.appendChild(m));let S=document.createElement("div");if(m==null||m.appendChild(S),S.className="cell",y.date=="$HOLE$"?(S.innerText="\xB7\xB7\xB7",S.className="cell",this.applyCellGlobalStylePartial(S,n,["minWidth","minHeight"])):y.value==0?(S.className="cell empty",this.applyCellGlobalStyle(S,n),this.applyCellStyleRule(S,y,u),this.bindCellAttribute(S,y)):(S.className="cell",this.applyCellGlobalStyle(S,n),this.applyCellStyleRule(S,y,u,()=>u[0]),this.bindCellAttribute(S,y),this.bindCellClickEvent(S,y,n,s),this.bindCellTips(S,y)),c+1<a.length){if(a[c+1].month!=y.month){let p=_f(n.startOfWeek||0,y.weekDay);for(let E=0;E<p;E++){let D=document.createElement("div");D.className="cell",this.applyCellGlobalStylePartial(D,n,["minWidth","minHeight"]),m==null||m.appendChild(D)}}}else if(c+1==a.length){let h=_f(n.startOfWeek||0,y.weekDay);for(let p=0;p<h;p++){let E=document.createElement("div");E.className="cell",this.applyCellGlobalStylePartial(E,n,["minWidth","minHeight"]),m==null||m.appendChild(E)}}}}};var Ll=class extends xr{constructor(){super()}graphType(){return"month-track"}render(e,n){let r=this.createGraphEl(e),o=this.createMainEl(r,n);n.title&&n.title.trim()!=""&&this.renderTitle(n,o);let i=createDiv({cls:["charts","month-track"],parent:o});this.renderCellRuleIndicator(n,o);let s=createDiv({cls:"row",parent:i});s.appendChild(createDiv({cls:"cell month-indicator",text:""})),this.renderMonthDateIndicator(s,n);let a=this.renderActivityContainer(n,o),l=this.generateContributionData(n).filter(c=>c.date!="$HOLE$"),u=Qo(l,c=>`${c.year}-${c.month+1}`,c=>c.value,(c,y)=>c+y),d=this.getCellRules(n),f,m="";for(let c=0;c<l.length;c++){let y=l[c],v=`${y.year}-${y.month}`;if(v!=m){if(c>0){let E=31-l[c-1].monthDate;for(let D=0;D<E;D++){let x=document.createElement("div");x.className="cell",this.applyCellGlobalStylePartial(x,n,["minWidth","minHeight"]),f==null||f.appendChild(x)}}f=document.createElement("div"),f.className="row",i.appendChild(f),m=v;let h=document.createElement("div");h.className="cell month-indicator",h.innerText=y.month==0?Ml(y.year,y.month):Jo(y.month),this.bindMonthTips(h,y,u),f.appendChild(h)}if(c==0){let p=new Date(y.date).getDate()-1;for(let E=0;E<p;E++){let D=document.createElement("div");D.className="cell",D.innerText="\xB7\xB7\xB7",this.applyCellGlobalStylePartial(D,n,["minWidth","minHeight"]),f==null||f.appendChild(D)}}let S=document.createElement("div");this.applyCellGlobalStyle(S,n),f==null||f.appendChild(S),y.value==0?(S.className="cell empty",this.applyCellStyleRule(S,y,d),this.bindCellAttribute(S,y)):(S.className="cell",this.applyCellStyleRule(S,y,d,()=>d[0]),this.bindCellAttribute(S,y),this.bindCellClickEvent(S,y,n,a),this.bindCellTips(S,y))}if(l.length>0){let y=31-l[l.length-1].monthDate;for(let v=0;v<y;v++){let S=document.createElement("div");S.className="cell",this.applyCellGlobalStylePartial(S,n,["minWidth","minHeight"]),f==null||f.appendChild(S)}}}renderMonthDateIndicator(e,n){for(let r=0;r<31;r++){let o=document.createElement("div");o.className="cell date-indicator",this.applyCellGlobalStylePartial(o,n,["minWidth","minHeight"]),o.innerText=`${r+1}`,e.appendChild(o)}}};var Pl=class extends xr{constructor(){super()}graphType(){return"default"}render(e,n){let r=this.createGraphEl(e),o=this.createMainEl(r,n);n.title&&n.title.trim()!=""&&this.renderTitle(n,o);let i=createDiv({cls:["charts","default"],parent:o});this.renderCellRuleIndicator(n,o);let s=this.renderActivityContainer(n,o),a=createDiv({cls:"column",parent:i});this.renderWeekIndicator(a,n);let l=this.generateContributionData(n);if(l.length>0){let c=new Date(l[0].date).getDay(),y=_l(n.startOfWeek||0,c);for(let v=0;v<y;v++)l.unshift({date:"$HOLE$",weekDay:-1,month:-1,monthDate:-1,year:-1,value:0})}let u=Qo(l,m=>`${m.year}-${m.month+1}`,m=>m.value,(m,c)=>m+c),d=this.getCellRules(n),f;for(let m=0;m<l.length;m++){m%7==0&&(f=document.createElement("div"),f.className="column",i.appendChild(f));let c=l[m];if(c.monthDate==1){let v=createDiv({cls:"month-indicator",parent:f,text:""});v.innerText=Jo(c.month),this.bindMonthTips(v,c,u)}let y=document.createElement("div");f==null||f.appendChild(y),c.value==0?c.date!="$HOLE$"?(y.className="cell empty",this.applyCellGlobalStyle(y,n),this.applyCellStyleRule(y,c,d),this.bindCellAttribute(y,c)):(y.className="cell",this.applyCellGlobalStylePartial(y,n,["minWidth","minHeight"])):(y.className="cell",this.applyCellGlobalStyle(y,n),this.applyCellStyleRule(y,c,d,()=>d[0]),this.bindCellAttribute(y,c),this.bindCellClickEvent(y,c,n,s),this.bindCellTips(y,c))}}renderWeekIndicator(e,n){let r=n.startOfWeek||0;for(let o=0;o<7;o++){let i=document.createElement("div");switch(i.className="cell week-indicator",this.applyCellGlobalStyle(i,n),o){case 1:case 3:case 5:i.innerText=Nl((o+r||0)%7);break;default:break}e.appendChild(i)}}};var Bl=class{static render(e,n){n.graphType===void 0&&(n.graphType="default");let r=this.renders.find(o=>o.graphType()===n.graphType);r?r.render(e,n):this.renderErrorTips(e,`invalid graphType "${n.graphType}"`,[`please set graphType to one of ${Bl.renders.map(o=>o.graphType()).join(", ")}`])}static renderErrorTips(e,n,r){e.empty();let o=createDiv({cls:"contribution-graph-render-error-container",parent:e});createEl("p",{text:n,cls:"summary",parent:o}),r&&r.forEach(i=>{createEl("pre",{text:i,cls:"recommend",parent:o})})}static renderError(e,{summary:n,recommends:r}){Bl.renderErrorTips(e,n,r)}},ln=Bl;ln.renders=[new Al,new Ll,new Pl];var lD=require("obsidian");function Xo(t,e){return{summary:t,recommends:e}}var Vl=t=>Xo("Empty Graph, please add config, for example",[`title: 'Contributions'
days: 365
dataSource: '#tag' # means all notes with tag 'tag'
  type: "page" # or "task"
  value: '""' # means all notes in folder`]),gy=t=>Xo("please set dataSource or data property, for example",[`dataSource: '#tag' # means all notes with tag 'tag'
  type: "page" # or "task"
  value: '""' # means all notes
days: 365`,`dataSource: '#tag and "folder"' # means all notes with tag 'tag' and in folder 'folder', folder should surrounded by quotes
  type: "page" # or "task"
	value: '""' # means all notes
fromDate: '2023-01-01' 
toDate: '2023-12-31'  `]),Dy=t=>Xo(`graphType "${t}" is invalid, value must be one of [default, month-track, calendar], for example`,[`graphType: 'default'
days: 365
dataSource: '#tag' # means all notes with tag 'tag'
  type: "page" # or "task"
  value: '""' # means all notes in folde `]),vy=t=>Xo("please set dateRangeValue or fromDate and toDate property, for example",[`dateRangeType: LATEST_DAYS
dateRangeValue: 365
dataSource: '#tag' # means all notes with tag 'tag'
  type: "page" # or "task"
  value: '""' # means all notes in folde `,`dateRangeType: FIXED_DATE_RANGE
fromDate: '2023-01-01'
toDate: '2023-12-31'
dataSource: '#tag' # means all notes with tag 'tag'
  type: "page" # or "task"
  value: '""' # means all notes in folde`]),kf=t=>Xo(`"${t}" is invalid, fromDate and toDate must be yyyy-MM-dd, for example`,[`fromDate: '2023-01-01'
toDate: '2023-12-31'
data: []`]),Of=t=>Xo(`startOfWeek value ${t} is invalid, should be 0~6, 0=Sunday, 1=Monday, 2=Thursday and etc. for example`,[`fromDate: '2023-01-01'
toDate: '2023-12-31'
data: []
startOfWeek: 1`]);var Ne=class{constructor({summary:e,recommends:n}){this.summary=e,this.recommends=n||[]}};var Kd=require("obsidian"),aD=B(ud());var Ln=class{constructor(e,n){this.date=n,this.raw=e}};var tr=class extends Error{},su=class extends tr{constructor(e){super(`Invalid DateTime: ${e.toMessage()}`)}},au=class extends tr{constructor(e){super(`Invalid Interval: ${e.toMessage()}`)}},lu=class extends tr{constructor(e){super(`Invalid Duration: ${e.toMessage()}`)}},pn=class extends tr{},ci=class extends tr{constructor(e){super(`Invalid unit ${e}`)}},We=class extends tr{},hn=class extends tr{constructor(){super("Zone is an abstract class")}};var U="numeric",yn="short",Mt="long",kr={year:U,month:U,day:U},As={year:U,month:yn,day:U},cd={year:U,month:yn,day:U,weekday:yn},Ls={year:U,month:Mt,day:U},Ps={year:U,month:Mt,day:U,weekday:Mt},Bs={hour:U,minute:U},Vs={hour:U,minute:U,second:U},Ws={hour:U,minute:U,second:U,timeZoneName:yn},Hs={hour:U,minute:U,second:U,timeZoneName:Mt},zs={hour:U,minute:U,hourCycle:"h23"},Us={hour:U,minute:U,second:U,hourCycle:"h23"},js={hour:U,minute:U,second:U,hourCycle:"h23",timeZoneName:yn},$s={hour:U,minute:U,second:U,hourCycle:"h23",timeZoneName:Mt},Gs={year:U,month:U,day:U,hour:U,minute:U},Ys={year:U,month:U,day:U,hour:U,minute:U,second:U},Zs={year:U,month:yn,day:U,hour:U,minute:U},qs={year:U,month:yn,day:U,hour:U,minute:U,second:U},fd={year:U,month:yn,day:U,weekday:yn,hour:U,minute:U},Ks={year:U,month:Mt,day:U,hour:U,minute:U,timeZoneName:yn},Qs={year:U,month:Mt,day:U,hour:U,minute:U,second:U,timeZoneName:yn},Js={year:U,month:Mt,day:U,weekday:Mt,hour:U,minute:U,timeZoneName:Mt},Xs={year:U,month:Mt,day:U,weekday:Mt,hour:U,minute:U,second:U,timeZoneName:Mt};var St=class{get type(){throw new hn}get name(){throw new hn}get ianaName(){return this.name}get isUniversal(){throw new hn}offsetName(e,n){throw new hn}formatOffset(e,n){throw new hn}offset(e){throw new hn}equals(e){throw new hn}get isValid(){throw new hn}};var dd=null,Pn=class extends St{static get instance(){return dd===null&&(dd=new Pn),dd}get type(){return"system"}get name(){return new Intl.DateTimeFormat().resolvedOptions().timeZone}get isUniversal(){return!1}offsetName(e,{format:n,locale:r}){return cu(e,n,r)}formatOffset(e,n){return Or(this.offset(e),n)}offset(e){return-new Date(e).getTimezoneOffset()}equals(e){return e.type==="system"}get isValid(){return!0}};var du={};function fT(t){return du[t]||(du[t]=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:t,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",era:"short"})),du[t]}var dT={year:0,month:1,day:2,era:3,hour:4,minute:5,second:6};function mT(t,e){let n=t.format(e).replace(/\u200E/g,""),r=/(\d+)\/(\d+)\/(\d+) (AD|BC),? (\d+):(\d+):(\d+)/.exec(n),[,o,i,s,a,l,u,d]=r;return[s,o,i,a,l,u,d]}function pT(t,e){let n=t.formatToParts(e),r=[];for(let o=0;o<n.length;o++){let{type:i,value:s}=n[o],a=dT[i];i==="era"?r[a]=s:J(a)||(r[a]=parseInt(s,10))}return r}var fu={},$e=class extends St{static create(e){return fu[e]||(fu[e]=new $e(e)),fu[e]}static resetCache(){fu={},du={}}static isValidSpecifier(e){return this.isValidZone(e)}static isValidZone(e){if(!e)return!1;try{return new Intl.DateTimeFormat("en-US",{timeZone:e}).format(),!0}catch(n){return!1}}constructor(e){super(),this.zoneName=e,this.valid=$e.isValidZone(e)}get type(){return"iana"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(e,{format:n,locale:r}){return cu(e,n,r,this.name)}formatOffset(e,n){return Or(this.offset(e),n)}offset(e){let n=new Date(e);if(isNaN(n))return NaN;let r=fT(this.name),[o,i,s,a,l,u,d]=r.formatToParts?pT(r,n):mT(r,n);a==="BC"&&(o=-Math.abs(o)+1);let m=fi({year:o,month:i,day:s,hour:l===24?0:l,minute:u,second:d,millisecond:0}),c=+n,y=c%1e3;return c-=y>=0?y:1e3+y,(m-c)/(60*1e3)}equals(e){return e.type==="iana"&&e.name===this.name}get isValid(){return this.valid}};var Kg={};function hT(t,e={}){let n=JSON.stringify([t,e]),r=Kg[n];return r||(r=new Intl.ListFormat(t,e),Kg[n]=r),r}var md={};function pd(t,e={}){let n=JSON.stringify([t,e]),r=md[n];return r||(r=new Intl.DateTimeFormat(t,e),md[n]=r),r}var hd={};function yT(t,e={}){let n=JSON.stringify([t,e]),r=hd[n];return r||(r=new Intl.NumberFormat(t,e),hd[n]=r),r}var yd={};function gT(t,e={}){let{base:n,...r}=e,o=JSON.stringify([t,r]),i=yd[o];return i||(i=new Intl.RelativeTimeFormat(t,e),yd[o]=i),i}var ea=null;function DT(){return ea||(ea=new Intl.DateTimeFormat().resolvedOptions().locale,ea)}var Qg={};function vT(t){let e=Qg[t];if(!e){let n=new Intl.Locale(t);e="getWeekInfo"in n?n.getWeekInfo():n.weekInfo,Qg[t]=e}return e}function ET(t){let e=t.indexOf("-x-");e!==-1&&(t=t.substring(0,e));let n=t.indexOf("-u-");if(n===-1)return[t];{let r,o;try{r=pd(t).resolvedOptions(),o=t}catch(a){let l=t.substring(0,n);r=pd(l).resolvedOptions(),o=l}let{numberingSystem:i,calendar:s}=r;return[o,i,s]}}function wT(t,e,n){return(n||e)&&(t.includes("-u-")||(t+="-u"),n&&(t+=`-ca-${n}`),e&&(t+=`-nu-${e}`)),t}function ST(t){let e=[];for(let n=1;n<=12;n++){let r=K.utc(2009,n,1);e.push(t(r))}return e}function CT(t){let e=[];for(let n=1;n<=7;n++){let r=K.utc(2016,11,13+n);e.push(t(r))}return e}function mu(t,e,n,r){let o=t.listingMode();return o==="error"?null:o==="en"?n(e):r(e)}function xT(t){return t.numberingSystem&&t.numberingSystem!=="latn"?!1:t.numberingSystem==="latn"||!t.locale||t.locale.startsWith("en")||new Intl.DateTimeFormat(t.intl).resolvedOptions().numberingSystem==="latn"}var gd=class{constructor(e,n,r){this.padTo=r.padTo||0,this.floor=r.floor||!1;let{padTo:o,floor:i,...s}=r;if(!n||Object.keys(s).length>0){let a={useGrouping:!1,...r};r.padTo>0&&(a.minimumIntegerDigits=r.padTo),this.inf=yT(e,a)}}format(e){if(this.inf){let n=this.floor?Math.floor(e):e;return this.inf.format(n)}else{let n=this.floor?Math.floor(e):di(e,3);return Fe(n,this.padTo)}}},Dd=class{constructor(e,n,r){this.opts=r,this.originalZone=void 0;let o;if(this.opts.timeZone)this.dt=e;else if(e.zone.type==="fixed"){let s=-1*(e.offset/60),a=s>=0?`Etc/GMT+${s}`:`Etc/GMT${s}`;e.offset!==0&&$e.create(a).valid?(o=a,this.dt=e):(o="UTC",this.dt=e.offset===0?e:e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone)}else e.zone.type==="system"?this.dt=e:e.zone.type==="iana"?(this.dt=e,o=e.zone.name):(o="UTC",this.dt=e.setZone("UTC").plus({minutes:e.offset}),this.originalZone=e.zone);let i={...this.opts};i.timeZone=i.timeZone||o,this.dtf=pd(n,i)}format(){return this.originalZone?this.formatToParts().map(({value:e})=>e).join(""):this.dtf.format(this.dt.toJSDate())}formatToParts(){let e=this.dtf.formatToParts(this.dt.toJSDate());return this.originalZone?e.map(n=>{if(n.type==="timeZoneName"){let r=this.originalZone.offsetName(this.dt.ts,{locale:this.dt.locale,format:this.opts.timeZoneName});return{...n,value:r}}else return n}):e}resolvedOptions(){return this.dtf.resolvedOptions()}},vd=class{constructor(e,n,r){this.opts={style:"long",...r},!n&&pu()&&(this.rtf=gT(e,r))}format(e,n){return this.rtf?this.rtf.format(e,n):Jg(n,e,this.opts.numeric,this.opts.style!=="long")}formatToParts(e,n){return this.rtf?this.rtf.formatToParts(e,n):[]}},TT={firstDay:1,minimalDays:4,weekend:[6,7]},ue=class{static fromOpts(e){return ue.create(e.locale,e.numberingSystem,e.outputCalendar,e.weekSettings,e.defaultToEN)}static create(e,n,r,o,i=!1){let s=e||de.defaultLocale,a=s||(i?"en-US":DT()),l=n||de.defaultNumberingSystem,u=r||de.defaultOutputCalendar,d=ta(o)||de.defaultWeekSettings;return new ue(a,l,u,d,s)}static resetCache(){ea=null,md={},hd={},yd={}}static fromObject({locale:e,numberingSystem:n,outputCalendar:r,weekSettings:o}={}){return ue.create(e,n,r,o)}constructor(e,n,r,o,i){let[s,a,l]=ET(e);this.locale=s,this.numberingSystem=n||a||null,this.outputCalendar=r||l||null,this.weekSettings=o,this.intl=wT(this.locale,this.numberingSystem,this.outputCalendar),this.weekdaysCache={format:{},standalone:{}},this.monthsCache={format:{},standalone:{}},this.meridiemCache=null,this.eraCache={},this.specifiedLocale=i,this.fastNumbersCached=null}get fastNumbers(){return this.fastNumbersCached==null&&(this.fastNumbersCached=xT(this)),this.fastNumbersCached}listingMode(){let e=this.isEnglish(),n=(this.numberingSystem===null||this.numberingSystem==="latn")&&(this.outputCalendar===null||this.outputCalendar==="gregory");return e&&n?"en":"intl"}clone(e){return!e||Object.getOwnPropertyNames(e).length===0?this:ue.create(e.locale||this.specifiedLocale,e.numberingSystem||this.numberingSystem,e.outputCalendar||this.outputCalendar,ta(e.weekSettings)||this.weekSettings,e.defaultToEN||!1)}redefaultToEN(e={}){return this.clone({...e,defaultToEN:!0})}redefaultToSystem(e={}){return this.clone({...e,defaultToEN:!1})}months(e,n=!1){return mu(this,e,Ed,()=>{let r=n?{month:e,day:"numeric"}:{month:e},o=n?"format":"standalone";return this.monthsCache[o][e]||(this.monthsCache[o][e]=ST(i=>this.extract(i,r,"month"))),this.monthsCache[o][e]})}weekdays(e,n=!1){return mu(this,e,wd,()=>{let r=n?{weekday:e,year:"numeric",month:"long",day:"numeric"}:{weekday:e},o=n?"format":"standalone";return this.weekdaysCache[o][e]||(this.weekdaysCache[o][e]=CT(i=>this.extract(i,r,"weekday"))),this.weekdaysCache[o][e]})}meridiems(){return mu(this,void 0,()=>Sd,()=>{if(!this.meridiemCache){let e={hour:"numeric",hourCycle:"h12"};this.meridiemCache=[K.utc(2016,11,13,9),K.utc(2016,11,13,19)].map(n=>this.extract(n,e,"dayperiod"))}return this.meridiemCache})}eras(e){return mu(this,e,Cd,()=>{let n={era:e};return this.eraCache[e]||(this.eraCache[e]=[K.utc(-40,1,1),K.utc(2017,1,1)].map(r=>this.extract(r,n,"era"))),this.eraCache[e]})}extract(e,n,r){let o=this.dtFormatter(e,n),i=o.formatToParts(),s=i.find(a=>a.type.toLowerCase()===r);return s?s.value:null}numberFormatter(e={}){return new gd(this.intl,e.forceSimple||this.fastNumbers,e)}dtFormatter(e,n={}){return new Dd(e,this.intl,n)}relFormatter(e={}){return new vd(this.intl,this.isEnglish(),e)}listFormatter(e={}){return hT(this.intl,e)}isEnglish(){return this.locale==="en"||this.locale.toLowerCase()==="en-us"||new Intl.DateTimeFormat(this.intl).resolvedOptions().locale.startsWith("en-us")}getWeekSettings(){return this.weekSettings?this.weekSettings:hu()?vT(this.locale):TT}getStartOfWeek(){return this.getWeekSettings().firstDay}getMinDaysInFirstWeek(){return this.getWeekSettings().minimalDays}getWeekendDays(){return this.getWeekSettings().weekend}equals(e){return this.locale===e.locale&&this.numberingSystem===e.numberingSystem&&this.outputCalendar===e.outputCalendar}};var Td=null,_e=class extends St{static get utcInstance(){return Td===null&&(Td=new _e(0)),Td}static instance(e){return e===0?_e.utcInstance:new _e(e)}static parseSpecifier(e){if(e){let n=e.match(/^utc(?:([+-]\d{1,2})(?::(\d{2}))?)?$/i);if(n)return new _e(Do(n[1],n[2]))}return null}constructor(e){super(),this.fixed=e}get type(){return"fixed"}get name(){return this.fixed===0?"UTC":`UTC${Or(this.fixed,"narrow")}`}get ianaName(){return this.fixed===0?"Etc/UTC":`Etc/GMT${Or(-this.fixed,"narrow")}`}offsetName(){return this.name}formatOffset(e,n){return Or(this.fixed,n)}get isUniversal(){return!0}offset(){return this.fixed}equals(e){return e.type==="fixed"&&e.fixed===this.fixed}get isValid(){return!0}};var mi=class extends St{constructor(e){super(),this.zoneName=e}get type(){return"invalid"}get name(){return this.zoneName}get isUniversal(){return!1}offsetName(){return null}formatOffset(){return""}offset(){return NaN}equals(){return!1}get isValid(){return!1}};function gn(t,e){let n;if(J(t)||t===null)return e;if(t instanceof St)return t;if(Xg(t)){let r=t.toLowerCase();return r==="default"?e:r==="local"||r==="system"?Pn.instance:r==="utc"||r==="gmt"?_e.utcInstance:_e.parseSpecifier(r)||$e.create(t)}else return Bn(t)?_e.instance(t):typeof t=="object"&&"offset"in t&&typeof t.offset=="function"?t:new mi(t)}var e0=()=>Date.now(),t0="system",n0=null,r0=null,o0=null,i0=60,s0,a0=null,de=class{static get now(){return e0}static set now(e){e0=e}static set defaultZone(e){t0=e}static get defaultZone(){return gn(t0,Pn.instance)}static get defaultLocale(){return n0}static set defaultLocale(e){n0=e}static get defaultNumberingSystem(){return r0}static set defaultNumberingSystem(e){r0=e}static get defaultOutputCalendar(){return o0}static set defaultOutputCalendar(e){o0=e}static get defaultWeekSettings(){return a0}static set defaultWeekSettings(e){a0=ta(e)}static get twoDigitCutoffYear(){return i0}static set twoDigitCutoffYear(e){i0=e%100}static get throwOnInvalid(){return s0}static set throwOnInvalid(e){s0=e}static resetCaches(){ue.resetCache(),$e.resetCache()}};var Qe=class{constructor(e,n){this.reason=e,this.explanation=n}toMessage(){return this.explanation?`${this.reason}: ${this.explanation}`:this.reason}};var l0=[0,31,59,90,120,151,181,212,243,273,304,334],u0=[0,31,60,91,121,152,182,213,244,274,305,335];function Gt(t,e){return new Qe("unit out of range",`you specified ${e} (of type ${typeof e}) as a ${t}, which is invalid`)}function yu(t,e,n){let r=new Date(Date.UTC(t,e-1,n));t<100&&t>=0&&r.setUTCFullYear(r.getUTCFullYear()-1900);let o=r.getUTCDay();return o===0?7:o}function c0(t,e,n){return n+(Eo(t)?u0:l0)[e-1]}function f0(t,e){let n=Eo(t)?u0:l0,r=n.findIndex(i=>i<e),o=e-n[r];return{month:r+1,day:o}}function gu(t,e){return(t-e+7)%7+1}function na(t,e=4,n=1){let{year:r,month:o,day:i}=t,s=c0(r,o,i),a=gu(yu(r,o,i),n),l=Math.floor((s-a+14-e)/7),u;return l<1?(u=r-1,l=vo(u,e,n)):l>vo(r,e,n)?(u=r+1,l=1):u=r,{weekYear:u,weekNumber:l,weekday:a,...oa(t)}}function Fd(t,e=4,n=1){let{weekYear:r,weekNumber:o,weekday:i}=t,s=gu(yu(r,1,e),n),a=br(r),l=o*7+i-s-7+e,u;l<1?(u=r-1,l+=br(u)):l>a?(u=r+1,l-=br(r)):u=r;let{month:d,day:f}=f0(u,l);return{year:u,month:d,day:f,...oa(t)}}function Du(t){let{year:e,month:n,day:r}=t,o=c0(e,n,r);return{year:e,ordinal:o,...oa(t)}}function _d(t){let{year:e,ordinal:n}=t,{month:r,day:o}=f0(e,n);return{year:e,month:r,day:o,...oa(t)}}function kd(t,e){if(!J(t.localWeekday)||!J(t.localWeekNumber)||!J(t.localWeekYear)){if(!J(t.weekday)||!J(t.weekNumber)||!J(t.weekYear))throw new pn("Cannot mix locale-based week fields with ISO-based week fields");return J(t.localWeekday)||(t.weekday=t.localWeekday),J(t.localWeekNumber)||(t.weekNumber=t.localWeekNumber),J(t.localWeekYear)||(t.weekYear=t.localWeekYear),delete t.localWeekday,delete t.localWeekNumber,delete t.localWeekYear,{minDaysInFirstWeek:e.getMinDaysInFirstWeek(),startOfWeek:e.getStartOfWeek()}}else return{minDaysInFirstWeek:4,startOfWeek:1}}function d0(t,e=4,n=1){let r=ra(t.weekYear),o=It(t.weekNumber,1,vo(t.weekYear,e,n)),i=It(t.weekday,1,7);return r?o?i?!1:Gt("weekday",t.weekday):Gt("week",t.weekNumber):Gt("weekYear",t.weekYear)}function m0(t){let e=ra(t.year),n=It(t.ordinal,1,br(t.year));return e?n?!1:Gt("ordinal",t.ordinal):Gt("year",t.year)}function Od(t){let e=ra(t.year),n=It(t.month,1,12),r=It(t.day,1,pi(t.year,t.month));return e?n?r?!1:Gt("day",t.day):Gt("month",t.month):Gt("year",t.year)}function bd(t){let{hour:e,minute:n,second:r,millisecond:o}=t,i=It(e,0,23)||e===24&&n===0&&r===0&&o===0,s=It(n,0,59),a=It(r,0,59),l=It(o,0,999);return i?s?a?l?!1:Gt("millisecond",o):Gt("second",r):Gt("minute",n):Gt("hour",e)}function J(t){return typeof t=="undefined"}function Bn(t){return typeof t=="number"}function ra(t){return typeof t=="number"&&t%1===0}function Xg(t){return typeof t=="string"}function h0(t){return Object.prototype.toString.call(t)==="[object Date]"}function pu(){try{return typeof Intl!="undefined"&&!!Intl.RelativeTimeFormat}catch(t){return!1}}function hu(){try{return typeof Intl!="undefined"&&!!Intl.Locale&&("weekInfo"in Intl.Locale.prototype||"getWeekInfo"in Intl.Locale.prototype)}catch(t){return!1}}function y0(t){return Array.isArray(t)?t:[t]}function Rd(t,e,n){if(t.length!==0)return t.reduce((r,o)=>{let i=[e(o),o];return r&&n(r[0],i[0])===r[0]?r:i},null)[1]}function g0(t,e){return e.reduce((n,r)=>(n[r]=t[r],n),{})}function Rr(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function ta(t){if(t==null)return null;if(typeof t!="object")throw new We("Week settings must be an object");if(!It(t.firstDay,1,7)||!It(t.minimalDays,1,7)||!Array.isArray(t.weekend)||t.weekend.some(e=>!It(e,1,7)))throw new We("Invalid week settings");return{firstDay:t.firstDay,minimalDays:t.minimalDays,weekend:Array.from(t.weekend)}}function It(t,e,n){return ra(t)&&t>=e&&t<=n}function FT(t,e){return t-e*Math.floor(t/e)}function Fe(t,e=2){let n=t<0,r;return n?r="-"+(""+-t).padStart(e,"0"):r=(""+t).padStart(e,"0"),r}function nr(t){if(!(J(t)||t===null||t===""))return parseInt(t,10)}function Nr(t){if(!(J(t)||t===null||t===""))return parseFloat(t)}function ia(t){if(!(J(t)||t===null||t==="")){let e=parseFloat("0."+t)*1e3;return Math.floor(e)}}function di(t,e,n=!1){let r=10**e;return(n?Math.trunc:Math.round)(t*r)/r}function Eo(t){return t%4===0&&(t%100!==0||t%400===0)}function br(t){return Eo(t)?366:365}function pi(t,e){let n=FT(e-1,12)+1,r=t+(e-n)/12;return n===2?Eo(r)?29:28:[31,null,31,30,31,30,31,31,30,31,30,31][n-1]}function fi(t){let e=Date.UTC(t.year,t.month-1,t.day,t.hour,t.minute,t.second,t.millisecond);return t.year<100&&t.year>=0&&(e=new Date(e),e.setUTCFullYear(t.year,t.month-1,t.day)),+e}function p0(t,e,n){return-gu(yu(t,1,e),n)+e-1}function vo(t,e=4,n=1){let r=p0(t,e,n),o=p0(t+1,e,n);return(br(t)-r+o)/7}function sa(t){return t>99?t:t>de.twoDigitCutoffYear?1900+t:2e3+t}function cu(t,e,n,r=null){let o=new Date(t),i={hourCycle:"h23",year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"};r&&(i.timeZone=r);let s={timeZoneName:e,...i},a=new Intl.DateTimeFormat(n,s).formatToParts(o).find(l=>l.type.toLowerCase()==="timezonename");return a?a.value:null}function Do(t,e){let n=parseInt(t,10);Number.isNaN(n)&&(n=0);let r=parseInt(e,10)||0,o=n<0||Object.is(n,-0)?-r:r;return n*60+o}function Nd(t){let e=Number(t);if(typeof t=="boolean"||t===""||Number.isNaN(e))throw new We(`Invalid unit value ${t}`);return e}function hi(t,e){let n={};for(let r in t)if(Rr(t,r)){let o=t[r];if(o==null)continue;n[e(r)]=Nd(o)}return n}function Or(t,e){let n=Math.trunc(Math.abs(t/60)),r=Math.trunc(Math.abs(t%60)),o=t>=0?"+":"-";switch(e){case"short":return`${o}${Fe(n,2)}:${Fe(r,2)}`;case"narrow":return`${o}${n}${r>0?`:${r}`:""}`;case"techie":return`${o}${Fe(n,2)}${Fe(r,2)}`;default:throw new RangeError(`Value format ${e} is out of range for property format`)}}function oa(t){return g0(t,["hour","minute","second","millisecond"])}var _T=["January","February","March","April","May","June","July","August","September","October","November","December"],Md=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],kT=["J","F","M","A","M","J","J","A","S","O","N","D"];function Ed(t){switch(t){case"narrow":return[...kT];case"short":return[...Md];case"long":return[..._T];case"numeric":return["1","2","3","4","5","6","7","8","9","10","11","12"];case"2-digit":return["01","02","03","04","05","06","07","08","09","10","11","12"];default:return null}}var Id=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],Ad=["Mon","Tue","Wed","Thu","Fri","Sat","Sun"],OT=["M","T","W","T","F","S","S"];function wd(t){switch(t){case"narrow":return[...OT];case"short":return[...Ad];case"long":return[...Id];case"numeric":return["1","2","3","4","5","6","7"];default:return null}}var Sd=["AM","PM"],bT=["Before Christ","Anno Domini"],RT=["BC","AD"],NT=["B","A"];function Cd(t){switch(t){case"narrow":return[...NT];case"short":return[...RT];case"long":return[...bT];default:return null}}function D0(t){return Sd[t.hour<12?0:1]}function v0(t,e){return wd(e)[t.weekday-1]}function E0(t,e){return Ed(e)[t.month-1]}function w0(t,e){return Cd(e)[t.year<0?0:1]}function Jg(t,e,n="always",r=!1){let o={years:["year","yr."],quarters:["quarter","qtr."],months:["month","mo."],weeks:["week","wk."],days:["day","day","days"],hours:["hour","hr."],minutes:["minute","min."],seconds:["second","sec."]},i=["hours","minutes","seconds"].indexOf(t)===-1;if(n==="auto"&&i){let f=t==="days";switch(e){case 1:return f?"tomorrow":`next ${o[t][0]}`;case-1:return f?"yesterday":`last ${o[t][0]}`;case 0:return f?"today":`this ${o[t][0]}`;default:}}let s=Object.is(e,-0)||e<0,a=Math.abs(e),l=a===1,u=o[t],d=r?l?u[1]:u[2]||u[1]:l?o[t][0]:t;return s?`${a} ${d} ago`:`in ${a} ${d}`}function S0(t,e){let n="";for(let r of t)r.literal?n+=r.val:n+=e(r.val);return n}var MT={D:kr,DD:As,DDD:Ls,DDDD:Ps,t:Bs,tt:Vs,ttt:Ws,tttt:Hs,T:zs,TT:Us,TTT:js,TTTT:$s,f:Gs,ff:Zs,fff:Ks,ffff:Js,F:Ys,FF:qs,FFF:Qs,FFFF:Xs},ke=class{static create(e,n={}){return new ke(e,n)}static parseFormat(e){let n=null,r="",o=!1,i=[];for(let s=0;s<e.length;s++){let a=e.charAt(s);a==="'"?(r.length>0&&i.push({literal:o||/^\s+$/.test(r),val:r}),n=null,r="",o=!o):o||a===n?r+=a:(r.length>0&&i.push({literal:/^\s+$/.test(r),val:r}),r=a,n=a)}return r.length>0&&i.push({literal:o||/^\s+$/.test(r),val:r}),i}static macroTokenToFormatOpts(e){return MT[e]}constructor(e,n){this.opts=n,this.loc=e,this.systemLoc=null}formatWithSystemDefault(e,n){return this.systemLoc===null&&(this.systemLoc=this.loc.redefaultToSystem()),this.systemLoc.dtFormatter(e,{...this.opts,...n}).format()}dtFormatter(e,n={}){return this.loc.dtFormatter(e,{...this.opts,...n})}formatDateTime(e,n){return this.dtFormatter(e,n).format()}formatDateTimeParts(e,n){return this.dtFormatter(e,n).formatToParts()}formatInterval(e,n){return this.dtFormatter(e.start,n).dtf.formatRange(e.start.toJSDate(),e.end.toJSDate())}resolvedOptions(e,n){return this.dtFormatter(e,n).resolvedOptions()}num(e,n=0){if(this.opts.forceSimple)return Fe(e,n);let r={...this.opts};return n>0&&(r.padTo=n),this.loc.numberFormatter(r).format(e)}formatDateTimeFromString(e,n){let r=this.loc.listingMode()==="en",o=this.loc.outputCalendar&&this.loc.outputCalendar!=="gregory",i=(c,y)=>this.loc.extract(e,c,y),s=c=>e.isOffsetFixed&&e.offset===0&&c.allowZ?"Z":e.isValid?e.zone.formatOffset(e.ts,c.format):"",a=()=>r?D0(e):i({hour:"numeric",hourCycle:"h12"},"dayperiod"),l=(c,y)=>r?E0(e,c):i(y?{month:c}:{month:c,day:"numeric"},"month"),u=(c,y)=>r?v0(e,c):i(y?{weekday:c}:{weekday:c,month:"long",day:"numeric"},"weekday"),d=c=>{let y=ke.macroTokenToFormatOpts(c);return y?this.formatWithSystemDefault(e,y):c},f=c=>r?w0(e,c):i({era:c},"era"),m=c=>{switch(c){case"S":return this.num(e.millisecond);case"u":case"SSS":return this.num(e.millisecond,3);case"s":return this.num(e.second);case"ss":return this.num(e.second,2);case"uu":return this.num(Math.floor(e.millisecond/10),2);case"uuu":return this.num(Math.floor(e.millisecond/100));case"m":return this.num(e.minute);case"mm":return this.num(e.minute,2);case"h":return this.num(e.hour%12===0?12:e.hour%12);case"hh":return this.num(e.hour%12===0?12:e.hour%12,2);case"H":return this.num(e.hour);case"HH":return this.num(e.hour,2);case"Z":return s({format:"narrow",allowZ:this.opts.allowZ});case"ZZ":return s({format:"short",allowZ:this.opts.allowZ});case"ZZZ":return s({format:"techie",allowZ:this.opts.allowZ});case"ZZZZ":return e.zone.offsetName(e.ts,{format:"short",locale:this.loc.locale});case"ZZZZZ":return e.zone.offsetName(e.ts,{format:"long",locale:this.loc.locale});case"z":return e.zoneName;case"a":return a();case"d":return o?i({day:"numeric"},"day"):this.num(e.day);case"dd":return o?i({day:"2-digit"},"day"):this.num(e.day,2);case"c":return this.num(e.weekday);case"ccc":return u("short",!0);case"cccc":return u("long",!0);case"ccccc":return u("narrow",!0);case"E":return this.num(e.weekday);case"EEE":return u("short",!1);case"EEEE":return u("long",!1);case"EEEEE":return u("narrow",!1);case"L":return o?i({month:"numeric",day:"numeric"},"month"):this.num(e.month);case"LL":return o?i({month:"2-digit",day:"numeric"},"month"):this.num(e.month,2);case"LLL":return l("short",!0);case"LLLL":return l("long",!0);case"LLLLL":return l("narrow",!0);case"M":return o?i({month:"numeric"},"month"):this.num(e.month);case"MM":return o?i({month:"2-digit"},"month"):this.num(e.month,2);case"MMM":return l("short",!1);case"MMMM":return l("long",!1);case"MMMMM":return l("narrow",!1);case"y":return o?i({year:"numeric"},"year"):this.num(e.year);case"yy":return o?i({year:"2-digit"},"year"):this.num(e.year.toString().slice(-2),2);case"yyyy":return o?i({year:"numeric"},"year"):this.num(e.year,4);case"yyyyyy":return o?i({year:"numeric"},"year"):this.num(e.year,6);case"G":return f("short");case"GG":return f("long");case"GGGGG":return f("narrow");case"kk":return this.num(e.weekYear.toString().slice(-2),2);case"kkkk":return this.num(e.weekYear,4);case"W":return this.num(e.weekNumber);case"WW":return this.num(e.weekNumber,2);case"n":return this.num(e.localWeekNumber);case"nn":return this.num(e.localWeekNumber,2);case"ii":return this.num(e.localWeekYear.toString().slice(-2),2);case"iiii":return this.num(e.localWeekYear,4);case"o":return this.num(e.ordinal);case"ooo":return this.num(e.ordinal,3);case"q":return this.num(e.quarter);case"qq":return this.num(e.quarter,2);case"X":return this.num(Math.floor(e.ts/1e3));case"x":return this.num(e.ts);default:return d(c)}};return S0(ke.parseFormat(n),m)}formatDurationFromString(e,n){let r=l=>{switch(l[0]){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":return"hour";case"d":return"day";case"w":return"week";case"M":return"month";case"y":return"year";default:return null}},o=l=>u=>{let d=r(u);return d?this.num(l.get(d),u.length):u},i=ke.parseFormat(n),s=i.reduce((l,{literal:u,val:d})=>u?l:l.concat(d),[]),a=e.shiftTo(...s.map(r).filter(l=>l));return S0(i,o(a))}};var x0=/[A-Za-z_+-]{1,256}(?::?\/[A-Za-z0-9_+-]{1,256}(?:\/[A-Za-z0-9_+-]{1,256})?)?/;function gi(...t){let e=t.reduce((n,r)=>n+r.source,"");return RegExp(`^${e}$`)}function Di(...t){return e=>t.reduce(([n,r,o],i)=>{let[s,a,l]=i(e,o);return[{...n,...s},a||r,l]},[{},null,1]).slice(0,2)}function vi(t,...e){if(t==null)return[null,null];for(let[n,r]of e){let o=n.exec(t);if(o)return r(o)}return[null,null]}function T0(...t){return(e,n)=>{let r={},o;for(o=0;o<t.length;o++)r[t[o]]=nr(e[n+o]);return[r,null,n+o]}}var F0=/(?:(Z)|([+-]\d\d)(?::?(\d\d))?)/,IT=`(?:${F0.source}?(?:\\[(${x0.source})\\])?)?`,Ld=/(\d\d)(?::?(\d\d)(?::?(\d\d)(?:[.,](\d{1,30}))?)?)?/,_0=RegExp(`${Ld.source}${IT}`),Pd=RegExp(`(?:T${_0.source})?`),AT=/([+-]\d{6}|\d{4})(?:-?(\d\d)(?:-?(\d\d))?)?/,LT=/(\d{4})-?W(\d\d)(?:-?(\d))?/,PT=/(\d{4})-?(\d{3})/,BT=T0("weekYear","weekNumber","weekDay"),VT=T0("year","ordinal"),WT=/(\d{4})-(\d\d)-(\d\d)/,k0=RegExp(`${Ld.source} ?(?:${F0.source}|(${x0.source}))?`),HT=RegExp(`(?: ${k0.source})?`);function yi(t,e,n){let r=t[e];return J(r)?n:nr(r)}function zT(t,e){return[{year:yi(t,e),month:yi(t,e+1,1),day:yi(t,e+2,1)},null,e+3]}function Ei(t,e){return[{hours:yi(t,e,0),minutes:yi(t,e+1,0),seconds:yi(t,e+2,0),milliseconds:ia(t[e+3])},null,e+4]}function aa(t,e){let n=!t[e]&&!t[e+1],r=Do(t[e+1],t[e+2]),o=n?null:_e.instance(r);return[{},o,e+3]}function la(t,e){let n=t[e]?$e.create(t[e]):null;return[{},n,e+1]}var UT=RegExp(`^T?${Ld.source}$`),jT=/^-?P(?:(?:(-?\d{1,20}(?:\.\d{1,20})?)Y)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20}(?:\.\d{1,20})?)W)?(?:(-?\d{1,20}(?:\.\d{1,20})?)D)?(?:T(?:(-?\d{1,20}(?:\.\d{1,20})?)H)?(?:(-?\d{1,20}(?:\.\d{1,20})?)M)?(?:(-?\d{1,20})(?:[.,](-?\d{1,20}))?S)?)?)$/;function $T(t){let[e,n,r,o,i,s,a,l,u]=t,d=e[0]==="-",f=l&&l[0]==="-",m=(c,y=!1)=>c!==void 0&&(y||c&&d)?-c:c;return[{years:m(Nr(n)),months:m(Nr(r)),weeks:m(Nr(o)),days:m(Nr(i)),hours:m(Nr(s)),minutes:m(Nr(a)),seconds:m(Nr(l),l==="-0"),milliseconds:m(ia(u),f)}]}var GT={GMT:0,EDT:-4*60,EST:-5*60,CDT:-5*60,CST:-6*60,MDT:-6*60,MST:-7*60,PDT:-7*60,PST:-8*60};function Bd(t,e,n,r,o,i,s){let a={year:e.length===2?sa(nr(e)):nr(e),month:Md.indexOf(n)+1,day:nr(r),hour:nr(o),minute:nr(i)};return s&&(a.second=nr(s)),t&&(a.weekday=t.length>3?Id.indexOf(t)+1:Ad.indexOf(t)+1),a}var YT=/^(?:(Mon|Tue|Wed|Thu|Fri|Sat|Sun),\s)?(\d{1,2})\s(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\s(\d{2,4})\s(\d\d):(\d\d)(?::(\d\d))?\s(?:(UT|GMT|[ECMP][SD]T)|([Zz])|(?:([+-]\d\d)(\d\d)))$/;function ZT(t){let[,e,n,r,o,i,s,a,l,u,d,f]=t,m=Bd(e,o,r,n,i,s,a),c;return l?c=GT[l]:u?c=0:c=Do(d,f),[m,new _e(c)]}function qT(t){return t.replace(/\([^()]*\)|[\n\t]/g," ").replace(/(\s\s+)/g," ").trim()}var KT=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun), (\d\d) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) (\d{4}) (\d\d):(\d\d):(\d\d) GMT$/,QT=/^(Monday|Tuesday|Wednesday|Thursday|Friday|Saturday|Sunday), (\d\d)-(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)-(\d\d) (\d\d):(\d\d):(\d\d) GMT$/,JT=/^(Mon|Tue|Wed|Thu|Fri|Sat|Sun) (Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec) ( \d|\d\d) (\d\d):(\d\d):(\d\d) (\d{4})$/;function C0(t){let[,e,n,r,o,i,s,a]=t;return[Bd(e,o,r,n,i,s,a),_e.utcInstance]}function XT(t){let[,e,n,r,o,i,s,a]=t;return[Bd(e,a,n,r,o,i,s),_e.utcInstance]}var eF=gi(AT,Pd),tF=gi(LT,Pd),nF=gi(PT,Pd),rF=gi(_0),O0=Di(zT,Ei,aa,la),oF=Di(BT,Ei,aa,la),iF=Di(VT,Ei,aa,la),sF=Di(Ei,aa,la);function b0(t){return vi(t,[eF,O0],[tF,oF],[nF,iF],[rF,sF])}function R0(t){return vi(qT(t),[YT,ZT])}function N0(t){return vi(t,[KT,C0],[QT,C0],[JT,XT])}function M0(t){return vi(t,[jT,$T])}var aF=Di(Ei);function I0(t){return vi(t,[UT,aF])}var lF=gi(WT,HT),uF=gi(k0),cF=Di(Ei,aa,la);function A0(t){return vi(t,[lF,O0],[uF,cF])}var L0="Invalid Duration",B0={weeks:{days:7,hours:7*24,minutes:7*24*60,seconds:7*24*60*60,milliseconds:7*24*60*60*1e3},days:{hours:24,minutes:24*60,seconds:24*60*60,milliseconds:24*60*60*1e3},hours:{minutes:60,seconds:60*60,milliseconds:60*60*1e3},minutes:{seconds:60,milliseconds:60*1e3},seconds:{milliseconds:1e3}},fF={years:{quarters:4,months:12,weeks:52,days:365,hours:365*24,minutes:365*24*60,seconds:365*24*60*60,milliseconds:365*24*60*60*1e3},quarters:{months:3,weeks:13,days:91,hours:91*24,minutes:91*24*60,seconds:91*24*60*60,milliseconds:91*24*60*60*1e3},months:{weeks:4,days:30,hours:30*24,minutes:30*24*60,seconds:30*24*60*60,milliseconds:30*24*60*60*1e3},...B0},Yt=146097/400,wi=146097/4800,dF={years:{quarters:4,months:12,weeks:Yt/7,days:Yt,hours:Yt*24,minutes:Yt*24*60,seconds:Yt*24*60*60,milliseconds:Yt*24*60*60*1e3},quarters:{months:3,weeks:Yt/28,days:Yt/4,hours:Yt*24/4,minutes:Yt*24*60/4,seconds:Yt*24*60*60/4,milliseconds:Yt*24*60*60*1e3/4},months:{weeks:wi/7,days:wi,hours:wi*24,minutes:wi*24*60,seconds:wi*24*60*60,milliseconds:wi*24*60*60*1e3},...B0},wo=["years","quarters","months","weeks","days","hours","minutes","seconds","milliseconds"],mF=wo.slice(0).reverse();function Mr(t,e,n=!1){let r={values:n?e.values:{...t.values,...e.values||{}},loc:t.loc.clone(e.loc),conversionAccuracy:e.conversionAccuracy||t.conversionAccuracy,matrix:e.matrix||t.matrix};return new ie(r)}function V0(t,e){var r;let n=(r=e.milliseconds)!=null?r:0;for(let o of mF.slice(1))e[o]&&(n+=e[o]*t[o].milliseconds);return n}function P0(t,e){let n=V0(t,e)<0?-1:1;wo.reduceRight((r,o)=>{if(J(e[o]))return r;if(r){let i=e[r]*n,s=t[o][r],a=Math.floor(i/s);e[o]+=a*n,e[r]-=a*s*n}return o},null),wo.reduce((r,o)=>{if(J(e[o]))return r;if(r){let i=e[r]%1;e[r]-=i,e[o]+=i*t[r][o]}return o},null)}function pF(t){let e={};for(let[n,r]of Object.entries(t))r!==0&&(e[n]=r);return e}var ie=class{constructor(e){let n=e.conversionAccuracy==="longterm"||!1,r=n?dF:fF;e.matrix&&(r=e.matrix),this.values=e.values,this.loc=e.loc||ue.create(),this.conversionAccuracy=n?"longterm":"casual",this.invalid=e.invalid||null,this.matrix=r,this.isLuxonDuration=!0}static fromMillis(e,n){return ie.fromObject({milliseconds:e},n)}static fromObject(e,n={}){if(e==null||typeof e!="object")throw new We(`Duration.fromObject: argument expected to be an object, got ${e===null?"null":typeof e}`);return new ie({values:hi(e,ie.normalizeUnit),loc:ue.fromObject(n),conversionAccuracy:n.conversionAccuracy,matrix:n.matrix})}static fromDurationLike(e){if(Bn(e))return ie.fromMillis(e);if(ie.isDuration(e))return e;if(typeof e=="object")return ie.fromObject(e);throw new We(`Unknown duration argument ${e} of type ${typeof e}`)}static fromISO(e,n){let[r]=M0(e);return r?ie.fromObject(r,n):ie.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static fromISOTime(e,n){let[r]=I0(e);return r?ie.fromObject(r,n):ie.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static invalid(e,n=null){if(!e)throw new We("need to specify a reason the Duration is invalid");let r=e instanceof Qe?e:new Qe(e,n);if(de.throwOnInvalid)throw new lu(r);return new ie({invalid:r})}static normalizeUnit(e){let n={year:"years",years:"years",quarter:"quarters",quarters:"quarters",month:"months",months:"months",week:"weeks",weeks:"weeks",day:"days",days:"days",hour:"hours",hours:"hours",minute:"minutes",minutes:"minutes",second:"seconds",seconds:"seconds",millisecond:"milliseconds",milliseconds:"milliseconds"}[e&&e.toLowerCase()];if(!n)throw new ci(e);return n}static isDuration(e){return e&&e.isLuxonDuration||!1}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}toFormat(e,n={}){let r={...n,floor:n.round!==!1&&n.floor!==!1};return this.isValid?ke.create(this.loc,r).formatDurationFromString(this,e):L0}toHuman(e={}){if(!this.isValid)return L0;let n=wo.map(r=>{let o=this.values[r];return J(o)?null:this.loc.numberFormatter({style:"unit",unitDisplay:"long",...e,unit:r.slice(0,-1)}).format(o)}).filter(r=>r);return this.loc.listFormatter({type:"conjunction",style:e.listStyle||"narrow",...e}).format(n)}toObject(){return this.isValid?{...this.values}:{}}toISO(){if(!this.isValid)return null;let e="P";return this.years!==0&&(e+=this.years+"Y"),(this.months!==0||this.quarters!==0)&&(e+=this.months+this.quarters*3+"M"),this.weeks!==0&&(e+=this.weeks+"W"),this.days!==0&&(e+=this.days+"D"),(this.hours!==0||this.minutes!==0||this.seconds!==0||this.milliseconds!==0)&&(e+="T"),this.hours!==0&&(e+=this.hours+"H"),this.minutes!==0&&(e+=this.minutes+"M"),(this.seconds!==0||this.milliseconds!==0)&&(e+=di(this.seconds+this.milliseconds/1e3,3)+"S"),e==="P"&&(e+="T0S"),e}toISOTime(e={}){if(!this.isValid)return null;let n=this.toMillis();return n<0||n>=864e5?null:(e={suppressMilliseconds:!1,suppressSeconds:!1,includePrefix:!1,format:"extended",...e,includeOffset:!1},K.fromMillis(n,{zone:"UTC"}).toISOTime(e))}toJSON(){return this.toISO()}toString(){return this.toISO()}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Duration { values: ${JSON.stringify(this.values)} }`:`Duration { Invalid, reason: ${this.invalidReason} }`}toMillis(){return this.isValid?V0(this.matrix,this.values):NaN}valueOf(){return this.toMillis()}plus(e){if(!this.isValid)return this;let n=ie.fromDurationLike(e),r={};for(let o of wo)(Rr(n.values,o)||Rr(this.values,o))&&(r[o]=n.get(o)+this.get(o));return Mr(this,{values:r},!0)}minus(e){if(!this.isValid)return this;let n=ie.fromDurationLike(e);return this.plus(n.negate())}mapUnits(e){if(!this.isValid)return this;let n={};for(let r of Object.keys(this.values))n[r]=Nd(e(this.values[r],r));return Mr(this,{values:n},!0)}get(e){return this[ie.normalizeUnit(e)]}set(e){if(!this.isValid)return this;let n={...this.values,...hi(e,ie.normalizeUnit)};return Mr(this,{values:n})}reconfigure({locale:e,numberingSystem:n,conversionAccuracy:r,matrix:o}={}){let s={loc:this.loc.clone({locale:e,numberingSystem:n}),matrix:o,conversionAccuracy:r};return Mr(this,s)}as(e){return this.isValid?this.shiftTo(e).get(e):NaN}normalize(){if(!this.isValid)return this;let e=this.toObject();return P0(this.matrix,e),Mr(this,{values:e},!0)}rescale(){if(!this.isValid)return this;let e=pF(this.normalize().shiftToAll().toObject());return Mr(this,{values:e},!0)}shiftTo(...e){if(!this.isValid)return this;if(e.length===0)return this;e=e.map(s=>ie.normalizeUnit(s));let n={},r={},o=this.toObject(),i;for(let s of wo)if(e.indexOf(s)>=0){i=s;let a=0;for(let u in r)a+=this.matrix[u][s]*r[u],r[u]=0;Bn(o[s])&&(a+=o[s]);let l=Math.trunc(a);n[s]=l,r[s]=(a*1e3-l*1e3)/1e3}else Bn(o[s])&&(r[s]=o[s]);for(let s in r)r[s]!==0&&(n[i]+=s===i?r[s]:r[s]/this.matrix[i][s]);return P0(this.matrix,n),Mr(this,{values:n},!0)}shiftToAll(){return this.isValid?this.shiftTo("years","months","weeks","days","hours","minutes","seconds","milliseconds"):this}negate(){if(!this.isValid)return this;let e={};for(let n of Object.keys(this.values))e[n]=this.values[n]===0?0:-this.values[n];return Mr(this,{values:e},!0)}get years(){return this.isValid?this.values.years||0:NaN}get quarters(){return this.isValid?this.values.quarters||0:NaN}get months(){return this.isValid?this.values.months||0:NaN}get weeks(){return this.isValid?this.values.weeks||0:NaN}get days(){return this.isValid?this.values.days||0:NaN}get hours(){return this.isValid?this.values.hours||0:NaN}get minutes(){return this.isValid?this.values.minutes||0:NaN}get seconds(){return this.isValid?this.values.seconds||0:NaN}get milliseconds(){return this.isValid?this.values.milliseconds||0:NaN}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}equals(e){if(!this.isValid||!e.isValid||!this.loc.equals(e.loc))return!1;function n(r,o){return r===void 0||r===0?o===void 0||o===0:r===o}for(let r of wo)if(!n(this.values[r],e.values[r]))return!1;return!0}};var Si="Invalid Interval";function hF(t,e){return!t||!t.isValid?ye.invalid("missing or invalid start"):!e||!e.isValid?ye.invalid("missing or invalid end"):e<t?ye.invalid("end before start",`The end of an interval must be after its start, but you had start=${t.toISO()} and end=${e.toISO()}`):null}var ye=class{constructor(e){this.s=e.start,this.e=e.end,this.invalid=e.invalid||null,this.isLuxonInterval=!0}static invalid(e,n=null){if(!e)throw new We("need to specify a reason the Interval is invalid");let r=e instanceof Qe?e:new Qe(e,n);if(de.throwOnInvalid)throw new au(r);return new ye({invalid:r})}static fromDateTimes(e,n){let r=Ci(e),o=Ci(n),i=hF(r,o);return i==null?new ye({start:r,end:o}):i}static after(e,n){let r=ie.fromDurationLike(n),o=Ci(e);return ye.fromDateTimes(o,o.plus(r))}static before(e,n){let r=ie.fromDurationLike(n),o=Ci(e);return ye.fromDateTimes(o.minus(r),o)}static fromISO(e,n){let[r,o]=(e||"").split("/",2);if(r&&o){let i,s;try{i=K.fromISO(r,n),s=i.isValid}catch(u){s=!1}let a,l;try{a=K.fromISO(o,n),l=a.isValid}catch(u){l=!1}if(s&&l)return ye.fromDateTimes(i,a);if(s){let u=ie.fromISO(o,n);if(u.isValid)return ye.after(i,u)}else if(l){let u=ie.fromISO(r,n);if(u.isValid)return ye.before(a,u)}}return ye.invalid("unparsable",`the input "${e}" can't be parsed as ISO 8601`)}static isInterval(e){return e&&e.isLuxonInterval||!1}get start(){return this.isValid?this.s:null}get end(){return this.isValid?this.e:null}get isValid(){return this.invalidReason===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}length(e="milliseconds"){return this.isValid?this.toDuration(e).get(e):NaN}count(e="milliseconds",n){if(!this.isValid)return NaN;let r=this.start.startOf(e,n),o;return n!=null&&n.useLocaleWeeks?o=this.end.reconfigure({locale:r.locale}):o=this.end,o=o.startOf(e,n),Math.floor(o.diff(r,e).get(e))+(o.valueOf()!==this.end.valueOf())}hasSame(e){return this.isValid?this.isEmpty()||this.e.minus(1).hasSame(this.s,e):!1}isEmpty(){return this.s.valueOf()===this.e.valueOf()}isAfter(e){return this.isValid?this.s>e:!1}isBefore(e){return this.isValid?this.e<=e:!1}contains(e){return this.isValid?this.s<=e&&this.e>e:!1}set({start:e,end:n}={}){return this.isValid?ye.fromDateTimes(e||this.s,n||this.e):this}splitAt(...e){if(!this.isValid)return[];let n=e.map(Ci).filter(s=>this.contains(s)).sort((s,a)=>s.toMillis()-a.toMillis()),r=[],{s:o}=this,i=0;for(;o<this.e;){let s=n[i]||this.e,a=+s>+this.e?this.e:s;r.push(ye.fromDateTimes(o,a)),o=a,i+=1}return r}splitBy(e){let n=ie.fromDurationLike(e);if(!this.isValid||!n.isValid||n.as("milliseconds")===0)return[];let{s:r}=this,o=1,i,s=[];for(;r<this.e;){let a=this.start.plus(n.mapUnits(l=>l*o));i=+a>+this.e?this.e:a,s.push(ye.fromDateTimes(r,i)),r=i,o+=1}return s}divideEqually(e){return this.isValid?this.splitBy(this.length()/e).slice(0,e):[]}overlaps(e){return this.e>e.s&&this.s<e.e}abutsStart(e){return this.isValid?+this.e==+e.s:!1}abutsEnd(e){return this.isValid?+e.e==+this.s:!1}engulfs(e){return this.isValid?this.s<=e.s&&this.e>=e.e:!1}equals(e){return!this.isValid||!e.isValid?!1:this.s.equals(e.s)&&this.e.equals(e.e)}intersection(e){if(!this.isValid)return this;let n=this.s>e.s?this.s:e.s,r=this.e<e.e?this.e:e.e;return n>=r?null:ye.fromDateTimes(n,r)}union(e){if(!this.isValid)return this;let n=this.s<e.s?this.s:e.s,r=this.e>e.e?this.e:e.e;return ye.fromDateTimes(n,r)}static merge(e){let[n,r]=e.sort((o,i)=>o.s-i.s).reduce(([o,i],s)=>i?i.overlaps(s)||i.abutsStart(s)?[o,i.union(s)]:[o.concat([i]),s]:[o,s],[[],null]);return r&&n.push(r),n}static xor(e){let n=null,r=0,o=[],i=e.map(l=>[{time:l.s,type:"s"},{time:l.e,type:"e"}]),s=Array.prototype.concat(...i),a=s.sort((l,u)=>l.time-u.time);for(let l of a)r+=l.type==="s"?1:-1,r===1?n=l.time:(n&&+n!=+l.time&&o.push(ye.fromDateTimes(n,l.time)),n=null);return ye.merge(o)}difference(...e){return ye.xor([this].concat(e)).map(n=>this.intersection(n)).filter(n=>n&&!n.isEmpty())}toString(){return this.isValid?`[${this.s.toISO()} \u2013 ${this.e.toISO()})`:Si}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`Interval { start: ${this.s.toISO()}, end: ${this.e.toISO()} }`:`Interval { Invalid, reason: ${this.invalidReason} }`}toLocaleString(e=kr,n={}){return this.isValid?ke.create(this.s.loc.clone(n),e).formatInterval(this):Si}toISO(e){return this.isValid?`${this.s.toISO(e)}/${this.e.toISO(e)}`:Si}toISODate(){return this.isValid?`${this.s.toISODate()}/${this.e.toISODate()}`:Si}toISOTime(e){return this.isValid?`${this.s.toISOTime(e)}/${this.e.toISOTime(e)}`:Si}toFormat(e,{separator:n=" \u2013 "}={}){return this.isValid?`${this.s.toFormat(e)}${n}${this.e.toFormat(e)}`:Si}toDuration(e,n){return this.isValid?this.e.diff(this.s,e,n):ie.invalid(this.invalidReason)}mapEndpoints(e){return ye.fromDateTimes(e(this.s),e(this.e))}};var rr=class{static hasDST(e=de.defaultZone){let n=K.now().setZone(e).set({month:12});return!e.isUniversal&&n.offset!==n.set({month:6}).offset}static isValidIANAZone(e){return $e.isValidZone(e)}static normalizeZone(e){return gn(e,de.defaultZone)}static getStartOfWeek({locale:e=null,locObj:n=null}={}){return(n||ue.create(e)).getStartOfWeek()}static getMinimumDaysInFirstWeek({locale:e=null,locObj:n=null}={}){return(n||ue.create(e)).getMinDaysInFirstWeek()}static getWeekendWeekdays({locale:e=null,locObj:n=null}={}){return(n||ue.create(e)).getWeekendDays().slice()}static months(e="long",{locale:n=null,numberingSystem:r=null,locObj:o=null,outputCalendar:i="gregory"}={}){return(o||ue.create(n,r,i)).months(e)}static monthsFormat(e="long",{locale:n=null,numberingSystem:r=null,locObj:o=null,outputCalendar:i="gregory"}={}){return(o||ue.create(n,r,i)).months(e,!0)}static weekdays(e="long",{locale:n=null,numberingSystem:r=null,locObj:o=null}={}){return(o||ue.create(n,r,null)).weekdays(e)}static weekdaysFormat(e="long",{locale:n=null,numberingSystem:r=null,locObj:o=null}={}){return(o||ue.create(n,r,null)).weekdays(e,!0)}static meridiems({locale:e=null}={}){return ue.create(e).meridiems()}static eras(e="short",{locale:n=null}={}){return ue.create(n,null,"gregory").eras(e)}static features(){return{relative:pu(),localeWeek:hu()}}};function W0(t,e){let n=o=>o.toUTC(0,{keepLocalTime:!0}).startOf("day").valueOf(),r=n(e)-n(t);return Math.floor(ie.fromMillis(r).as("days"))}function yF(t,e,n){let r=[["years",(l,u)=>u.year-l.year],["quarters",(l,u)=>u.quarter-l.quarter+(u.year-l.year)*4],["months",(l,u)=>u.month-l.month+(u.year-l.year)*12],["weeks",(l,u)=>{let d=W0(l,u);return(d-d%7)/7}],["days",W0]],o={},i=t,s,a;for(let[l,u]of r)n.indexOf(l)>=0&&(s=l,o[l]=u(t,e),a=i.plus(o),a>e?(o[l]--,t=i.plus(o),t>e&&(a=t,o[l]--,t=i.plus(o))):t=a);return[t,o,a,s]}function H0(t,e,n,r){let[o,i,s,a]=yF(t,e,n),l=e-o,u=n.filter(f=>["hours","minutes","seconds","milliseconds"].indexOf(f)>=0);u.length===0&&(s<e&&(s=o.plus({[a]:1})),s!==o&&(i[a]=(i[a]||0)+l/(s-o)));let d=ie.fromObject(i,r);return u.length>0?ie.fromMillis(l,r).shiftTo(...u).plus(d):d}var Vd={arab:"[\u0660-\u0669]",arabext:"[\u06F0-\u06F9]",bali:"[\u1B50-\u1B59]",beng:"[\u09E6-\u09EF]",deva:"[\u0966-\u096F]",fullwide:"[\uFF10-\uFF19]",gujr:"[\u0AE6-\u0AEF]",hanidec:"[\u3007|\u4E00|\u4E8C|\u4E09|\u56DB|\u4E94|\u516D|\u4E03|\u516B|\u4E5D]",khmr:"[\u17E0-\u17E9]",knda:"[\u0CE6-\u0CEF]",laoo:"[\u0ED0-\u0ED9]",limb:"[\u1946-\u194F]",mlym:"[\u0D66-\u0D6F]",mong:"[\u1810-\u1819]",mymr:"[\u1040-\u1049]",orya:"[\u0B66-\u0B6F]",tamldec:"[\u0BE6-\u0BEF]",telu:"[\u0C66-\u0C6F]",thai:"[\u0E50-\u0E59]",tibt:"[\u0F20-\u0F29]",latn:"\\d"},z0={arab:[1632,1641],arabext:[1776,1785],bali:[6992,7001],beng:[2534,2543],deva:[2406,2415],fullwide:[65296,65303],gujr:[2790,2799],khmr:[6112,6121],knda:[3302,3311],laoo:[3792,3801],limb:[6470,6479],mlym:[3430,3439],mong:[6160,6169],mymr:[4160,4169],orya:[2918,2927],tamldec:[3046,3055],telu:[3174,3183],thai:[3664,3673],tibt:[3872,3881]},gF=Vd.hanidec.replace(/[\[|\]]/g,"").split("");function U0(t){let e=parseInt(t,10);if(isNaN(e)){e="";for(let n=0;n<t.length;n++){let r=t.charCodeAt(n);if(t[n].search(Vd.hanidec)!==-1)e+=gF.indexOf(t[n]);else for(let o in z0){let[i,s]=z0[o];r>=i&&r<=s&&(e+=r-i)}}return parseInt(e,10)}else return e}function Zt({numberingSystem:t},e=""){return new RegExp(`${Vd[t||"latn"]}${e}`)}var DF="missing Intl.DateTimeFormat.formatToParts support";function me(t,e=n=>n){return{regex:t,deser:([n])=>e(U0(n))}}var vF=String.fromCharCode(160),G0=`[ ${vF}]`,Y0=new RegExp(G0,"g");function EF(t){return t.replace(/\./g,"\\.?").replace(Y0,G0)}function j0(t){return t.replace(/\./g,"").replace(Y0," ").toLowerCase()}function Dn(t,e){return t===null?null:{regex:RegExp(t.map(EF).join("|")),deser:([n])=>t.findIndex(r=>j0(n)===j0(r))+e}}function $0(t,e){return{regex:t,deser:([,n,r])=>Do(n,r),groups:e}}function vu(t){return{regex:t,deser:([e])=>e}}function wF(t){return t.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function SF(t,e){let n=Zt(e),r=Zt(e,"{2}"),o=Zt(e,"{3}"),i=Zt(e,"{4}"),s=Zt(e,"{6}"),a=Zt(e,"{1,2}"),l=Zt(e,"{1,3}"),u=Zt(e,"{1,6}"),d=Zt(e,"{1,9}"),f=Zt(e,"{2,4}"),m=Zt(e,"{4,6}"),c=S=>({regex:RegExp(wF(S.val)),deser:([h])=>h,literal:!0}),v=(S=>{if(t.literal)return c(S);switch(S.val){case"G":return Dn(e.eras("short"),0);case"GG":return Dn(e.eras("long"),0);case"y":return me(u);case"yy":return me(f,sa);case"yyyy":return me(i);case"yyyyy":return me(m);case"yyyyyy":return me(s);case"M":return me(a);case"MM":return me(r);case"MMM":return Dn(e.months("short",!0),1);case"MMMM":return Dn(e.months("long",!0),1);case"L":return me(a);case"LL":return me(r);case"LLL":return Dn(e.months("short",!1),1);case"LLLL":return Dn(e.months("long",!1),1);case"d":return me(a);case"dd":return me(r);case"o":return me(l);case"ooo":return me(o);case"HH":return me(r);case"H":return me(a);case"hh":return me(r);case"h":return me(a);case"mm":return me(r);case"m":return me(a);case"q":return me(a);case"qq":return me(r);case"s":return me(a);case"ss":return me(r);case"S":return me(l);case"SSS":return me(o);case"u":return vu(d);case"uu":return vu(a);case"uuu":return me(n);case"a":return Dn(e.meridiems(),0);case"kkkk":return me(i);case"kk":return me(f,sa);case"W":return me(a);case"WW":return me(r);case"E":case"c":return me(n);case"EEE":return Dn(e.weekdays("short",!1),1);case"EEEE":return Dn(e.weekdays("long",!1),1);case"ccc":return Dn(e.weekdays("short",!0),1);case"cccc":return Dn(e.weekdays("long",!0),1);case"Z":case"ZZ":return $0(new RegExp(`([+-]${a.source})(?::(${r.source}))?`),2);case"ZZZ":return $0(new RegExp(`([+-]${a.source})(${r.source})?`),2);case"z":return vu(/[a-z_+-/]{1,256}?/i);case" ":return vu(/[^\S\n\r]/);default:return c(S)}})(t)||{invalidReason:DF};return v.token=t,v}var CF={year:{"2-digit":"yy",numeric:"yyyyy"},month:{numeric:"M","2-digit":"MM",short:"MMM",long:"MMMM"},day:{numeric:"d","2-digit":"dd"},weekday:{short:"EEE",long:"EEEE"},dayperiod:"a",dayPeriod:"a",hour12:{numeric:"h","2-digit":"hh"},hour24:{numeric:"H","2-digit":"HH"},minute:{numeric:"m","2-digit":"mm"},second:{numeric:"s","2-digit":"ss"},timeZoneName:{long:"ZZZZZ",short:"ZZZ"}};function xF(t,e,n){let{type:r,value:o}=t;if(r==="literal"){let l=/^\s+$/.test(o);return{literal:!l,val:l?" ":o}}let i=e[r],s=r;r==="hour"&&(e.hour12!=null?s=e.hour12?"hour12":"hour24":e.hourCycle!=null?e.hourCycle==="h11"||e.hourCycle==="h12"?s="hour12":s="hour24":s=n.hour12?"hour12":"hour24");let a=CF[s];if(typeof a=="object"&&(a=a[i]),a)return{literal:!1,val:a}}function TF(t){return[`^${t.map(n=>n.regex).reduce((n,r)=>`${n}(${r.source})`,"")}$`,t]}function FF(t,e,n){let r=t.match(e);if(r){let o={},i=1;for(let s in n)if(Rr(n,s)){let a=n[s],l=a.groups?a.groups+1:1;!a.literal&&a.token&&(o[a.token.val[0]]=a.deser(r.slice(i,i+l))),i+=l}return[r,o]}else return[r,{}]}function _F(t){let e=i=>{switch(i){case"S":return"millisecond";case"s":return"second";case"m":return"minute";case"h":case"H":return"hour";case"d":return"day";case"o":return"ordinal";case"L":case"M":return"month";case"y":return"year";case"E":case"c":return"weekday";case"W":return"weekNumber";case"k":return"weekYear";case"q":return"quarter";default:return null}},n=null,r;return J(t.z)||(n=$e.create(t.z)),J(t.Z)||(n||(n=new _e(t.Z)),r=t.Z),J(t.q)||(t.M=(t.q-1)*3+1),J(t.h)||(t.h<12&&t.a===1?t.h+=12:t.h===12&&t.a===0&&(t.h=0)),t.G===0&&t.y&&(t.y=-t.y),J(t.u)||(t.S=ia(t.u)),[Object.keys(t).reduce((i,s)=>{let a=e(s);return a&&(i[a]=t[s]),i},{}),n,r]}var Wd=null;function kF(){return Wd||(Wd=K.fromMillis(1555555555555)),Wd}function OF(t,e){if(t.literal)return t;let n=ke.macroTokenToFormatOpts(t.val),r=Ud(n,e);return r==null||r.includes(void 0)?t:r}function Hd(t,e){return Array.prototype.concat(...t.map(n=>OF(n,e)))}function zd(t,e,n){let r=Hd(ke.parseFormat(n),t),o=r.map(s=>SF(s,t)),i=o.find(s=>s.invalidReason);if(i)return{input:e,tokens:r,invalidReason:i.invalidReason};{let[s,a]=TF(o),l=RegExp(s,"i"),[u,d]=FF(e,l,a),[f,m,c]=d?_F(d):[null,null,void 0];if(Rr(d,"a")&&Rr(d,"H"))throw new pn("Can't include meridiem when specifying 24-hour format");return{input:e,tokens:r,regex:l,rawMatches:u,matches:d,result:f,zone:m,specificOffset:c}}}function Z0(t,e,n){let{result:r,zone:o,specificOffset:i,invalidReason:s}=zd(t,e,n);return[r,o,i,s]}function Ud(t,e){if(!t)return null;let r=ke.create(e,t).dtFormatter(kF()),o=r.formatToParts(),i=r.resolvedOptions();return o.map(s=>xF(s,t,i))}var jd="Invalid DateTime",q0=864e13;function Eu(t){return new Qe("unsupported zone",`the zone "${t.name}" is not supported`)}function $d(t){return t.weekData===null&&(t.weekData=na(t.c)),t.weekData}function Gd(t){return t.localWeekData===null&&(t.localWeekData=na(t.c,t.loc.getMinDaysInFirstWeek(),t.loc.getStartOfWeek())),t.localWeekData}function So(t,e){let n={ts:t.ts,zone:t.zone,c:t.c,o:t.o,loc:t.loc,invalid:t.invalid};return new K({...n,...e,old:n})}function nD(t,e,n){let r=t-e*60*1e3,o=n.offset(r);if(e===o)return[r,e];r-=(o-e)*60*1e3;let i=n.offset(r);return o===i?[r,o]:[t-Math.min(o,i)*60*1e3,Math.max(o,i)]}function wu(t,e){t+=e*60*1e3;let n=new Date(t);return{year:n.getUTCFullYear(),month:n.getUTCMonth()+1,day:n.getUTCDate(),hour:n.getUTCHours(),minute:n.getUTCMinutes(),second:n.getUTCSeconds(),millisecond:n.getUTCMilliseconds()}}function Cu(t,e,n){return nD(fi(t),e,n)}function K0(t,e){let n=t.o,r=t.c.year+Math.trunc(e.years),o=t.c.month+Math.trunc(e.months)+Math.trunc(e.quarters)*3,i={...t.c,year:r,month:o,day:Math.min(t.c.day,pi(r,o))+Math.trunc(e.days)+Math.trunc(e.weeks)*7},s=ie.fromObject({years:e.years-Math.trunc(e.years),quarters:e.quarters-Math.trunc(e.quarters),months:e.months-Math.trunc(e.months),weeks:e.weeks-Math.trunc(e.weeks),days:e.days-Math.trunc(e.days),hours:e.hours,minutes:e.minutes,seconds:e.seconds,milliseconds:e.milliseconds}).as("milliseconds"),a=fi(i),[l,u]=nD(a,n,t.zone);return s!==0&&(l+=s,u=t.zone.offset(l)),{ts:l,o:u}}function ua(t,e,n,r,o,i){let{setZone:s,zone:a}=n;if(t&&Object.keys(t).length!==0||e){let l=e||a,u=K.fromObject(t,{...n,zone:l,specificOffset:i});return s?u:u.setZone(a)}else return K.invalid(new Qe("unparsable",`the input "${o}" can't be parsed as ${r}`))}function Su(t,e,n=!0){return t.isValid?ke.create(ue.create("en-US"),{allowZ:n,forceSimple:!0}).formatDateTimeFromString(t,e):null}function Yd(t,e){let n=t.c.year>9999||t.c.year<0,r="";return n&&t.c.year>=0&&(r+="+"),r+=Fe(t.c.year,n?6:4),e?(r+="-",r+=Fe(t.c.month),r+="-",r+=Fe(t.c.day)):(r+=Fe(t.c.month),r+=Fe(t.c.day)),r}function Q0(t,e,n,r,o,i){let s=Fe(t.c.hour);return e?(s+=":",s+=Fe(t.c.minute),(t.c.millisecond!==0||t.c.second!==0||!n)&&(s+=":")):s+=Fe(t.c.minute),(t.c.millisecond!==0||t.c.second!==0||!n)&&(s+=Fe(t.c.second),(t.c.millisecond!==0||!r)&&(s+=".",s+=Fe(t.c.millisecond,3))),o&&(t.isOffsetFixed&&t.offset===0&&!i?s+="Z":t.o<0?(s+="-",s+=Fe(Math.trunc(-t.o/60)),s+=":",s+=Fe(Math.trunc(-t.o%60))):(s+="+",s+=Fe(Math.trunc(t.o/60)),s+=":",s+=Fe(Math.trunc(t.o%60)))),i&&(s+="["+t.zone.ianaName+"]"),s}var rD={month:1,day:1,hour:0,minute:0,second:0,millisecond:0},bF={weekNumber:1,weekday:1,hour:0,minute:0,second:0,millisecond:0},RF={ordinal:1,hour:0,minute:0,second:0,millisecond:0},oD=["year","month","day","hour","minute","second","millisecond"],NF=["weekYear","weekNumber","weekday","hour","minute","second","millisecond"],MF=["year","ordinal","hour","minute","second","millisecond"];function IF(t){let e={year:"year",years:"year",month:"month",months:"month",day:"day",days:"day",hour:"hour",hours:"hour",minute:"minute",minutes:"minute",quarter:"quarter",quarters:"quarter",second:"second",seconds:"second",millisecond:"millisecond",milliseconds:"millisecond",weekday:"weekday",weekdays:"weekday",weeknumber:"weekNumber",weeksnumber:"weekNumber",weeknumbers:"weekNumber",weekyear:"weekYear",weekyears:"weekYear",ordinal:"ordinal"}[t.toLowerCase()];if(!e)throw new ci(t);return e}function J0(t){switch(t.toLowerCase()){case"localweekday":case"localweekdays":return"localWeekday";case"localweeknumber":case"localweeknumbers":return"localWeekNumber";case"localweekyear":case"localweekyears":return"localWeekYear";default:return IF(t)}}function X0(t,e){let n=gn(e.zone,de.defaultZone),r=ue.fromObject(e),o=de.now(),i,s;if(J(t.year))i=o;else{for(let u of oD)J(t[u])&&(t[u]=rD[u]);let a=Od(t)||bd(t);if(a)return K.invalid(a);let l=n.offset(o);[i,s]=Cu(t,l,n)}return new K({ts:i,zone:n,loc:r,o:s})}function eD(t,e,n){let r=J(n.round)?!0:n.round,o=(s,a)=>(s=di(s,r||n.calendary?0:2,!0),e.loc.clone(n).relFormatter(n).format(s,a)),i=s=>n.calendary?e.hasSame(t,s)?0:e.startOf(s).diff(t.startOf(s),s).get(s):e.diff(t,s).get(s);if(n.unit)return o(i(n.unit),n.unit);for(let s of n.units){let a=i(s);if(Math.abs(a)>=1)return o(a,s)}return o(t>e?-0:0,n.units[n.units.length-1])}function tD(t){let e={},n;return t.length>0&&typeof t[t.length-1]=="object"?(e=t[t.length-1],n=Array.from(t).slice(0,t.length-1)):n=Array.from(t),[e,n]}var K=class{constructor(e){let n=e.zone||de.defaultZone,r=e.invalid||(Number.isNaN(e.ts)?new Qe("invalid input"):null)||(n.isValid?null:Eu(n));this.ts=J(e.ts)?de.now():e.ts;let o=null,i=null;if(!r)if(e.old&&e.old.ts===this.ts&&e.old.zone.equals(n))[o,i]=[e.old.c,e.old.o];else{let a=n.offset(this.ts);o=wu(this.ts,a),r=Number.isNaN(o.year)?new Qe("invalid input"):null,o=r?null:o,i=r?null:a}this._zone=n,this.loc=e.loc||ue.create(),this.invalid=r,this.weekData=null,this.localWeekData=null,this.c=o,this.o=i,this.isLuxonDateTime=!0}static now(){return new K({})}static local(){let[e,n]=tD(arguments),[r,o,i,s,a,l,u]=n;return X0({year:r,month:o,day:i,hour:s,minute:a,second:l,millisecond:u},e)}static utc(){let[e,n]=tD(arguments),[r,o,i,s,a,l,u]=n;return e.zone=_e.utcInstance,X0({year:r,month:o,day:i,hour:s,minute:a,second:l,millisecond:u},e)}static fromJSDate(e,n={}){let r=h0(e)?e.valueOf():NaN;if(Number.isNaN(r))return K.invalid("invalid input");let o=gn(n.zone,de.defaultZone);return o.isValid?new K({ts:r,zone:o,loc:ue.fromObject(n)}):K.invalid(Eu(o))}static fromMillis(e,n={}){if(Bn(e))return e<-q0||e>q0?K.invalid("Timestamp out of range"):new K({ts:e,zone:gn(n.zone,de.defaultZone),loc:ue.fromObject(n)});throw new We(`fromMillis requires a numerical input, but received a ${typeof e} with value ${e}`)}static fromSeconds(e,n={}){if(Bn(e))return new K({ts:e*1e3,zone:gn(n.zone,de.defaultZone),loc:ue.fromObject(n)});throw new We("fromSeconds requires a numerical input")}static fromObject(e,n={}){e=e||{};let r=gn(n.zone,de.defaultZone);if(!r.isValid)return K.invalid(Eu(r));let o=ue.fromObject(n),i=hi(e,J0),{minDaysInFirstWeek:s,startOfWeek:a}=kd(i,o),l=de.now(),u=J(n.specificOffset)?r.offset(l):n.specificOffset,d=!J(i.ordinal),f=!J(i.year),m=!J(i.month)||!J(i.day),c=f||m,y=i.weekYear||i.weekNumber;if((c||d)&&y)throw new pn("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(m&&d)throw new pn("Can't mix ordinal dates with month/day");let v=y||i.weekday&&!c,S,h,p=wu(l,u);v?(S=NF,h=bF,p=na(p,s,a)):d?(S=MF,h=RF,p=Du(p)):(S=oD,h=rD);let E=!1;for(let N of S){let ee=i[N];J(ee)?E?i[N]=h[N]:i[N]=p[N]:E=!0}let D=v?d0(i,s,a):d?m0(i):Od(i),x=D||bd(i);if(x)return K.invalid(x);let O=v?Fd(i,s,a):d?_d(i):i,[_,k]=Cu(O,u,r),L=new K({ts:_,zone:r,o:k,loc:o});return i.weekday&&c&&e.weekday!==L.weekday?K.invalid("mismatched weekday",`you can't specify both a weekday of ${i.weekday} and a date of ${L.toISO()}`):L}static fromISO(e,n={}){let[r,o]=b0(e);return ua(r,o,n,"ISO 8601",e)}static fromRFC2822(e,n={}){let[r,o]=R0(e);return ua(r,o,n,"RFC 2822",e)}static fromHTTP(e,n={}){let[r,o]=N0(e);return ua(r,o,n,"HTTP",n)}static fromFormat(e,n,r={}){if(J(e)||J(n))throw new We("fromFormat requires an input string and a format");let{locale:o=null,numberingSystem:i=null}=r,s=ue.fromOpts({locale:o,numberingSystem:i,defaultToEN:!0}),[a,l,u,d]=Z0(s,e,n);return d?K.invalid(d):ua(a,l,r,`format ${n}`,e,u)}static fromString(e,n,r={}){return K.fromFormat(e,n,r)}static fromSQL(e,n={}){let[r,o]=A0(e);return ua(r,o,n,"SQL",e)}static invalid(e,n=null){if(!e)throw new We("need to specify a reason the DateTime is invalid");let r=e instanceof Qe?e:new Qe(e,n);if(de.throwOnInvalid)throw new su(r);return new K({invalid:r})}static isDateTime(e){return e&&e.isLuxonDateTime||!1}static parseFormatForOpts(e,n={}){let r=Ud(e,ue.fromObject(n));return r?r.map(o=>o?o.val:null).join(""):null}static expandFormat(e,n={}){return Hd(ke.parseFormat(e),ue.fromObject(n)).map(o=>o.val).join("")}get(e){return this[e]}get isValid(){return this.invalid===null}get invalidReason(){return this.invalid?this.invalid.reason:null}get invalidExplanation(){return this.invalid?this.invalid.explanation:null}get locale(){return this.isValid?this.loc.locale:null}get numberingSystem(){return this.isValid?this.loc.numberingSystem:null}get outputCalendar(){return this.isValid?this.loc.outputCalendar:null}get zone(){return this._zone}get zoneName(){return this.isValid?this.zone.name:null}get year(){return this.isValid?this.c.year:NaN}get quarter(){return this.isValid?Math.ceil(this.c.month/3):NaN}get month(){return this.isValid?this.c.month:NaN}get day(){return this.isValid?this.c.day:NaN}get hour(){return this.isValid?this.c.hour:NaN}get minute(){return this.isValid?this.c.minute:NaN}get second(){return this.isValid?this.c.second:NaN}get millisecond(){return this.isValid?this.c.millisecond:NaN}get weekYear(){return this.isValid?$d(this).weekYear:NaN}get weekNumber(){return this.isValid?$d(this).weekNumber:NaN}get weekday(){return this.isValid?$d(this).weekday:NaN}get isWeekend(){return this.isValid&&this.loc.getWeekendDays().includes(this.weekday)}get localWeekday(){return this.isValid?Gd(this).weekday:NaN}get localWeekNumber(){return this.isValid?Gd(this).weekNumber:NaN}get localWeekYear(){return this.isValid?Gd(this).weekYear:NaN}get ordinal(){return this.isValid?Du(this.c).ordinal:NaN}get monthShort(){return this.isValid?rr.months("short",{locObj:this.loc})[this.month-1]:null}get monthLong(){return this.isValid?rr.months("long",{locObj:this.loc})[this.month-1]:null}get weekdayShort(){return this.isValid?rr.weekdays("short",{locObj:this.loc})[this.weekday-1]:null}get weekdayLong(){return this.isValid?rr.weekdays("long",{locObj:this.loc})[this.weekday-1]:null}get offset(){return this.isValid?+this.o:NaN}get offsetNameShort(){return this.isValid?this.zone.offsetName(this.ts,{format:"short",locale:this.locale}):null}get offsetNameLong(){return this.isValid?this.zone.offsetName(this.ts,{format:"long",locale:this.locale}):null}get isOffsetFixed(){return this.isValid?this.zone.isUniversal:null}get isInDST(){return this.isOffsetFixed?!1:this.offset>this.set({month:1,day:1}).offset||this.offset>this.set({month:5}).offset}getPossibleOffsets(){if(!this.isValid||this.isOffsetFixed)return[this];let e=864e5,n=6e4,r=fi(this.c),o=this.zone.offset(r-e),i=this.zone.offset(r+e),s=this.zone.offset(r-o*n),a=this.zone.offset(r-i*n);if(s===a)return[this];let l=r-s*n,u=r-a*n,d=wu(l,s),f=wu(u,a);return d.hour===f.hour&&d.minute===f.minute&&d.second===f.second&&d.millisecond===f.millisecond?[So(this,{ts:l}),So(this,{ts:u})]:[this]}get isInLeapYear(){return Eo(this.year)}get daysInMonth(){return pi(this.year,this.month)}get daysInYear(){return this.isValid?br(this.year):NaN}get weeksInWeekYear(){return this.isValid?vo(this.weekYear):NaN}get weeksInLocalWeekYear(){return this.isValid?vo(this.localWeekYear,this.loc.getMinDaysInFirstWeek(),this.loc.getStartOfWeek()):NaN}resolvedLocaleOptions(e={}){let{locale:n,numberingSystem:r,calendar:o}=ke.create(this.loc.clone(e),e).resolvedOptions(this);return{locale:n,numberingSystem:r,outputCalendar:o}}toUTC(e=0,n={}){return this.setZone(_e.instance(e),n)}toLocal(){return this.setZone(de.defaultZone)}setZone(e,{keepLocalTime:n=!1,keepCalendarTime:r=!1}={}){if(e=gn(e,de.defaultZone),e.equals(this.zone))return this;if(e.isValid){let o=this.ts;if(n||r){let i=e.offset(this.ts),s=this.toObject();[o]=Cu(s,i,e)}return So(this,{ts:o,zone:e})}else return K.invalid(Eu(e))}reconfigure({locale:e,numberingSystem:n,outputCalendar:r}={}){let o=this.loc.clone({locale:e,numberingSystem:n,outputCalendar:r});return So(this,{loc:o})}setLocale(e){return this.reconfigure({locale:e})}set(e){if(!this.isValid)return this;let n=hi(e,J0),{minDaysInFirstWeek:r,startOfWeek:o}=kd(n,this.loc),i=!J(n.weekYear)||!J(n.weekNumber)||!J(n.weekday),s=!J(n.ordinal),a=!J(n.year),l=!J(n.month)||!J(n.day),u=a||l,d=n.weekYear||n.weekNumber;if((u||s)&&d)throw new pn("Can't mix weekYear/weekNumber units with year/month/day or ordinals");if(l&&s)throw new pn("Can't mix ordinal dates with month/day");let f;i?f=Fd({...na(this.c,r,o),...n},r,o):J(n.ordinal)?(f={...this.toObject(),...n},J(n.day)&&(f.day=Math.min(pi(f.year,f.month),f.day))):f=_d({...Du(this.c),...n});let[m,c]=Cu(f,this.o,this.zone);return So(this,{ts:m,o:c})}plus(e){if(!this.isValid)return this;let n=ie.fromDurationLike(e);return So(this,K0(this,n))}minus(e){if(!this.isValid)return this;let n=ie.fromDurationLike(e).negate();return So(this,K0(this,n))}startOf(e,{useLocaleWeeks:n=!1}={}){if(!this.isValid)return this;let r={},o=ie.normalizeUnit(e);switch(o){case"years":r.month=1;case"quarters":case"months":r.day=1;case"weeks":case"days":r.hour=0;case"hours":r.minute=0;case"minutes":r.second=0;case"seconds":r.millisecond=0;break;case"milliseconds":break}if(o==="weeks")if(n){let i=this.loc.getStartOfWeek(),{weekday:s}=this;s<i&&(r.weekNumber=this.weekNumber-1),r.weekday=i}else r.weekday=1;if(o==="quarters"){let i=Math.ceil(this.month/3);r.month=(i-1)*3+1}return this.set(r)}endOf(e,n){return this.isValid?this.plus({[e]:1}).startOf(e,n).minus(1):this}toFormat(e,n={}){return this.isValid?ke.create(this.loc.redefaultToEN(n)).formatDateTimeFromString(this,e):jd}toLocaleString(e=kr,n={}){return this.isValid?ke.create(this.loc.clone(n),e).formatDateTime(this):jd}toLocaleParts(e={}){return this.isValid?ke.create(this.loc.clone(e),e).formatDateTimeParts(this):[]}toISO({format:e="extended",suppressSeconds:n=!1,suppressMilliseconds:r=!1,includeOffset:o=!0,extendedZone:i=!1}={}){if(!this.isValid)return null;let s=e==="extended",a=Yd(this,s);return a+="T",a+=Q0(this,s,n,r,o,i),a}toISODate({format:e="extended"}={}){return this.isValid?Yd(this,e==="extended"):null}toISOWeekDate(){return Su(this,"kkkk-'W'WW-c")}toISOTime({suppressMilliseconds:e=!1,suppressSeconds:n=!1,includeOffset:r=!0,includePrefix:o=!1,extendedZone:i=!1,format:s="extended"}={}){return this.isValid?(o?"T":"")+Q0(this,s==="extended",n,e,r,i):null}toRFC2822(){return Su(this,"EEE, dd LLL yyyy HH:mm:ss ZZZ",!1)}toHTTP(){return Su(this.toUTC(),"EEE, dd LLL yyyy HH:mm:ss 'GMT'")}toSQLDate(){return this.isValid?Yd(this,!0):null}toSQLTime({includeOffset:e=!0,includeZone:n=!1,includeOffsetSpace:r=!0}={}){let o="HH:mm:ss.SSS";return(n||e)&&(r&&(o+=" "),n?o+="z":e&&(o+="ZZ")),Su(this,o,!0)}toSQL(e={}){return this.isValid?`${this.toSQLDate()} ${this.toSQLTime(e)}`:null}toString(){return this.isValid?this.toISO():jd}[Symbol.for("nodejs.util.inspect.custom")](){return this.isValid?`DateTime { ts: ${this.toISO()}, zone: ${this.zone.name}, locale: ${this.locale} }`:`DateTime { Invalid, reason: ${this.invalidReason} }`}valueOf(){return this.toMillis()}toMillis(){return this.isValid?this.ts:NaN}toSeconds(){return this.isValid?this.ts/1e3:NaN}toUnixInteger(){return this.isValid?Math.floor(this.ts/1e3):NaN}toJSON(){return this.toISO()}toBSON(){return this.toJSDate()}toObject(e={}){if(!this.isValid)return{};let n={...this.c};return e.includeConfig&&(n.outputCalendar=this.outputCalendar,n.numberingSystem=this.loc.numberingSystem,n.locale=this.loc.locale),n}toJSDate(){return new Date(this.isValid?this.ts:NaN)}diff(e,n="milliseconds",r={}){if(!this.isValid||!e.isValid)return ie.invalid("created by diffing an invalid DateTime");let o={locale:this.locale,numberingSystem:this.numberingSystem,...r},i=y0(n).map(ie.normalizeUnit),s=e.valueOf()>this.valueOf(),a=s?this:e,l=s?e:this,u=H0(a,l,i,o);return s?u.negate():u}diffNow(e="milliseconds",n={}){return this.diff(K.now(),e,n)}until(e){return this.isValid?ye.fromDateTimes(this,e):this}hasSame(e,n,r){if(!this.isValid)return!1;let o=e.valueOf(),i=this.setZone(e.zone,{keepLocalTime:!0});return i.startOf(n,r)<=o&&o<=i.endOf(n,r)}equals(e){return this.isValid&&e.isValid&&this.valueOf()===e.valueOf()&&this.zone.equals(e.zone)&&this.loc.equals(e.loc)}toRelative(e={}){if(!this.isValid)return null;let n=e.base||K.fromObject({},{zone:this.zone}),r=e.padding?this<n?-e.padding:e.padding:0,o=["years","months","days","hours","minutes","seconds"],i=e.unit;return Array.isArray(e.unit)&&(o=e.unit,i=void 0),eD(n,this.plus(r),{...e,numeric:"always",units:o,unit:i})}toRelativeCalendar(e={}){return this.isValid?eD(e.base||K.fromObject({},{zone:this.zone}),this,{...e,numeric:"auto",units:["years","months","days"],calendary:!0}):null}static min(...e){if(!e.every(K.isDateTime))throw new We("min requires all arguments be DateTimes");return Rd(e,n=>n.valueOf(),Math.min)}static max(...e){if(!e.every(K.isDateTime))throw new We("max requires all arguments be DateTimes");return Rd(e,n=>n.valueOf(),Math.max)}static fromFormatExplain(e,n,r={}){let{locale:o=null,numberingSystem:i=null}=r,s=ue.fromOpts({locale:o,numberingSystem:i,defaultToEN:!0});return zd(s,e,n)}static fromStringExplain(e,n,r={}){return K.fromFormatExplain(e,n,r)}static get DATE_SHORT(){return kr}static get DATE_MED(){return As}static get DATE_MED_WITH_WEEKDAY(){return cd}static get DATE_FULL(){return Ls}static get DATE_HUGE(){return Ps}static get TIME_SIMPLE(){return Bs}static get TIME_WITH_SECONDS(){return Vs}static get TIME_WITH_SHORT_OFFSET(){return Ws}static get TIME_WITH_LONG_OFFSET(){return Hs}static get TIME_24_SIMPLE(){return zs}static get TIME_24_WITH_SECONDS(){return Us}static get TIME_24_WITH_SHORT_OFFSET(){return js}static get TIME_24_WITH_LONG_OFFSET(){return $s}static get DATETIME_SHORT(){return Gs}static get DATETIME_SHORT_WITH_SECONDS(){return Ys}static get DATETIME_MED(){return Zs}static get DATETIME_MED_WITH_SECONDS(){return qs}static get DATETIME_MED_WITH_WEEKDAY(){return fd}static get DATETIME_FULL(){return Ks}static get DATETIME_FULL_WITH_SECONDS(){return Qs}static get DATETIME_HUGE(){return Js}static get DATETIME_HUGE_WITH_SECONDS(){return Xs}};function Ci(t){if(K.isDateTime(t))return t;if(t&&t.valueOf&&Bn(t.valueOf()))return K.fromJSDate(t);if(t&&typeof t=="object")return K.fromObject(t);throw new We(`Unknown datetime argument: ${t}, of type ${typeof t}`)}function iD(t){return t==null||t==null?!1:typeof t=="object"&&"isLuxonDateTime"in t&&t.isLuxonDateTime===!0}var Zd=class{filter(e,n,r){if(!e.countField||e.countField.type=="DEFAULT")return!0;let o=AF(e.countField.type),i=r.getValueByCustomizeProperty(n,o,e.countField.value||"");return!(i==null||i==null)}},qd=class{filter(e,n,r){var o;if(e.dateField&&e.dateField.value){let i=e.dateField.value,s=e.dateField.type;if(s=="FILE_CTIME"||s=="FILE_MTIME"||s=="FILE_NAME")return!0;let a=LF((o=e.dateField)==null?void 0:o.type);if(!r.getValueByCustomizeProperty(n,a,i))return!1}return!0}},sD=[new Zd,new qd];function AF(t){switch(t){case"PAGE_PROPERTY":return"PAGE";case"TASK_PROPERTY":return"TASK";default:return"UNKNOWN"}}function LF(t){switch(t){case"FILE_CTIME":case"FILE_MTIME":case"FILE_NAME":case"PAGE_PROPERTY":return"PAGE";case"TASK_PROPERTY":return"TASK";default:return"UNKNOWN"}}var xi=class{query(e,n){this.reconcileSourceValueIfNotExists(e);let r=this.checkAndGetApi(n),o=this.doQuery(r,e),i=this.mapToQueryData(o,e),s=i.filter(l=>!l.date);return s.length>0&&console.warn(s.length+" data can't be converted to date, please check the date field format",s),i.filter(l=>l.date!=null).groupBy(l=>{var u;return(u=l.date)==null?void 0:u.toFormat("yyyy-MM-dd")}).map(l=>{var f,m;let u=this.countSumValueByCustomizeProperty(l.rows,(f=e.countField)==null?void 0:f.type,(m=e.countField)==null?void 0:m.value),d=l.rows.map(c=>{var S,h,p,E;let y;e.type=="PAGE"?y=c.raw.file.name:y=c.raw.text;let v=this.getAndConvertValueByCustomizeProperty(c,(S=e.countField)==null?void 0:S.type,(h=e.countField)==null?void 0:h.value);return((p=e.countField)==null?void 0:p.type)=="PAGE_PROPERTY"&&(y+=` [${(E=e.countField)==null?void 0:E.value}:${v}]`),{label:y,value:v,open:D=>PF(D,e,c,n)}}).array();return{date:l.key,value:u,items:d}}).array()}reconcileSourceValueIfNotExists(e){e.value||(e.value='""')}checkAndGetApi(e){let n=(0,aD.getAPI)(e);if(!n)throw new Ne({summary:"Initialize Dataview failed",recommends:["Please install Dataview plugin"]});return n}mapToQueryData(e,n){if(n.dateField&&n.dateField.type){let r=n.dateField.value,o=n.dateField.type,i=n.dateField.format;return e.filter(s=>sD.every(a=>a.filter(n,s,this))).map(s=>{var l;let a=s.file.name;if(o=="FILE_CTIME")return new Ln(s,s.file.ctime);if(o=="FILE_MTIME")return new Ln(s,s.file.mtime);if(o=="FILE_NAME"){let u=this.toDateTime(a,a,i);return u?new Ln(s,u):new Ln(s)}else{let u=this.getPropertySourceByDateFieldType((l=n.dateField)==null?void 0:l.type),d=this.getValueByCustomizeProperty(s,u,r||"");if(iD(d))return new Ln(s,d);{let f=this.toDateTime(a,d,i);return new Ln(s,f)}}})}else return e.map(r=>new Ln(r,r.file.ctime))}toDateTime(e,n,r){if(typeof n!="string"){console.warn("can't parse date, it's a valid format? "+n+" in page "+e);return}try{let o=null;if(r&&(o=K.fromFormat(n,r),o.isValid)||(o=K.fromISO(n),o.isValid)||(o=K.fromRFC2822(n),o.isValid)||(o=K.fromHTTP(n),o.isValid)||(o=K.fromSQL(n),o.isValid)||(o=K.fromFormat(n,"yyyy-MM-dd HH:mm"),o.isValid)||(o=K.fromFormat(n,"yyyy-MM-dd'T'HH:mm"),o.isValid))return o}catch(o){console.warn("can't parse date, it's a valid format? "+n+" in page "+e)}}countSumValueByCustomizeProperty(e,n,r){return!n||n=="DEFAULT"?e.length:r?e.map(o=>this.getAndConvertValueByCustomizeProperty(o,n,r)).array().reduce((o,i)=>o+i,0):e.length}getAndConvertValueByCustomizeProperty(e,n,r){if(r){let o;switch(n){case"PAGE_PROPERTY":o="PAGE";break;case"TASK_PROPERTY":o="TASK";break;default:o="UNKNOWN";break}let i=this.getValueByCustomizeProperty(e.raw,o,r);if(i==null||i==null)return 0;if(i instanceof Array)return i.length;if(typeof i=="number"||i instanceof Number)return i;if(typeof i=="string"||i instanceof String){let s=dy(i);return s!=null?s:i.trim()===""?0:1}return typeof i=="boolean"||i instanceof Boolean?i?1:0:1}else return 1}getPropertySourceByCountFieldType(e){switch(e){case"PAGE_PROPERTY":return"PAGE";case"TASK_PROPERTY":return"TASK";default:return"UNKNOWN"}}getPropertySourceByDateFieldType(e){switch(e){case"FILE_CTIME":case"FILE_MTIME":case"FILE_NAME":case"PAGE_PROPERTY":return"PAGE";case"TASK_PROPERTY":return"TASK";default:return"UNKNOWN"}}};function PF(t,e,n,r){var o;if(e.type!="PAGE"){let i={eState:{cursor:{from:{line:n.raw.line,ch:n.raw.position.start.col},to:{line:n.raw.line+n.raw.lineCount-1,ch:n.raw.position.end.col}},line:n.raw.line}};r.workspace.openLinkText(n.raw.link.toFile().obsidianLink(),n.raw.path,t.ctrlKey||t.metaKey&&Kd.Platform.isMacOS,i)}else r.workspace.openLinkText((o=n.raw.file)==null?void 0:o.path,"",t.ctrlKey||t.metaKey&&Kd.Platform.isMacOS)}var xu=class extends xi{accept(e){return e.type==="PAGE"}doQuery(e,n){return e.pages(n.value)}getValueByCustomizeProperty(e,n,r){if(n==="PAGE")return e[r]}};var Tu=class extends xi{accept(e){return e.type==="ALL_TASK"||e.type==="TASK_IN_SPECIFIC_PAGE"}doQuery(e,n){let r;n.type==="ALL_TASK"?r=e.pages('""'):r=e.pages(n.value);let o=r.filter(i=>i.file.tasks.length>0).flatMap(i=>i.file.tasks.map(a=>({...a,file:i.file})).array());return!n.filters||n.filters.length===0?o:o.filter(i=>n.filters?n.filters.every(s=>{switch(s.type){case"NONE":return!0;case"STATUS_IS":return this.filterByStatusIs(s,i);case"STATUS_IN":return this.filterByStatusIn(s,i);case"CONTAINS_ANY_TAG":return this.filterByContainsAnyTag(s,i);default:return!0}}):!0)}filterByStatusIn(e,n){return e.value.some(o=>o=="COMPLETED"?n.completed:o=="INCOMPLETE"?n.status==" ":o=="CANCELED"?n.status=="-":o=="ANY"?!0:o=="FULLY_COMPLETED"?n.fullyCompleted:n.status===o)}filterByStatusIs(e,n){return e.value=="COMPLETED"?n.completed:e.value=="INCOMPLETE"?n.status==" ":e.value=="CANCELED"?n.status=="-":e.value=="ANY"?!0:e.value=="FULLY_COMPLETED"?n.fullyCompleted:n.status===e.value}filterByContainsAnyTag(e,n){if((e==null?void 0:e.value)instanceof Array){let r=e==null?void 0:e.value;return r.length===0?!0:n.tags.some(o=>r.find(i=>i.toLowerCase()===o.toLowerCase())!==void 0)}else return!0}getValueByCustomizeProperty(e,n,r){if(n==="PAGE"&&e.file)return e.file[r];if(n==="TASK")return e[r]}};var Fu=class{constructor(){this.dataSourceQueries=[new xu,new Tu]}query(e,n){let r=this.dataSourceQueries.find(o=>o.accept(e));if(!r)throw new Ne({summary:"Unsupported data source",recommends:["Please use supported data source"]});return r.query(e,n)}};var Co=class{constructor(){this.title="Contributions",this.graphType="default",this.dateRangeValue=180,this.dateRangeType="LATEST_DAYS",this.startOfWeek=ws()?1:0,this.showCellRuleIndicators=!0,this.titleStyle={textAlign:"left",fontSize:"1.5em",fontWeight:"normal"},this.dataSource={type:"PAGE",value:"",dateField:{}},this.fillTheScreen=!1,this.enableMainContainerShadow=!1,this.query=void 0,this.dateFieldFormat=void 0,this.dateField=void 0,this.days=void 0}static toContributionGraphConfig(e){let{query:n,dateField:r,...o}=e;if(e.dateRangeType!="FIXED_DATE_RANGE"){if(e.dateRangeType=="LATEST_DAYS")return{days:e.dateRangeValue,...o};if(e.dateRangeType=="LATEST_MONTH"){let{start:i,end:s}=cy(e.dateRangeValue||0);return{...o,days:void 0,fromDate:Nn(i),toDate:Nn(s)}}if(e.dateRangeType=="LATEST_YEAR"){let{start:i,end:s}=uy(e.dateRangeValue||0);return{...o,days:void 0,fromDate:Nn(i),toDate:Nn(s)}}}return o}static validate(e){if(!e)throw new Ne(Vl());if(!e.dataSource&&!e.data)throw new Ne(gy());if(e.graphType&&!["default","month-track","calendar"].includes(e.graphType))throw new Ne(Dy(e.graphType));if(!e.dateRangeValue&&(!e.fromDate||!e.toDate))throw new Ne(vy());if(e.fromDate||e.toDate){let n=/^\d{4}-\d{2}-\d{2}$/;if(e.fromDate&&!n.test(e.fromDate))throw new Ne(kf(e.fromDate));if(e.toDate&&!n.test(e.toDate))throw new Ne(kf(e.toDate))}if(e.startOfWeek){let n=[0,1,2,3,4,5,6];if(typeof e.startOfWeek!="number")try{e.startOfWeek=parseInt(e.startOfWeek)}catch(r){throw new Ne(Of(e.startOfWeek))}if(!n.includes(e.startOfWeek))throw new Ne(Of(e.startOfWeek))}}};var Ir=class{constructor(){}static reconcile(e){return Ir.reconcile_from_0_4_0(e)}static reconcile_from_0_4_0(e){if(e.dataSource||(e.dataSource={type:"PAGE",value:e.query||'""',filters:[],dateField:{type:"PAGE_PROPERTY",value:e.dateField,format:e.dateFieldFormat},countField:{type:"DEFAULT"}}),!e.dateRangeType){let r=e.days!==void 0?"LATEST_DAYS":"FIXED_DATE_RANGE";e.dateRangeType=r}return e.dateRangeValue||(e.dateRangeValue=e.days),e.query=void 0,e.dateField=void 0,e.dateFieldFormat=void 0,e}};var uD=B(ud()),Ti=class{constructor(){this.dataSourceQuery=new Fu}async renderFromCodeBlock(e,n,r,o){try{let i=this.loadYamlConfig(n,e);await this.renderFromYaml(i,n,o)}catch(i){if(i instanceof Ne)ln.renderErrorTips(n,i.summary,i.recommends);else{console.error(i);let s="unexpected error: "+i.message;ln.renderErrorTips(n,s)}}}async renderFromYaml(e,n,r){let o=()=>{try{Co.validate(e);let s=this.dataSourceQuery.query(e.dataSource,r),a=[];e.data&&a.push(...e.data),a.push(...s),e.data=a,ln.render(n,Co.toContributionGraphConfig(e))}catch(s){if(s instanceof Ne)ln.renderErrorTips(n,s.summary,s.recommends);else{console.error(s);let a="unexpected error: "+s.message;ln.renderErrorTips(n,a)}}},i=(0,uD.getAPI)(r);if(!i)throw new Ne({summary:"Initialize Dataview failed",recommends:["Please install Dataview plugin"]});i.index.initialized?o():r.metadataCache.on("dataview:index-ready",()=>{o()})}loadYamlConfig(e,n){var r;if(n==null||n.trim()=="")throw new Ne(Vl());try{let o=(0,lD.parseYaml)(n);return Ir.reconcile(o)}catch(o){throw(r=o.mark)!=null&&r.line?new Ne({summary:"yaml parse error at line "+(o.mark.line+1)+", please check the format"}):new Ne({summary:"content parse error, please check the format(such as blank, indent)"})}}};var sn=require("obsidian"),BS=B(se()),VS=B(Uw());var El=B(se());var yh=B(se()),Qc=B(X());function Yw(t){let{onChoose:e}=t,[n,r]=(0,yh.useState)(t.options),[o,i]=(0,yh.useState)(t.defaultValue),s=a=>{i(a.value),e(a)};return(0,Qc.jsx)("div",{className:"contribution-graph-choose",children:n.map(a=>(0,Qc.jsx)("div",{className:a.value==o?"item choosed":"item","ariea-label":a.tip,onClick:l=>s(a),children:(0,Qc.jsx)("div",{className:"icon",children:a.icon})},a.value))})}var re=B(se(),1),ll=B(se(),1);function $n(t){return Zw(t)?(t.nodeName||"").toLowerCase():"#document"}function Dt(t){var e;return(t==null||(e=t.ownerDocument)==null?void 0:e.defaultView)||window}function Gn(t){var e;return(e=(Zw(t)?t.ownerDocument:t.document)||window.document)==null?void 0:e.documentElement}function Zw(t){return t instanceof Node||t instanceof Dt(t).Node}function Pe(t){return t instanceof Element||t instanceof Dt(t).Element}function kt(t){return t instanceof HTMLElement||t instanceof Dt(t).HTMLElement}function Jc(t){return typeof ShadowRoot=="undefined"?!1:t instanceof ShadowRoot||t instanceof Dt(t).ShadowRoot}function ns(t){let{overflow:e,overflowX:n,overflowY:r,display:o}=vt(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!["inline","contents"].includes(o)}function qw(t){return["table","td","th"].includes($n(t))}function Xc(t){let e=ef(),n=vt(t);return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!e&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!e&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function Kw(t){let e=pr(t);for(;kt(e)&&!no(e);){if(Xc(e))return e;e=pr(e)}return null}function ef(){return typeof CSS=="undefined"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function no(t){return["html","body","#document"].includes($n(t))}function vt(t){return Dt(t).getComputedStyle(t)}function nl(t){return Pe(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.pageXOffset,scrollTop:t.pageYOffset}}function pr(t){if($n(t)==="html")return t;let e=t.assignedSlot||t.parentNode||Jc(t)&&t.host||Gn(t);return Jc(e)?e.host:e}function Qw(t){let e=pr(t);return no(e)?t.ownerDocument?t.ownerDocument.body:t.body:kt(e)&&ns(e)?e:Qw(e)}function Ht(t,e,n){var r;e===void 0&&(e=[]),n===void 0&&(n=!0);let o=Qw(t),i=o===((r=t.ownerDocument)==null?void 0:r.body),s=Dt(o);return i?e.concat(s,s.visualViewport||[],ns(o)?o:[],s.frameElement&&n?Ht(s.frameElement):[]):e.concat(o,Ht(o,[],n))}function gh(t,e){if(!t||!e)return!1;let n=e.getRootNode&&e.getRootNode();if(t.contains(e))return!0;if(n&&Jc(n)){let r=e;for(;r;){if(t===r)return!0;r=r.parentNode||r.host}}return!1}function Jw(t){return"nativeEvent"in t}function Xw(t){return t.matches("html,body")}function Dh(t){return(t==null?void 0:t.ownerDocument)||document}function tf(t,e){if(e==null)return!1;if("composedPath"in t)return t.composedPath().includes(e);let n=t;return n.target!=null&&e.contains(n.target)}function rs(t){return"composedPath"in t?t.composedPath()[0]:t.target}var e1=["top","right","bottom","left"];var hr=Math.min,nn=Math.max,ol=Math.round,il=Math.floor,yr=t=>({x:t,y:t}),s2={left:"right",right:"left",bottom:"top",top:"bottom"},a2={start:"end",end:"start"};function vh(t,e,n){return nn(t,hr(e,n))}function Vo(t,e){return typeof t=="function"?t(e):t}function Yn(t){return t.split("-")[0]}function sl(t){return t.split("-")[1]}function Eh(t){return t==="x"?"y":"x"}function wh(t){return t==="y"?"height":"width"}function os(t){return["top","bottom"].includes(Yn(t))?"y":"x"}function Sh(t){return Eh(os(t))}function t1(t,e,n){n===void 0&&(n=!1);let r=sl(t),o=Sh(t),i=wh(o),s=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return e.reference[i]>e.floating[i]&&(s=rl(s)),[s,rl(s)]}function n1(t){let e=rl(t);return[nf(t),e,nf(e)]}function nf(t){return t.replace(/start|end/g,e=>a2[e])}function l2(t,e,n){let r=["left","right"],o=["right","left"],i=["top","bottom"],s=["bottom","top"];switch(t){case"top":case"bottom":return n?e?o:r:e?r:o;case"left":case"right":return e?i:s;default:return[]}}function r1(t,e,n,r){let o=sl(t),i=l2(Yn(t),n==="start",r);return o&&(i=i.map(s=>s+"-"+o),e&&(i=i.concat(i.map(nf)))),i}function rl(t){return t.replace(/left|right|bottom|top/g,e=>s2[e])}function u2(t){return{top:0,right:0,bottom:0,left:0,...t}}function Ch(t){return typeof t!="number"?u2(t):{top:t,right:t,bottom:t,left:t}}function gr(t){return{...t,top:t.y,left:t.x,right:t.x+t.width,bottom:t.y+t.height}}function o1(t,e,n){let{reference:r,floating:o}=t,i=os(e),s=Sh(e),a=wh(s),l=Yn(e),u=i==="y",d=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,m=r[a]/2-o[a]/2,c;switch(l){case"top":c={x:d,y:r.y-o.height};break;case"bottom":c={x:d,y:r.y+r.height};break;case"right":c={x:r.x+r.width,y:f};break;case"left":c={x:r.x-o.width,y:f};break;default:c={x:r.x,y:r.y}}switch(sl(e)){case"start":c[s]-=m*(n&&u?-1:1);break;case"end":c[s]+=m*(n&&u?-1:1);break}return c}var a1=async(t,e,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:s}=n,a=i.filter(Boolean),l=await(s.isRTL==null?void 0:s.isRTL(e)),u=await s.getElementRects({reference:t,floating:e,strategy:o}),{x:d,y:f}=o1(u,r,l),m=r,c={},y=0;for(let v=0;v<a.length;v++){let{name:S,fn:h}=a[v],{x:p,y:E,data:D,reset:x}=await h({x:d,y:f,initialPlacement:r,placement:m,strategy:o,middlewareData:c,rects:u,platform:s,elements:{reference:t,floating:e}});if(d=p!=null?p:d,f=E!=null?E:f,c={...c,[S]:{...c[S],...D}},x&&y<=50){y++,typeof x=="object"&&(x.placement&&(m=x.placement),x.rects&&(u=x.rects===!0?await s.getElementRects({reference:t,floating:e,strategy:o}):x.rects),{x:d,y:f}=o1(u,m,l)),v=-1;continue}}return{x:d,y:f,placement:m,strategy:o,middlewareData:c}};async function ro(t,e){var n;e===void 0&&(e={});let{x:r,y:o,platform:i,rects:s,elements:a,strategy:l}=t,{boundary:u="clippingAncestors",rootBoundary:d="viewport",elementContext:f="floating",altBoundary:m=!1,padding:c=0}=Vo(e,t),y=Ch(c),S=a[m?f==="floating"?"reference":"floating":f],h=gr(await i.getClippingRect({element:(n=await(i.isElement==null?void 0:i.isElement(S)))==null||n?S:S.contextElement||await(i.getDocumentElement==null?void 0:i.getDocumentElement(a.floating)),boundary:u,rootBoundary:d,strategy:l})),p=f==="floating"?{...s.floating,x:r,y:o}:s.reference,E=await(i.getOffsetParent==null?void 0:i.getOffsetParent(a.floating)),D=await(i.isElement==null?void 0:i.isElement(E))?await(i.getScale==null?void 0:i.getScale(E))||{x:1,y:1}:{x:1,y:1},x=gr(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({rect:p,offsetParent:E,strategy:l}):p);return{top:(h.top-x.top+y.top)/D.y,bottom:(x.bottom-h.bottom+y.bottom)/D.y,left:(h.left-x.left+y.left)/D.x,right:(x.right-h.right+y.right)/D.x}}var Dr=function(t){return t===void 0&&(t={}),{name:"flip",options:t,async fn(e){var n,r;let{placement:o,middlewareData:i,rects:s,initialPlacement:a,platform:l,elements:u}=e,{mainAxis:d=!0,crossAxis:f=!0,fallbackPlacements:m,fallbackStrategy:c="bestFit",fallbackAxisSideDirection:y="none",flipAlignment:v=!0,...S}=Vo(t,e);if((n=i.arrow)!=null&&n.alignmentOffset)return{};let h=Yn(o),p=Yn(a)===a,E=await(l.isRTL==null?void 0:l.isRTL(u.floating)),D=m||(p||!v?[rl(a)]:n1(a));!m&&y!=="none"&&D.push(...r1(a,v,y,E));let x=[a,...D],O=await ro(e,S),_=[],k=((r=i.flip)==null?void 0:r.overflows)||[];if(d&&_.push(O[h]),f){let oe=t1(o,s,E);_.push(O[oe[0]],O[oe[1]])}if(k=[...k,{placement:o,overflows:_}],!_.every(oe=>oe<=0)){var L,N;let oe=(((L=i.flip)==null?void 0:L.index)||0)+1,b=x[oe];if(b)return{data:{index:oe,overflows:k},reset:{placement:b}};let $=(N=k.filter(T=>T.overflows[0]<=0).sort((T,P)=>T.overflows[1]-P.overflows[1])[0])==null?void 0:N.placement;if(!$)switch(c){case"bestFit":{var ee;let T=(ee=k.map(P=>[P.placement,P.overflows.filter(Y=>Y>0).reduce((Y,Z)=>Y+Z,0)]).sort((P,Y)=>P[1]-Y[1])[0])==null?void 0:ee[0];T&&($=T);break}case"initialPlacement":$=a;break}if(o!==$)return{reset:{placement:$}}}return{}}}};function i1(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function s1(t){return e1.some(e=>t[e]>=0)}var al=function(t){return t===void 0&&(t={}),{name:"hide",options:t,async fn(e){let{rects:n}=e,{strategy:r="referenceHidden",...o}=Vo(t,e);switch(r){case"referenceHidden":{let i=await ro(e,{...o,elementContext:"reference"}),s=i1(i,n.reference);return{data:{referenceHiddenOffsets:s,referenceHidden:s1(s)}}}case"escaped":{let i=await ro(e,{...o,altBoundary:!0}),s=i1(i,n.floating);return{data:{escapedOffsets:s,escaped:s1(s)}}}default:return{}}}}};function l1(t){let e=hr(...t.map(i=>i.left)),n=hr(...t.map(i=>i.top)),r=nn(...t.map(i=>i.right)),o=nn(...t.map(i=>i.bottom));return{x:e,y:n,width:r-e,height:o-n}}function c2(t){let e=t.slice().sort((o,i)=>o.y-i.y),n=[],r=null;for(let o=0;o<e.length;o++){let i=e[o];!r||i.y-r.y>r.height/2?n.push([i]):n[n.length-1].push(i),r=i}return n.map(o=>gr(l1(o)))}var vr=function(t){return t===void 0&&(t={}),{name:"inline",options:t,async fn(e){let{placement:n,elements:r,rects:o,platform:i,strategy:s}=e,{padding:a=2,x:l,y:u}=Vo(t,e),d=Array.from(await(i.getClientRects==null?void 0:i.getClientRects(r.reference))||[]),f=c2(d),m=gr(l1(d)),c=Ch(a);function y(){if(f.length===2&&f[0].left>f[1].right&&l!=null&&u!=null)return f.find(S=>l>S.left-c.left&&l<S.right+c.right&&u>S.top-c.top&&u<S.bottom+c.bottom)||m;if(f.length>=2){if(os(n)==="y"){let N=f[0],ee=f[f.length-1],oe=Yn(n)==="top",b=N.top,$=ee.bottom,T=oe?N.left:ee.left,P=oe?N.right:ee.right,Y=P-T,Z=$-b;return{top:b,bottom:$,left:T,right:P,width:Y,height:Z,x:T,y:b}}let S=Yn(n)==="left",h=nn(...f.map(N=>N.right)),p=hr(...f.map(N=>N.left)),E=f.filter(N=>S?N.left===p:N.right===h),D=E[0].top,x=E[E.length-1].bottom,O=p,_=h,k=_-O,L=x-D;return{top:D,bottom:x,left:O,right:_,width:k,height:L,x:O,y:D}}return m}let v=await i.getElementRects({reference:{getBoundingClientRect:y},floating:r.floating,strategy:s});return o.reference.x!==v.reference.x||o.reference.y!==v.reference.y||o.reference.width!==v.reference.width||o.reference.height!==v.reference.height?{reset:{rects:v}}:{}}}};async function f2(t,e){let{placement:n,platform:r,elements:o}=t,i=await(r.isRTL==null?void 0:r.isRTL(o.floating)),s=Yn(n),a=sl(n),l=os(n)==="y",u=["left","top"].includes(s)?-1:1,d=i&&l?-1:1,f=Vo(e,t),{mainAxis:m,crossAxis:c,alignmentAxis:y}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...f};return a&&typeof y=="number"&&(c=a==="end"?y*-1:y),l?{x:c*d,y:m*u}:{x:m*u,y:c*d}}var Zn=function(t){return t===void 0&&(t=0),{name:"offset",options:t,async fn(e){var n,r;let{x:o,y:i,placement:s,middlewareData:a}=e,l=await f2(e,t);return s===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:s}}}}},Er=function(t){return t===void 0&&(t={}),{name:"shift",options:t,async fn(e){let{x:n,y:r,placement:o}=e,{mainAxis:i=!0,crossAxis:s=!1,limiter:a={fn:S=>{let{x:h,y:p}=S;return{x:h,y:p}}},...l}=Vo(t,e),u={x:n,y:r},d=await ro(e,l),f=os(Yn(o)),m=Eh(f),c=u[m],y=u[f];if(i){let S=m==="y"?"top":"left",h=m==="y"?"bottom":"right",p=c+d[S],E=c-d[h];c=vh(p,c,E)}if(s){let S=f==="y"?"top":"left",h=f==="y"?"bottom":"right",p=y+d[S],E=y-d[h];y=vh(p,y,E)}let v=a.fn({...e,[m]:c,[f]:y});return{...v,data:{x:v.x-n,y:v.y-r}}}}};function p1(t){let e=vt(t),n=parseFloat(e.width)||0,r=parseFloat(e.height)||0,o=kt(t),i=o?t.offsetWidth:n,s=o?t.offsetHeight:r,a=ol(n)!==i||ol(r)!==s;return a&&(n=i,r=s),{width:n,height:r,$:a}}function xh(t){return Pe(t)?t:t.contextElement}function is(t){let e=xh(t);if(!kt(e))return yr(1);let n=e.getBoundingClientRect(),{width:r,height:o,$:i}=p1(e),s=(i?ol(n.width):n.width)/r,a=(i?ol(n.height):n.height)/o;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}var m2=yr(0);function h1(t){let e=Dt(t);return!ef()||!e.visualViewport?m2:{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}}function p2(t,e,n){return e===void 0&&(e=!1),!n||e&&n!==Dt(t)?!1:e}function Wo(t,e,n,r){e===void 0&&(e=!1),n===void 0&&(n=!1);let o=t.getBoundingClientRect(),i=xh(t),s=yr(1);e&&(r?Pe(r)&&(s=is(r)):s=is(t));let a=p2(i,n,r)?h1(i):yr(0),l=(o.left+a.x)/s.x,u=(o.top+a.y)/s.y,d=o.width/s.x,f=o.height/s.y;if(i){let m=Dt(i),c=r&&Pe(r)?Dt(r):r,y=m.frameElement;for(;y&&r&&c!==m;){let v=is(y),S=y.getBoundingClientRect(),h=vt(y),p=S.left+(y.clientLeft+parseFloat(h.paddingLeft))*v.x,E=S.top+(y.clientTop+parseFloat(h.paddingTop))*v.y;l*=v.x,u*=v.y,d*=v.x,f*=v.y,l+=p,u+=E,y=Dt(y).frameElement}}return gr({width:d,height:f,x:l,y:u})}function h2(t){let{rect:e,offsetParent:n,strategy:r}=t,o=kt(n),i=Gn(n);if(n===i)return e;let s={scrollLeft:0,scrollTop:0},a=yr(1),l=yr(0);if((o||!o&&r!=="fixed")&&(($n(n)!=="body"||ns(i))&&(s=nl(n)),kt(n))){let u=Wo(n);a=is(n),l.x=u.x+n.clientLeft,l.y=u.y+n.clientTop}return{width:e.width*a.x,height:e.height*a.y,x:e.x*a.x-s.scrollLeft*a.x+l.x,y:e.y*a.y-s.scrollTop*a.y+l.y}}function y2(t){return Array.from(t.getClientRects())}function y1(t){return Wo(Gn(t)).left+nl(t).scrollLeft}function g2(t){let e=Gn(t),n=nl(t),r=t.ownerDocument.body,o=nn(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),i=nn(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight),s=-n.scrollLeft+y1(t),a=-n.scrollTop;return vt(r).direction==="rtl"&&(s+=nn(e.clientWidth,r.clientWidth)-o),{width:o,height:i,x:s,y:a}}function D2(t,e){let n=Dt(t),r=Gn(t),o=n.visualViewport,i=r.clientWidth,s=r.clientHeight,a=0,l=0;if(o){i=o.width,s=o.height;let u=ef();(!u||u&&e==="fixed")&&(a=o.offsetLeft,l=o.offsetTop)}return{width:i,height:s,x:a,y:l}}function v2(t,e){let n=Wo(t,!0,e==="fixed"),r=n.top+t.clientTop,o=n.left+t.clientLeft,i=kt(t)?is(t):yr(1),s=t.clientWidth*i.x,a=t.clientHeight*i.y,l=o*i.x,u=r*i.y;return{width:s,height:a,x:l,y:u}}function u1(t,e,n){let r;if(e==="viewport")r=D2(t,n);else if(e==="document")r=g2(Gn(t));else if(Pe(e))r=v2(e,n);else{let o=h1(t);r={...e,x:e.x-o.x,y:e.y-o.y}}return gr(r)}function g1(t,e){let n=pr(t);return n===e||!Pe(n)||no(n)?!1:vt(n).position==="fixed"||g1(n,e)}function E2(t,e){let n=e.get(t);if(n)return n;let r=Ht(t,[],!1).filter(a=>Pe(a)&&$n(a)!=="body"),o=null,i=vt(t).position==="fixed",s=i?pr(t):t;for(;Pe(s)&&!no(s);){let a=vt(s),l=Xc(s);!l&&a.position==="fixed"&&(o=null),(i?!l&&!o:!l&&a.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||ns(s)&&!l&&g1(t,s))?r=r.filter(d=>d!==s):o=a,s=pr(s)}return e.set(t,r),r}function w2(t){let{element:e,boundary:n,rootBoundary:r,strategy:o}=t,s=[...n==="clippingAncestors"?E2(e,this._c):[].concat(n),r],a=s[0],l=s.reduce((u,d)=>{let f=u1(e,d,o);return u.top=nn(f.top,u.top),u.right=hr(f.right,u.right),u.bottom=hr(f.bottom,u.bottom),u.left=nn(f.left,u.left),u},u1(e,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function S2(t){return p1(t)}function C2(t,e,n){let r=kt(e),o=Gn(e),i=n==="fixed",s=Wo(t,!0,i,e),a={scrollLeft:0,scrollTop:0},l=yr(0);if(r||!r&&!i)if(($n(e)!=="body"||ns(o))&&(a=nl(e)),r){let u=Wo(e,!0,i,e);l.x=u.x+e.clientLeft,l.y=u.y+e.clientTop}else o&&(l.x=y1(o));return{x:s.left+a.scrollLeft-l.x,y:s.top+a.scrollTop-l.y,width:s.width,height:s.height}}function c1(t,e){return!kt(t)||vt(t).position==="fixed"?null:e?e(t):t.offsetParent}function D1(t,e){let n=Dt(t);if(!kt(t))return n;let r=c1(t,e);for(;r&&qw(r)&&vt(r).position==="static";)r=c1(r,e);return r&&($n(r)==="html"||$n(r)==="body"&&vt(r).position==="static"&&!Xc(r))?n:r||Kw(t)||n}var x2=async function(t){let{reference:e,floating:n,strategy:r}=t,o=this.getOffsetParent||D1,i=this.getDimensions;return{reference:C2(e,await o(n),r),floating:{x:0,y:0,...await i(n)}}};function T2(t){return vt(t).direction==="rtl"}var rf={convertOffsetParentRelativeRectToViewportRelativeRect:h2,getDocumentElement:Gn,getClippingRect:w2,getOffsetParent:D1,getElementRects:x2,getClientRects:y2,getDimensions:S2,getScale:is,isElement:Pe,isRTL:T2};function F2(t,e){let n=null,r,o=Gn(t);function i(){clearTimeout(r),n&&n.disconnect(),n=null}function s(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),i();let{left:u,top:d,width:f,height:m}=t.getBoundingClientRect();if(a||e(),!f||!m)return;let c=il(d),y=il(o.clientWidth-(u+f)),v=il(o.clientHeight-(d+m)),S=il(u),p={rootMargin:-c+"px "+-y+"px "+-v+"px "+-S+"px",threshold:nn(0,hr(1,l))||1},E=!0;function D(x){let O=x[0].intersectionRatio;if(O!==l){if(!E)return s();O?s(!1,O):r=setTimeout(()=>{s(!1,1e-7)},100)}E=!1}try{n=new IntersectionObserver(D,{...p,root:o.ownerDocument})}catch(x){n=new IntersectionObserver(D,p)}n.observe(t)}return s(!0),i}function oo(t,e,n,r){r===void 0&&(r={});let{ancestorScroll:o=!0,ancestorResize:i=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=r,u=xh(t),d=o||i?[...u?Ht(u):[],...Ht(e)]:[];d.forEach(h=>{o&&h.addEventListener("scroll",n,{passive:!0}),i&&h.addEventListener("resize",n)});let f=u&&a?F2(u,n):null,m=-1,c=null;s&&(c=new ResizeObserver(h=>{let[p]=h;p&&p.target===u&&c&&(c.unobserve(e),cancelAnimationFrame(m),m=requestAnimationFrame(()=>{c&&c.observe(e)})),n()}),u&&!l&&c.observe(u),c.observe(e));let y,v=l?Wo(t):null;l&&S();function S(){let h=Wo(t);v&&(h.x!==v.x||h.y!==v.y||h.width!==v.width||h.height!==v.height)&&n(),v=h,y=requestAnimationFrame(S)}return n(),()=>{d.forEach(h=>{o&&h.removeEventListener("scroll",n),i&&h.removeEventListener("resize",n)}),f&&f(),c&&c.disconnect(),c=null,l&&cancelAnimationFrame(y)}}var of=(t,e,n)=>{let r=new Map,o={platform:rf,...n},i={...o.platform,_c:r};return a1(t,e,{...o,platform:i})};var ze=B(se(),1),lf=B(se(),1),w1=B(qc(),1);var sf=typeof document!="undefined"?lf.useLayoutEffect:lf.useEffect;function af(t,e){if(t===e)return!0;if(typeof t!=typeof e)return!1;if(typeof t=="function"&&t.toString()===e.toString())return!0;let n,r,o;if(t&&e&&typeof t=="object"){if(Array.isArray(t)){if(n=t.length,n!=e.length)return!1;for(r=n;r--!==0;)if(!af(t[r],e[r]))return!1;return!0}if(o=Object.keys(t),n=o.length,n!==Object.keys(e).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(e,o[r]))return!1;for(r=n;r--!==0;){let i=o[r];if(!(i==="_owner"&&t.$$typeof)&&!af(t[i],e[i]))return!1}return!0}return t!==t&&e!==e}function S1(t){return typeof window=="undefined"?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function v1(t,e){let n=S1(t);return Math.round(e*n)/n}function E1(t){let e=ze.useRef(t);return sf(()=>{e.current=t}),e}function C1(t){t===void 0&&(t={});let{placement:e="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:i,floating:s}={},transform:a=!0,whileElementsMounted:l,open:u}=t,[d,f]=ze.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[m,c]=ze.useState(r);af(m,r)||c(r);let[y,v]=ze.useState(null),[S,h]=ze.useState(null),p=ze.useCallback(P=>{P!=O.current&&(O.current=P,v(P))},[v]),E=ze.useCallback(P=>{P!==_.current&&(_.current=P,h(P))},[h]),D=i||y,x=s||S,O=ze.useRef(null),_=ze.useRef(null),k=ze.useRef(d),L=E1(l),N=E1(o),ee=ze.useCallback(()=>{if(!O.current||!_.current)return;let P={placement:e,strategy:n,middleware:m};N.current&&(P.platform=N.current),of(O.current,_.current,P).then(Y=>{let Z={...Y,isPositioned:!0};oe.current&&!af(k.current,Z)&&(k.current=Z,w1.flushSync(()=>{f(Z)}))})},[m,e,n,N]);sf(()=>{u===!1&&k.current.isPositioned&&(k.current.isPositioned=!1,f(P=>({...P,isPositioned:!1})))},[u]);let oe=ze.useRef(!1);sf(()=>(oe.current=!0,()=>{oe.current=!1}),[]),sf(()=>{if(D&&(O.current=D),x&&(_.current=x),D&&x){if(L.current)return L.current(D,x,ee);ee()}},[D,x,ee,L]);let b=ze.useMemo(()=>({reference:O,floating:_,setReference:p,setFloating:E}),[p,E]),$=ze.useMemo(()=>({reference:D,floating:x}),[D,x]),T=ze.useMemo(()=>{let P={position:n,left:0,top:0};if(!$.floating)return P;let Y=v1($.floating,d.x),Z=v1($.floating,d.y);return a?{...P,transform:"translate("+Y+"px, "+Z+"px)",...S1($.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:Y,top:Z}},[n,a,$.floating,d.x,d.y]);return ze.useMemo(()=>({...d,update:ee,refs:b,elements:$,floatingStyles:T}),[d,ee,b,$,T])}var k1=B(qc(),1);var _2=re["useInsertionEffect".toString()],k2=_2||(t=>t());function ss(t){let e=re.useRef(()=>{});return k2(()=>{e.current=t}),re.useCallback(function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return e.current==null?void 0:e.current(...r)},[])}var O2="ArrowUp",b2="ArrowDown",R2="ArrowLeft",N2="ArrowRight";var O1=typeof document!="undefined"?ll.useLayoutEffect:ll.useEffect;var M2=[R2,N2],I2=[O2,b2],IM=[...M2,...I2];var Th=!1,A2=0,x1=()=>"floating-ui-"+A2++;function L2(){let[t,e]=re.useState(()=>Th?x1():void 0);return O1(()=>{t==null&&e(x1())},[]),re.useEffect(()=>{Th||(Th=!0)},[]),t}var P2=re["useId".toString()],b1=P2||L2;function B2(){let t=new Map;return{emit(e,n){var r;(r=t.get(e))==null||r.forEach(o=>o(n))},on(e,n){t.set(e,[...t.get(e)||[],n])},off(e,n){var r;t.set(e,((r=t.get(e))==null?void 0:r.filter(o=>o!==n))||[])}}}var V2=re.createContext(null),W2=re.createContext(null),R1=()=>{var t;return((t=re.useContext(V2))==null?void 0:t.id)||null},N1=()=>re.useContext(W2);function H2(t){return"data-floating-ui-"+t}function Fh(t,e){let n=t.filter(o=>{var i;return o.parentId===e&&((i=o.context)==null?void 0:i.open)}),r=n;for(;r.length;)r=t.filter(o=>{var i;return(i=r)==null?void 0:i.some(s=>{var a;return o.parentId===s.id&&((a=o.context)==null?void 0:a.open)})}),n=n.concat(r);return n}var z2={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},U2={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},T1=t=>{var e,n;return{escapeKey:typeof t=="boolean"?t:(e=t==null?void 0:t.escapeKey)!=null?e:!1,outsidePress:typeof t=="boolean"?t:(n=t==null?void 0:t.outsidePress)!=null?n:!0}};function as(t,e){e===void 0&&(e={});let{open:n,onOpenChange:r,nodeId:o,elements:{reference:i,domReference:s,floating:a},dataRef:l}=t,{enabled:u=!0,escapeKey:d=!0,outsidePress:f=!0,outsidePressEvent:m="pointerdown",referencePress:c=!1,referencePressEvent:y="pointerdown",ancestorScroll:v=!1,bubbles:S,capture:h}=e,p=N1(),E=ss(typeof f=="function"?f:()=>!1),D=typeof f=="function"?E:f,x=re.useRef(!1),O=re.useRef(!1),{escapeKey:_,outsidePress:k}=T1(S),{escapeKey:L,outsidePress:N}=T1(h),ee=ss(T=>{if(!n||!u||!d||T.key!=="Escape")return;let P=p?Fh(p.nodesRef.current,o):[];if(!_&&(T.stopPropagation(),P.length>0)){let Y=!0;if(P.forEach(Z=>{var Ue;if((Ue=Z.context)!=null&&Ue.open&&!Z.context.dataRef.current.__escapeKeyBubbles){Y=!1;return}}),!Y)return}r(!1,Jw(T)?T.nativeEvent:T,"escape-key")}),oe=ss(T=>{var P;let Y=()=>{var Z;ee(T),(Z=rs(T))==null||Z.removeEventListener("keydown",Y)};(P=rs(T))==null||P.addEventListener("keydown",Y)}),b=ss(T=>{let P=x.current;x.current=!1;let Y=O.current;if(O.current=!1,m==="click"&&Y||P||typeof D=="function"&&!D(T))return;let Z=rs(T),Ue="["+H2("inert")+"]",Cr=Dh(a).querySelectorAll(Ue),bn=Pe(Z)?Z:null;for(;bn&&!no(bn);){let Te=pr(bn);if(no(Te)||!Pe(Te))break;bn=Te}if(Cr.length&&Pe(Z)&&!Xw(Z)&&!gh(Z,a)&&Array.from(Cr).every(Te=>!gh(bn,Te)))return;if(kt(Z)&&a){let Te=Z.clientWidth>0&&Z.scrollWidth>Z.clientWidth,an=Z.clientHeight>0&&Z.scrollHeight>Z.clientHeight,ao=an&&T.offsetX>Z.clientWidth;if(an&&vt(Z).direction==="rtl"&&(ao=T.offsetX<=Z.offsetWidth-Z.clientWidth),ao||Te&&T.offsetY>Z.clientHeight)return}let so=p&&Fh(p.nodesRef.current,o).some(Te=>{var an;return tf(T,(an=Te.context)==null?void 0:an.elements.floating)});if(tf(T,a)||tf(T,s)||so)return;let tt=p?Fh(p.nodesRef.current,o):[];if(tt.length>0){let Te=!0;if(tt.forEach(an=>{var ao;if((ao=an.context)!=null&&ao.open&&!an.context.dataRef.current.__outsidePressBubbles){Te=!1;return}}),!Te)return}r(!1,T,"outside-press")}),$=ss(T=>{var P;let Y=()=>{var Z;b(T),(Z=rs(T))==null||Z.removeEventListener(m,Y)};(P=rs(T))==null||P.addEventListener(m,Y)});return re.useEffect(()=>{if(!n||!u)return;l.current.__escapeKeyBubbles=_,l.current.__outsidePressBubbles=k;function T(Z){r(!1,Z,"ancestor-scroll")}let P=Dh(a);d&&P.addEventListener("keydown",L?oe:ee,L),D&&P.addEventListener(m,N?$:b,N);let Y=[];return v&&(Pe(s)&&(Y=Ht(s)),Pe(a)&&(Y=Y.concat(Ht(a))),!Pe(i)&&i&&i.contextElement&&(Y=Y.concat(Ht(i.contextElement)))),Y=Y.filter(Z=>{var Ue;return Z!==((Ue=P.defaultView)==null?void 0:Ue.visualViewport)}),Y.forEach(Z=>{Z.addEventListener("scroll",T,{passive:!0})}),()=>{d&&P.removeEventListener("keydown",L?oe:ee,L),D&&P.removeEventListener(m,N?$:b,N),Y.forEach(Z=>{Z.removeEventListener("scroll",T)})}},[l,a,s,i,d,D,m,n,r,v,u,_,k,ee,L,oe,b,N,$]),re.useEffect(()=>{x.current=!1},[D,m]),re.useMemo(()=>u?{reference:{onKeyDown:ee,[z2[y]]:T=>{c&&r(!1,T.nativeEvent,"reference-press")}},floating:{onKeyDown:ee,onMouseDown(){O.current=!0},onMouseUp(){O.current=!0},[U2[m]]:()=>{x.current=!0}}}:{},[u,c,m,y,r,ee])}function ls(t){var e;t===void 0&&(t={});let{open:n=!1,onOpenChange:r,nodeId:o}=t;if(!1){var i;if((i=t.elements)!=null&&i.reference&&!Pe(t.elements.reference)){var s;if(!((s=devMessageSet)!=null&&s.has(k)))var a}}let[l,u]=re.useState(null),d=((e=t.elements)==null?void 0:e.reference)||l,f=C1(t),m=N1(),c=R1()!=null,y=ss((k,L,N)=>{k&&(S.current.openEvent=L),h.emit("openchange",{open:k,event:L,reason:N,nested:c}),r==null||r(k,L,N)}),v=re.useRef(null),S=re.useRef({}),h=re.useState(()=>B2())[0],p=b1(),E=re.useCallback(k=>{let L=Pe(k)?{getBoundingClientRect:()=>k.getBoundingClientRect(),contextElement:k}:k;f.refs.setReference(L)},[f.refs]),D=re.useCallback(k=>{(Pe(k)||k===null)&&(v.current=k,u(k)),(Pe(f.refs.reference.current)||f.refs.reference.current===null||k!==null&&!Pe(k))&&f.refs.setReference(k)},[f.refs]),x=re.useMemo(()=>({...f.refs,setReference:D,setPositionReference:E,domReference:v}),[f.refs,D,E]),O=re.useMemo(()=>({...f.elements,domReference:d}),[f.elements,d]),_=re.useMemo(()=>({...f,refs:x,elements:O,dataRef:S,nodeId:o,floatingId:p,events:h,open:n,onOpenChange:y}),[f,o,p,h,n,y,x,O]);return O1(()=>{let k=m==null?void 0:m.nodesRef.current.find(L=>L.id===o);k&&(k.context=_)}),re.useMemo(()=>({...f,context:_,refs:x,elements:O}),[f,x,O,_])}var F1="active",_1="selected";function _h(t,e,n){let r=new Map,o=n==="item",i=t;if(o&&t){let{[F1]:s,[_1]:a,...l}=t;i=l}return{...n==="floating"&&{tabIndex:-1},...i,...e.map(s=>{let a=s?s[n]:null;return typeof a=="function"?t?a(t):null:a}).concat(t).reduce((s,a)=>(a&&Object.entries(a).forEach(l=>{let[u,d]=l;if(!(o&&[F1,_1].includes(u)))if(u.indexOf("on")===0){if(r.has(u)||r.set(u,[]),typeof d=="function"){var f;(f=r.get(u))==null||f.push(d),s[u]=function(){for(var m,c=arguments.length,y=new Array(c),v=0;v<c;v++)y[v]=arguments[v];return(m=r.get(u))==null?void 0:m.map(S=>S(...y)).find(S=>S!==void 0)}}}else s[u]=d}),s),{})}}function us(t){t===void 0&&(t=[]);let e=t,n=re.useCallback(i=>_h(i,t,"reference"),e),r=re.useCallback(i=>_h(i,t,"floating"),e),o=re.useCallback(i=>_h(i,t,"item"),t.map(i=>i==null?void 0:i.item));return re.useMemo(()=>({getReferenceProps:n,getFloatingProps:r,getItemProps:o}),[n,r,o])}var j2=new Map([["select","listbox"],["combobox","listbox"],["label",!1]]);function M1(t,e){var n;e===void 0&&(e={});let{open:r,floatingId:o}=t,{enabled:i=!0,role:s="dialog"}=e,a=(n=j2.get(s))!=null?n:s,l=b1(),d=R1()!=null;return re.useMemo(()=>{if(!i)return{};let f={id:o,...a&&{role:a}};return a==="tooltip"||s==="label"?{reference:{["aria-"+(s==="label"?"labelledby":"describedby")]:r?o:void 0},floating:f}:{reference:{"aria-expanded":r?"true":"false","aria-haspopup":a==="alertdialog"?"dialog":a,"aria-controls":r?o:void 0,...a==="listbox"&&{role:"combobox"},...a==="menu"&&{id:l},...a==="menu"&&d&&{role:"menuitem"},...s==="select"&&{"aria-autocomplete":"none"},...s==="combobox"&&{"aria-autocomplete":"list"}},floating:{...f,...a==="menu"&&{"aria-labelledby":l}},item(m){let{active:c,selected:y}=m,v={role:"option",...c&&{id:o+"-option"}};switch(s){case"select":return{...v,"aria-selected":c&&y};case"combobox":return{...v,...c&&{"aria-selected":!0}}}return{}}}},[i,s,a,r,o,l,d])}function V(){return V=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t},V.apply(this,arguments)}var cs=255,kh=360,_n=100,fs=t=>{var{r:e,g:n,b:r,a:o}=t,i=Math.max(e,n,r),s=i-Math.min(e,n,r),a=s?i===e?(n-r)/s:i===n?2+(r-e)/s:4+(e-n)/s:0;return{h:60*(a<0?a+6:a),s:i?s/i*_n:0,v:i/cs*_n,a:o}};var uf=t=>{var{h:e,s:n,l:r,a:o}=cf(t);return"hsla("+e+", "+n+"%, "+r+"%, "+o+")"};var ul=t=>{var{h:e,s:n,l:r,a:o}=t;return n*=(r<50?r:_n-r)/_n,{h:e,s:n>0?2*n/(r+n)*_n:0,v:r+n,a:o}},cf=t=>{var{h:e,s:n,v:r,a:o}=t,i=(200-n)*r/_n;return{h:e,s:i>0&&i<200?n*r/_n/(i<=_n?i:200-i)*_n:0,l:i/2,a:o}};var VM={grad:kh/400,turn:kh,rad:kh/(Math.PI*2)};var I1=t=>{var{r:e,g:n,b:r}=t,o=e<<16|n<<8|r;return"#"+(i=>new Array(7-i.length).join("0")+i)(o.toString(16))},A1=t=>{var{r:e,g:n,b:r,a:o}=t,i=typeof o=="number"&&(o*255|1<<8).toString(16).slice(1);return""+I1({r:e,g:n,b:r,a:o})+(i||"")},wr=t=>fs($2(t)),$2=t=>{var e=t.replace("#","");/^#?/.test(t)&&e.length===3&&(t="#"+e.charAt(0)+e.charAt(0)+e.charAt(1)+e.charAt(1)+e.charAt(2)+e.charAt(2));var n=new RegExp("[A-Za-z0-9]{2}","g"),[r,o,i=0,s]=t.match(n).map(a=>parseInt(a,16));return{r,g:o,b:i,a:(s!=null?s:255)/cs}},ds=t=>{var{h:e,s:n,v:r,a:o}=t,i=e/60,s=n/_n,a=r/_n,l=Math.floor(i)%6,u=i-Math.floor(i),d=cs*a*(1-s),f=cs*a*(1-s*u),m=cs*a*(1-s*(1-u));a*=cs;var c={};switch(l){case 0:c.r=a,c.g=m,c.b=d;break;case 1:c.r=f,c.g=a,c.b=d;break;case 2:c.r=d,c.g=a,c.b=m;break;case 3:c.r=d,c.g=f,c.b=a;break;case 4:c.r=m,c.g=d,c.b=a;break;case 5:c.r=a,c.g=d,c.b=f;break}return c.r=Math.round(c.r),c.g=Math.round(c.g),c.b=Math.round(c.b),V({},c,{a:o})};var L1=t=>{var{r:e,g:n,b:r,a:o}=ds(t);return"rgba("+e+", "+n+", "+r+", "+o+")"},G2=t=>{var{r:e,g:n,b:r}=t;return{r:e,g:n,b:r}},Y2=t=>{var{h:e,s:n,l:r}=t;return{h:e,s:n,l:r}},cl=t=>I1(ds(t)),P1=t=>A1(ds(t)),Z2=t=>{var{h:e,s:n,v:r}=t;return{h:e,s:n,v:r}},dt=t=>{var e,n,r,o,i,s,a,l;return typeof t=="string"&&fl(t)?(s=wr(t),a=t):typeof t!="string"&&(s=t),s&&(r=Z2(s),i=cf(s),o=ds(s),l=A1(o),a=cl(s),n=Y2(i),e=G2(o)),{rgb:e,hsl:n,hsv:r,rgba:o,hsla:i,hsva:s,hex:a,hexa:l}};var fl=t=>/^#?([A-Fa-f0-9]{3,4}){1,2}$/.test(t);function Se(t,e){if(t==null)return{};var n={},r=Object.keys(t),o,i;for(i=0;i<r.length;i++)o=r[i],!(e.indexOf(o)>=0)&&(n[o]=t[o]);return n}var z1=B(se());var Ot=B(se());var ms=B(se());function Oh(t){var e=(0,ms.useRef)(t);return(0,ms.useEffect)(()=>{e.current=t}),(0,ms.useCallback)((n,r)=>e.current&&e.current(n,r),[])}var ps=t=>"touches"in t,bh=t=>{!ps(t)&&t.preventDefault&&t.preventDefault()},B1=function(e,n,r){return n===void 0&&(n=0),r===void 0&&(r=1),e>r?r:e<n?n:e},Rh=(t,e)=>{var n=t.getBoundingClientRect(),r=ps(e)?e.touches[0]:e;return{left:B1((r.pageX-(n.left+window.pageXOffset))/n.width),top:B1((r.pageY-(n.top+window.pageYOffset))/n.height),width:n.width,height:n.height,x:r.pageX-(n.left+window.pageXOffset),y:r.pageY-(n.top+window.pageYOffset)}};var V1=B(X()),q2=["prefixCls","className","onMove","onDown"],W1=Ot.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-interactive",className:r,onMove:o,onDown:i}=t,s=Se(t,q2),a=(0,Ot.useRef)(null),l=(0,Ot.useRef)(!1),[u,d]=(0,Ot.useState)(!1),f=Oh(o),m=Oh(i),c=p=>l.current&&!ps(p)?!1:(l.current=ps(p),!0),y=(0,Ot.useCallback)(p=>{bh(p);var E=ps(p)?p.touches.length>0:p.buttons>0;E&&a.current?f&&f(Rh(a.current,p),p):d(!1)},[f]),v=(0,Ot.useCallback)(()=>d(!1),[]),S=(0,Ot.useCallback)(p=>{var E=p?window.addEventListener:window.removeEventListener;E(l.current?"touchmove":"mousemove",y),E(l.current?"touchend":"mouseup",v)},[]);(0,Ot.useEffect)(()=>(S(u),()=>{u&&S(!1)}),[u,S]);var h=(0,Ot.useCallback)(p=>{bh(p.nativeEvent),c(p.nativeEvent)&&(m&&m(Rh(a.current,p.nativeEvent),p.nativeEvent),d(!0))},[m]);return(0,V1.jsx)("div",V({},s,{className:[n,r||""].filter(Boolean).join(" "),style:V({},s.style,{touchAction:"none"}),ref:a,tabIndex:0,onMouseDown:h,onTouchStart:h}))});W1.displayName="Interactive";var ff=W1;var KM=B(se()),Nh=B(X()),K2=["className","prefixCls","left","top","style","fillProps"],H1=t=>{var{className:e,prefixCls:n,left:r,top:o,style:i,fillProps:s}=t,a=Se(t,K2),l=V({},i,{position:"absolute",left:r,top:o}),u=V({width:18,height:18,boxShadow:"var(--alpha-pointer-box-shadow)",borderRadius:"50%",backgroundColor:"var(--alpha-pointer-background-color)"},s==null?void 0:s.style,{transform:r?"translate(-9px, -1px)":"translate(-1px, -9px)"});return(0,Nh.jsx)("div",V({className:n+"-pointer "+(e||""),style:l},a,{children:(0,Nh.jsx)("div",V({className:n+"-fill"},s,{style:u}))}))};var df=B(X()),U1=B(X()),Q2=["prefixCls","className","hsva","background","bgProps","innerProps","pointerProps","radius","width","height","direction","style","onChange","pointer"],J2="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAMUlEQVQ4T2NkYGAQYcAP3uCTZhw1gGGYhAGBZIA/nYDCgBDAm9BGDWAAJyRCgLaBCAAgXwixzAS0pgAAAABJRU5ErkJggg==",j1=z1.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-alpha",className:r,hsva:o,background:i,bgProps:s={},innerProps:a={},pointerProps:l={},radius:u=0,width:d,height:f=16,direction:m="horizontal",style:c,onChange:y,pointer:v}=t,S=Se(t,Q2),h=_=>{y&&y(V({},o,{a:m==="horizontal"?_.left:_.top}),_)},p=uf(Object.assign({},o,{a:1})),E="linear-gradient(to "+(m==="horizontal"?"right":"bottom")+", rgba(244, 67, 54, 0) 0%, "+p+" 100%)",D={};m==="horizontal"?D.left=o.a*100+"%":D.top=o.a*100+"%";var x=V({"--alpha-background-color":"#fff","--alpha-pointer-background-color":"rgb(248, 248, 248)","--alpha-pointer-box-shadow":"rgb(0 0 0 / 37%) 0px 1px 4px 0px",borderRadius:u,background:"url("+J2+") left center",backgroundColor:"var(--alpha-background-color)"},{width:d,height:f},c,{position:"relative"}),O=v&&typeof v=="function"?v(V({prefixCls:n},l,D)):(0,df.jsx)(H1,V({},l,{prefixCls:n},D));return(0,U1.jsxs)("div",V({},S,{className:[n,n+"-"+m,r||""].filter(Boolean).join(" "),style:x,ref:e,children:[(0,df.jsx)("div",V({},s,{style:V({inset:0,position:"absolute",background:i||E,borderRadius:u},s.style)})),(0,df.jsx)(ff,V({},a,{style:V({},a.style,{inset:0,zIndex:1,position:"absolute"}),onMove:h,onDown:h,children:O}))]}))});j1.displayName="Alpha";var dl=j1;var $1=B(se()),hs=B(se()),Mh=B(X()),G1=B(X()),X2=["prefixCls","placement","label","value","className","style","labelStyle","inputStyle","onChange","onBlur"],eO=t=>/^#?([A-Fa-f0-9]{3,4}){1,2}$/.test(t),tO=t=>Number(String(t).replace(/%/g,"")),Y1=$1.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-editable-input",placement:r="bottom",label:o,value:i,className:s,style:a,labelStyle:l,inputStyle:u,onChange:d,onBlur:f}=t,m=Se(t,X2),[c,y]=(0,hs.useState)(i),v=(0,hs.useRef)(!1);(0,hs.useEffect)(()=>{t.value!==c&&(v.current||y(t.value))},[t.value]);function S(x,O){var _=(O||x.target.value).trim().replace(/^#/,"");eO(_)&&d&&d(x,_);var k=tO(_);isNaN(k)||d&&d(x,k),y(_)}function h(x){v.current=!1,y(t.value),f&&f(x)}var p={};r==="bottom"&&(p.flexDirection="column"),r==="top"&&(p.flexDirection="column-reverse"),r==="left"&&(p.flexDirection="row-reverse");var E=V({"--editable-input-label-color":"rgb(153, 153, 153)","--editable-input-box-shadow":"rgb(204 204 204) 0px 0px 0px 1px inset","--editable-input-color":"#666",position:"relative",alignItems:"center",display:"flex",fontSize:11},p,a),D=V({width:"100%",paddingTop:2,paddingBottom:2,paddingLeft:3,paddingRight:3,fontSize:11,background:"transparent",boxSizing:"border-box",border:"none",color:"var(--editable-input-color)",boxShadow:"var(--editable-input-box-shadow)"},u);return(0,G1.jsxs)("div",{className:[n,s||""].filter(Boolean).join(" "),style:E,children:[(0,Mh.jsx)("input",V({ref:e,value:c,onChange:S,onBlur:h,autoComplete:"off",onFocus:()=>v.current=!0},m,{style:D})),o&&(0,Mh.jsx)("span",{style:V({color:"var(--editable-input-label-color)",textTransform:"capitalize"},l),children:o})]})});Y1.displayName="EditableInput";var Ho=Y1;var Z1=B(se());var ml=B(X()),q1=B(X()),nO=["prefixCls","hsva","placement","rProps","gProps","bProps","aProps","className","style","onChange"],K1=Z1.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-editable-input-rgba",hsva:r,placement:o="bottom",rProps:i={},gProps:s={},bProps:a={},aProps:l={},className:u,style:d,onChange:f}=t,m=Se(t,nO),c=r?ds(r):{};function y(S){var h=Number(S.target.value);h&&h>255&&(S.target.value="255"),h&&h<0&&(S.target.value="0")}var v=(S,h,p)=>{typeof S=="number"&&(h==="a"&&(S<0&&(S=0),S>100&&(S=100),f&&f(dt(fs(V({},c,{a:S/100}))))),S>255&&(S=255,p.target.value="255"),S<0&&(S=0,p.target.value="0"),h==="r"&&f&&f(dt(fs(V({},c,{r:S})))),h==="g"&&f&&f(dt(fs(V({},c,{g:S})))),h==="b"&&f&&f(dt(fs(V({},c,{b:S})))))};return(0,q1.jsxs)("div",V({ref:e,className:[n,u||""].filter(Boolean).join(" ")},m,{style:V({fontSize:11,display:"flex"},d),children:[(0,ml.jsx)(Ho,V({label:"R",value:c.r||0,onBlur:y,placement:o,onChange:(S,h)=>v(h,"r",S)},i,{style:V({},i.style)})),(0,ml.jsx)(Ho,V({label:"G",value:c.g||0,onBlur:y,placement:o,onChange:(S,h)=>v(h,"g",S)},s,{style:V({marginLeft:5},i.style)})),(0,ml.jsx)(Ho,V({label:"B",value:c.b||0,onBlur:y,placement:o,onChange:(S,h)=>v(h,"b",S)},a,{style:V({marginLeft:5},a.style)})),l&&(0,ml.jsx)(Ho,V({label:"A",value:c.a?parseInt(String(c.a*100),10):0,onBlur:y,placement:o,onChange:(S,h)=>v(h,"a",S)},l,{style:V({marginLeft:5},l.style)}))]}))});K1.displayName="EditableInputRGBA";var mf=K1;var io=B(se());var Ih=B(X()),Q1=B(X()),rO=["prefixCls","className","color","colors","style","rectProps","onChange","addonAfter","addonBefore","rectRender"],J1=io.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-swatch",className:r,color:o,colors:i=[],style:s,rectProps:a={},onChange:l,addonAfter:u,addonBefore:d,rectRender:f}=t,m=Se(t,rO),c=V({"--swatch-background-color":"rgb(144, 19, 254)",background:"var(--swatch-background-color)",height:15,width:15,marginRight:5,marginBottom:5,cursor:"pointer",position:"relative",outline:"none",borderRadius:2},a.style),y=(v,S)=>{l&&l(wr(v),dt(wr(v)),S)};return(0,Q1.jsxs)("div",V({ref:e},m,{className:[n,r||""].filter(Boolean).join(" "),style:V({display:"flex",flexWrap:"wrap",position:"relative"},s),children:[d&&io.default.isValidElement(d)&&d,i&&Array.isArray(i)&&i.map((v,S)=>{var h="",p="";typeof v=="string"&&(h=v,p=v),typeof v=="object"&&v.color&&(h=v.title||v.color,p=v.color);var E=o&&o.toLocaleLowerCase()===p.toLocaleLowerCase(),D=f&&f({title:h,color:p,checked:!!E,style:V({},c,{background:p}),onClick:O=>y(p,O)});if(D)return(0,Ih.jsx)(io.Fragment,{children:D},S);var x=a.children&&io.default.isValidElement(a.children)?io.default.cloneElement(a.children,{color:p,checked:E}):null;return(0,Ih.jsx)("div",V({tabIndex:0,title:h,onClick:O=>y(p,O)},a,{children:x,style:V({},c,{background:p})}),S)}),u&&io.default.isValidElement(u)&&u]}))});J1.displayName="Swatch";var X1=J1;var gs=B(se());function Ah(t){if(t==null)throw new TypeError("Cannot destructure "+t)}var pf=B(se());var Lh=B(se()),eS=B(se()),tS=B(X()),oO={marginRight:0,marginBottom:0,borderRadius:0,boxSizing:"border-box",height:25,width:25};function Ph(t){var{style:e,title:n,checked:r,color:o,onClick:i,rectProps:s}=t,a=(0,eS.useRef)(null),l=(0,Lh.useCallback)(()=>{a.current.style.zIndex="2",a.current.style.outline="#fff solid 2px",a.current.style.boxShadow="rgb(0 0 0 / 25%) 0 0 5px 2px"},[]),u=(0,Lh.useCallback)(()=>{r||(a.current.style.zIndex="0",a.current.style.outline="initial",a.current.style.boxShadow="initial")},[r]),d=r?{zIndex:1,outline:"#fff solid 2px",boxShadow:"rgb(0 0 0 / 25%) 0 0 5px 2px"}:{zIndex:0};return(0,tS.jsx)("div",V({ref:a,title:n},s,{onClick:i,onMouseEnter:l,onMouseLeave:u,style:V({},e,{marginRight:0,marginBottom:0,borderRadius:0,boxSizing:"border-box",height:25,width:25},oO,d,s==null?void 0:s.style)}))}var pl=B(X()),nS=B(X()),iO=["prefixCls","placement","className","style","color","colors","rectProps","onChange","rectRender"],sO=["#B80000","#DB3E00","#FCCB00","#008B02","#006B76","#1273DE","#004DCF","#5300EB","#EB9694","#FAD0C3","#FEF3BD","#C1E1C5","#BEDADC","#C4DEF6","#BED3F3","#D4C4FB"],Be=function(t){return t.Left="L",t.LeftTop="LT",t.LeftBottom="LB",t.Right="R",t.RightTop="RT",t.RightBottom="RB",t.Top="T",t.TopRight="TR",t.TopLeft="TL",t.Bottom="B",t.BottomLeft="BL",t.BottomRight="BR",t}({}),rS=pf.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-github",placement:r=Be.TopRight,className:o,style:i,color:s,colors:a=sO,rectProps:l={},onChange:u,rectRender:d}=t,f=Se(t,iO),m=typeof s=="string"&&fl(s)?wr(s):s,c=s?cl(m):"",y=D=>u&&u(dt(D)),v=V({"--github-border":"1px solid rgba(0, 0, 0, 0.2)","--github-background-color":"#fff","--github-box-shadow":"rgb(0 0 0 / 15%) 0px 3px 12px","--github-arrow-border-color":"rgba(0, 0, 0, 0.15)",width:200,borderRadius:4,background:"var(--github-background-color)",boxShadow:"var(--github-box-shadow)",border:"var(--github-border)",position:"relative",padding:5},i),S={borderStyle:"solid",position:"absolute"},h=V({},S),p=V({},S);/^T/.test(r)&&(h.borderWidth="0 8px 8px",h.borderColor="transparent transparent var(--github-arrow-border-color)",p.borderWidth="0 7px 7px",p.borderColor="transparent transparent var(--github-background-color)"),r===Be.TopRight&&(h.top=-8,p.top=-7),r===Be.Top&&(h.top=-8,p.top=-7),r===Be.TopLeft&&(h.top=-8,p.top=-7),/^B/.test(r)&&(h.borderWidth="8px 8px 0",h.borderColor="var(--github-arrow-border-color) transparent transparent",p.borderWidth="7px 7px 0",p.borderColor="var(--github-background-color) transparent transparent",r===Be.BottomRight&&(h.top="100%",p.top="100%"),r===Be.Bottom&&(h.top="100%",p.top="100%"),r===Be.BottomLeft&&(h.top="100%",p.top="100%")),/^(B|T)/.test(r)&&((r===Be.Top||r===Be.Bottom)&&(h.left="50%",h.marginLeft=-8,p.left="50%",p.marginLeft=-7),(r===Be.TopRight||r===Be.BottomRight)&&(h.right=10,p.right=11),(r===Be.TopLeft||r===Be.BottomLeft)&&(h.left=7,p.left=8)),/^L/.test(r)&&(h.borderWidth="8px 8px 8px 0",h.borderColor="transparent var(--github-arrow-border-color) transparent transparent",p.borderWidth="7px 7px 7px 0",p.borderColor="transparent var(--github-background-color) transparent transparent",h.left=-8,p.left=-7),/^R/.test(r)&&(h.borderWidth="8px 0 8px 8px",h.borderColor="transparent transparent transparent var(--github-arrow-border-color)",p.borderWidth="7px 0 7px 7px",p.borderColor="transparent transparent transparent var(--github-background-color)",h.right=-8,p.right=-7),/^(L|R)/.test(r)&&((r===Be.RightTop||r===Be.LeftTop)&&(h.top=5,p.top=6),(r===Be.Left||r===Be.Right)&&(h.top="50%",p.top="50%",h.marginTop=-8,p.marginTop=-7),(r===Be.LeftBottom||r===Be.RightBottom)&&(h.top="100%",p.top="100%",h.marginTop=-21,p.marginTop=-20));var E=D=>{var x=V({},(Ah(D),D)),O=d&&d(V({},x));return O||(0,pl.jsx)(Ph,V({},x,{rectProps:l}))};return(0,pl.jsx)(X1,V({ref:e,className:[n,o].filter(Boolean).join(" "),colors:a,color:c,rectRender:E},f,{onChange:y,style:v,rectProps:{style:{marginRight:0,marginBottom:0,borderRadius:0,height:25,width:25}},addonBefore:(0,nS.jsxs)(pf.Fragment,{children:[(0,pl.jsx)("div",{style:h}),(0,pl.jsx)("div",{style:p})]})}))});rS.displayName="Github";var oS=rS;var hf=B(se());var OI=B(se()),iS=B(se()),Bh=B(X()),sS=t=>{var{className:e,color:n,left:r,top:o,prefixCls:i}=t,s={position:"absolute",top:o,left:r},a={"--saturation-pointer-box-shadow":"rgb(255 255 255) 0px 0px 0px 1.5px, rgb(0 0 0 / 30%) 0px 0px 1px 1px inset, rgb(0 0 0 / 40%) 0px 0px 1px 2px",width:6,height:6,transform:"translate(-3px, -3px)",boxShadow:"var(--saturation-pointer-box-shadow)",borderRadius:"50%",backgroundColor:n};return(0,iS.useMemo)(()=>(0,Bh.jsx)("div",{className:i+"-pointer "+(e||""),style:s,children:(0,Bh.jsx)("div",{className:i+"-fill",style:a})}),[o,r,n,e,i])};var Vh=B(X()),aO=["prefixCls","radius","pointer","className","hue","style","hsva","onChange"],aS=hf.default.forwardRef((t,e)=>{var n,{prefixCls:r="w-color-saturation",radius:o=0,pointer:i,className:s,hue:a=0,style:l,hsva:u,onChange:d}=t,f=Se(t,aO),m=V({width:200,height:200,borderRadius:o},l,{position:"relative"}),c=(v,S)=>{d&&u&&d({h:u.h,s:v.left*100,v:(1-v.top)*100,a:u.a})},y=(0,hf.useMemo)(()=>{if(!u)return null;var v={top:100-u.v+"%",left:u.s+"%",color:uf(u)};return i&&typeof i=="function"?i(V({prefixCls:r},v)):(0,Vh.jsx)(sS,V({prefixCls:r},v))},[u,i,r]);return(0,Vh.jsx)(ff,V({className:[r,s||""].filter(Boolean).join(" ")},f,{style:V({position:"absolute",inset:0,cursor:"crosshair",backgroundImage:"linear-gradient(0deg, #000, transparent), linear-gradient(90deg, #fff, hsl("+((n=u==null?void 0:u.h)!=null?n:a)+", 100%, 50%))"},m),ref:e,onMove:c,onDown:c,children:y}))});aS.displayName="Saturation";var lS=aS;var uS=B(se());var cS=B(X()),lO=["prefixCls","className","hue","onChange","direction"],fS=uS.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-hue",className:r,hue:o=0,onChange:i,direction:s="horizontal"}=t,a=Se(t,lO);return(0,cS.jsx)(dl,V({ref:e,className:n+" "+(r||"")},a,{direction:s,background:"linear-gradient(to "+(s==="horizontal"?"right":"bottom")+", rgb(255, 0, 0) 0%, rgb(255, 255, 0) 17%, rgb(0, 255, 0) 33%, rgb(0, 255, 255) 50%, rgb(0, 0, 255) 67%, rgb(255, 0, 255) 83%, rgb(255, 0, 0) 100%)",hsva:{h:o,s:100,v:100,a:o/360},onChange:(l,u)=>{i&&i({h:s==="horizontal"?360*u.left:360*u.top})}}))});fS.displayName="Hue";var dS=fS;var mS=B(se());var pS=B(X()),uO=["prefixCls","hsva","hProps","sProps","lProps","aProps","className","onChange"],hS=mS.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-editable-input-hsla",hsva:r,hProps:o={},sProps:i={},lProps:s={},aProps:a={},className:l,onChange:u}=t,d=Se(t,uO),f=r?cf(r):{h:0,s:0,l:0,a:0},m=(c,y,v)=>{typeof c=="number"&&(y==="h"&&(c<0&&(c=0),c>360&&(c=360),u&&u(dt(ul(V({},f,{h:c}))))),y==="s"&&(c<0&&(c=0),c>100&&(c=100),u&&u(dt(ul(V({},f,{s:c}))))),y==="l"&&(c<0&&(c=0),c>100&&(c=100),u&&u(dt(ul(V({},f,{l:c}))))),y==="a"&&(c<0&&(c=0),c>1&&(c=1),u&&u(dt(ul(V({},f,{a:c}))))))};return(0,pS.jsx)(mf,V({ref:e,hsva:r,rProps:V({label:"H",value:Math.round(f.h)},o,{onChange:(c,y)=>m(y,"h",c)}),gProps:V({label:"S",value:Math.round(f.s)+"%"},i,{onChange:(c,y)=>m(y,"s",c)}),bProps:V({label:"L",value:Math.round(f.l)+"%"},s,{onChange:(c,y)=>m(y,"l",c)}),aProps:V({label:"A",value:Math.round(f.a*100)/100},a,{onChange:(c,y)=>m(y,"a",c)}),className:[n,l||""].filter(Boolean).join(" ")},d))});hS.displayName="EditableInputHSLA";var yS=hS;var gS=B(se());var hl=B(se()),yf=B(X()),cO=["style"];function Wh(t){var{style:e}=t,n=Se(t,cO),r=(0,hl.useRef)(null),o=(0,hl.useCallback)(()=>{r.current.style.backgroundColor="var(--chrome-arrow-background-color)"},[]),i=(0,hl.useCallback)(()=>{r.current.style.backgroundColor="transparent"},[]);return(0,yf.jsx)("div",V({ref:r,style:V({marginLeft:5,cursor:"pointer",transition:"background-color .3s",borderRadius:2},e)},n,{onMouseEnter:o,onMouseLeave:i,children:(0,yf.jsx)("svg",{viewBox:"0 0 1024 1024",width:"24",height:"24",style:{display:"block"},children:(0,yf.jsx)("path",{d:"M373.888 576h276.224c9.322667 0 14.293333 11.178667 9.173333 18.773333l-1.258666 1.557334-138.112 146.858666a10.709333 10.709333 0 0 1-14.293334 1.365334l-1.536-1.365334-138.112-146.858666c-6.592-6.997333-2.666667-18.645333 5.973334-20.16l1.941333-0.170667h276.224-276.224z m146.026667-295.189333l138.112 146.858666c7.04 7.509333 2.069333 20.330667-7.914667 20.330667H373.888c-9.984 0-14.976-12.821333-7.914667-20.330667l138.112-146.858666a10.730667 10.730667 0 0 1 15.829334 0z",fill:"var(--chrome-arrow-fill)"})})}))}var rn=B(X()),ys=B(X()),fO=["prefixCls","className","style","color","inputType","rectProps","onChange"],qn=function(t){return t.HEXA="hexa",t.RGBA="rgba",t.HSLA="hsla",t}({}),DS=gs.default.forwardRef((t,e)=>{var{prefixCls:n="w-color-chrome",className:r,style:o,color:i,inputType:s=qn.RGBA,rectProps:a={},onChange:l}=t,u=Se(t,fO),d=typeof i=="string"&&fl(i)?wr(i):i||{h:0,s:0,l:0,a:0},f=E=>l&&l(dt(E)),[m,c]=(0,gS.useState)(s),y=()=>{m===qn.RGBA&&c(qn.HSLA),m===qn.HSLA&&c(qn.HEXA),m===qn.HEXA&&c(qn.RGBA)},v={paddingTop:6},S={textAlign:"center",paddingTop:4,paddingBottom:4},h=V({"--chrome-arrow-fill":"#333","--chrome-arrow-background-color":"#e8e8e8",borderRadius:0,flexDirection:"column",width:230,padding:0},o),p={"--chrome-alpha-box-shadow":"rgb(0 0 0 / 25%) 0px 0px 1px inset",borderRadius:"50%",background:L1(d),boxShadow:"var(--chrome-alpha-box-shadow)"};return(0,rn.jsx)(oS,V({ref:e,color:d,style:h,colors:void 0,className:[n,r].filter(Boolean).join(" "),placement:Be.TopLeft},u,{addonAfter:(0,ys.jsxs)(gs.Fragment,{children:[(0,rn.jsx)(lS,{hsva:d,style:{width:"100%",height:130},onChange:E=>{f(V({},d,E,{a:d.a}))}}),(0,ys.jsxs)("div",{style:{padding:15,display:"flex",alignItems:"center"},children:[(0,rn.jsx)(dl,{width:24,height:24,hsva:d,radius:2,style:{marginRight:15,borderRadius:"50%"},bgProps:{style:{background:"transparent"}},innerProps:{style:p},pointer:()=>(0,rn.jsx)(gs.Fragment,{})}),(0,ys.jsxs)("div",{style:{flex:1},children:[(0,rn.jsx)(dS,{hue:d.h,style:{width:"100%"},bgProps:{style:{borderRadius:2}},onChange:E=>{f(V({},d,E))}}),(0,rn.jsx)(dl,{hsva:d,style:{marginTop:10},bgProps:{style:{borderRadius:2}},onChange:E=>{f(V({},d,E))}})]})]}),(0,ys.jsxs)("div",{style:{display:"flex",alignItems:"flex-start",padding:"0 15px 15px 15px",userSelect:"none"},children:[(0,ys.jsxs)("div",{style:{flex:1},children:[m==qn.RGBA&&(0,rn.jsx)(mf,{hsva:d,rProps:{labelStyle:v,inputStyle:S},gProps:{labelStyle:v,inputStyle:S},bProps:{labelStyle:v,inputStyle:S},aProps:{labelStyle:v,inputStyle:S},onChange:E=>f(E.hsva)}),m===qn.HEXA&&(0,rn.jsx)(Ho,{label:"HEX",labelStyle:v,inputStyle:S,value:d.a>0&&d.a<1?P1(d).toLocaleUpperCase():cl(d).toLocaleUpperCase(),onChange:(E,D)=>{typeof D=="string"&&f(wr(/^#/.test(D)?D:"#"+D))}}),m===qn.HSLA&&(0,rn.jsx)(yS,{hsva:d,hProps:{labelStyle:v,inputStyle:S},sProps:{labelStyle:v,inputStyle:S},lProps:{labelStyle:v,inputStyle:S},aProps:{labelStyle:v,inputStyle:S},onChange:E=>f(E.hsva)})]}),(0,rn.jsx)(Wh,{onClick:y})]})]}),rectRender:()=>(0,rn.jsx)(gs.Fragment,{})}))});DS.displayName="Chrome";var yl=DS;var Hh=B(se()),Et=B(X());function vS(t){let[e,n]=(0,Hh.useState)(t.rule),[r,o]=(0,Hh.useState)(!1),i=(f,m)=>{let c={...e,[f]:m};n(c),t.onChange(c)},{refs:s,floatingStyles:a,context:l}=ls({open:r,onOpenChange:f=>o(f),middleware:[Zn(6),Dr(),Er(),vr()],whileElementsMounted:oo}),u=as(l),{getFloatingProps:d}=us([u]);return(0,Et.jsx)("div",{className:"form-item",children:(0,Et.jsxs)("div",{className:"form-content",ref:s.setReference,children:[(0,Et.jsx)("button",{className:"list-remove-button",onClick:()=>t.onRemove(t.rule.id),children:"x"}),(0,Et.jsx)("input",{type:"number",defaultValue:t.rule.min,placeholder:"min",className:"cell-rule-value",onChange:f=>i("min",f.target.value)}),(0,Et.jsx)("span",{children:"\u2264"}),(0,Et.jsx)("span",{children:"contributions"}),(0,Et.jsx)("span",{children:"\uFF1C"}),(0,Et.jsx)("input",{type:"number",defaultValue:t.rule.max,placeholder:"max",className:"cell-rule-value",onChange:f=>i("max",f.target.value)}),(0,Et.jsx)("span",{children:"="}),(0,Et.jsx)("span",{className:"color-indicator",style:{backgroundColor:t.rule.color},onClick:()=>o(!r)}),r?(0,Et.jsx)("div",{ref:s.setFloating,style:{...a},...d(),children:(0,Et.jsx)(yl,{color:t.rule.color,onChange:f=>{i("color",f.hexa)}})}):null,(0,Et.jsx)("input",{type:"text",defaultValue:t.rule.text,placeholder:"emoji",className:"cell-rule-text",onChange:f=>i("text",f.target.value)})]})})}var zh=[{name:Q.get().form_theme_placeholder,description:"",rules:[]},{name:"default",description:"",rules:kl},{name:"Ocean",description:"",rules:gf("Ocean","#8dd1e2","#63a1be","#376d93","#012f60")},{name:"Halloween",description:"",rules:gf("Halloween","#fdd577","#faaa53","#f07c44","#d94e49")},{name:"Lovely",description:"",rules:gf("Lovely","#fedcdc","#fdb8bf","#f892a9","#ec6a97")},{name:"Wine",description:"",rules:gf("Wine","#d8b0b3","#c78089","#ac4c61","#830738")}];function gf(t,e,n,r,o){return[{id:`${t}_a`,color:e,min:1,max:2},{id:`${t}_b`,color:n,min:2,max:3},{id:`${t}_c`,color:r,min:3,max:5},{id:`${t}_d`,color:o,min:5,max:9999}]}var et=B(X()),on={CODE:(0,et.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"lucide lucide-code-2",children:[(0,et.jsx)("path",{d:"m18 16 4-4-4-4"}),(0,et.jsx)("path",{d:"m6 8-4 4 4 4"}),(0,et.jsx)("path",{d:"m14.5 4-5 16"})]}),ALIGN_LEFT:(0,et.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"svg-icon lucide lucide-align-left",children:[(0,et.jsx)("line",{x1:"21",x2:"3",y1:"6",y2:"6"}),(0,et.jsx)("line",{x1:"15",x2:"3",y1:"12",y2:"12"}),(0,et.jsx)("line",{x1:"17",x2:"3",y1:"18",y2:"18"})]}),ALIGN_CENTER:(0,et.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"svg-icon lucide lucide-align-center",children:[(0,et.jsx)("line",{x1:"21",x2:"3",y1:"6",y2:"6"}),(0,et.jsx)("line",{x1:"17",x2:"7",y1:"12",y2:"12"}),(0,et.jsx)("line",{x1:"19",x2:"5",y1:"18",y2:"18"})]}),ALIGN_RIGHT:(0,et.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"svg-icon lucide lucide-align-right",children:[(0,et.jsx)("line",{x1:"21",x2:"3",y1:"6",y2:"6"}),(0,et.jsx)("line",{x1:"21",x2:"9",y1:"12",y2:"12"}),(0,et.jsx)("line",{x1:"21",x2:"7",y1:"18",y2:"18"})]})};var ES=[{tip:"left",icon:on.ALIGN_LEFT,value:"left"},{tip:"center",icon:on.ALIGN_CENTER,value:"center"},{tip:"right",icon:on.ALIGN_RIGHT,value:"right"}],Uh=[{label:Q.get().form_graph_type_git,value:"default",selected:!0},{label:Q.get().form_graph_type_month_track,value:"month-track"},{label:Q.get().form_graph_type_calendar,value:"calendar"}],jh=[{label:Q.get().weekday_sunday,value:"0",selected:!ws()},{label:Q.get().weekday_monday,value:"1",selected:ws()},{label:Q.get().weekday_tuesday,value:"2"},{label:Q.get().weekday_wednesday,value:"3"},{label:Q.get().weekday_thursday,value:"4"},{label:Q.get().weekday_friday,value:"5"},{label:Q.get().weekday_saturday,value:"6"}],$h=[{label:Q.get().form_cell_shape_rounded,value:"",selected:!0},{label:Q.get().form_cell_shape_square,value:"0%"},{label:Q.get().form_cell_shape_circle,value:"50%"}],wS=[{label:Q.get().form_datasource_type_page,value:"PAGE",selected:!0},{label:Q.get().form_datasource_type_all_task,value:"ALL_TASK"},{label:Q.get().form_datasource_type_task_in_specific_page,value:"TASK_IN_SPECIFIC_PAGE"}];function SS(t){return t==="PAGE"?[]:[{label:Q.get().form_datasource_filter_type_none,value:"NONE"},{label:Q.get().form_datasource_filter_type_status_is,value:"STATUS_IS"},{label:Q.get().form_datasource_filter_type_status_in,value:"STATUS_IN"},{label:Q.get().form_datasource_filter_type_contains_any_tag,value:"CONTAINS_ANY_TAG"}]}var CS=t=>{let e=[{label:Q.get().form_count_field_count_field_type_default,value:"DEFAULT"},{label:Q.get().form_count_field_count_field_type_page_prop,value:"PAGE_PROPERTY"}];return(t==="ALL_TASK"||t==="TASK_IN_SPECIFIC_PAGE")&&e.push({label:Q.get().form_count_field_count_field_type_task_prop,value:"TASK_PROPERTY"}),e},xS=t=>{let e=[{label:Q.get().form_date_field_type_file_ctime,value:"FILE_CTIME"},{label:Q.get().form_date_field_type_file_mtime,value:"FILE_MTIME"},{label:Q.get().form_date_field_type_file_name,value:"FILE_NAME"},{label:Q.get().form_date_field_type_file_specific_page_property,value:"PAGE_PROPERTY"}];return(t==="ALL_TASK"||t==="TASK_IN_SPECIFIC_PAGE")&&e.push({label:Q.get().form_date_field_type_file_specific_task_property,value:"TASK_PROPERTY"}),e},TS=[{label:Q.get().form_datasource_filter_task_status_completed,value:"COMPLETED",selected:!0},{label:Q.get().form_datasource_filter_task_status_fully_completed,value:"FULLY_COMPLETED"},{label:Q.get().form_datasource_filter_task_status_incomplete,value:"INCOMPLETE"},{label:Q.get().form_datasource_filter_task_status_canceled,value:"CANCELED"},{label:Q.get().form_datasource_filter_task_status_any,value:"ANY"}],FS=[{label:Q.get().form_date_range_latest_days,value:"LATEST_DAYS"},{label:Q.get().form_date_range_fixed_date,value:"FIXED_DATE_RANGE"},{label:Q.get().form_date_range_latest_month,value:"LATEST_MONTH"},{label:Q.get().form_date_range_latest_year,value:"LATEST_YEAR"}];var vl=B(se());var gl=require("obsidian");function Gh(t,e){let n=new Map,r=t==null?void 0:t.toLowerCase();return e.vault.getAllLoadedFiles().forEach(o=>{if(o instanceof gl.TFile){let i=e.metadataCache.getCache(o.path);if(i){for(let s in i.frontmatter)if(!n.has(s)&&(!r||s.toLowerCase().includes(r))){let a=i.frontmatter[s];n.set(s,{name:s,sampleValue:a})}}}}),Array.from(n.values())}function _S(t,e){let n=e.vault.getAllLoadedFiles(),r=[],o=t.toLowerCase();return n.forEach(i=>{if(i instanceof gl.TFile){let s=this.app.metadataCache.getCache(i.path);if(s){let a=(0,gl.getAllTags)(s);a==null||a.forEach(l=>{l.toLowerCase().contains(o)&&!r.includes(l)&&r.push(l)})}}}),r.map(i=>({id:i,label:i,value:i}))}var Dl=B(se());var kS=require("obsidian"),Yh=B(se()),zo=B(se()),kn=B(X());function Df(t){let{query:e,onOpenChange:n,anchorElement:r,showSuggest:o}=t,[i,s]=Yh.useState(-1),[a,l]=Yh.useState([]);(0,zo.useEffect)(()=>{if(e==null||e==null)return;let v=(0,kS.debounce)(S=>{l(t.getItems(S))},300,!0);return v(e),()=>{v.cancel()}},[e,t.getItems]),(0,zo.useEffect)(()=>{s(-1)},[e]),(0,zo.useEffect)(()=>{t.onSelectChange&&(i==null?t.onSelectChange(null,-1):t.onSelectChange(a[i],i))},[i,a]);let{refs:u,floatingStyles:d,context:f}=ls({open:o,onOpenChange:n,middleware:[Zn(6),Dr(),Er(),vr()],whileElementsMounted:oo,elements:{reference:r}}),m=as(f),c=M1(f,{role:"tooltip"}),{getFloatingProps:y}=us([m,c]);return(0,zo.useEffect)(()=>{if(!o)return;function v(S){if(S.key==="ArrowDown")S.preventDefault(),s(h=>(h+1)%a.length);else if(S.key==="ArrowUp")S.preventDefault(),s(h=>(h-1+a.length)%a.length);else if(S.key==="Enter"){if(i<0||i>=a.length)return;a.length>0&&(S.preventDefault(),t.onSelected(a[i],i))}else S.key==="Escape"&&(S.preventDefault(),al())}return window.addEventListener("keydown",v),()=>{window.removeEventListener("keydown",v)}},[i,a,o]),(0,zo.useLayoutEffect)(()=>{var h;if(!o||i==null)return;let v=(h=u.floating)==null?void 0:h.current,S=v==null?void 0:v.children[i];S&&v&&dO(v,S)},[i,o]),(0,kn.jsx)(kn.Fragment,{children:o&&a.length>0&&(0,kn.jsx)("div",{className:"suggest-container",ref:u.setFloating,style:{...d},...y(),children:a.map((v,S)=>(0,kn.jsxs)("div",{className:`suggest-item ${S===i?"selected":""}`,onClick:h=>{h.preventDefault(),t.onSelected(v,S),s(S)},children:[v.icon&&(0,kn.jsx)("div",{className:"suggest-icon",children:v.icon}),(0,kn.jsxs)("div",{className:"suggest-content",children:[(0,kn.jsx)("div",{className:"suggest-label",children:v.label}),(0,kn.jsx)("div",{className:"suggest-description",children:v.description})]})]},v.id))})})}var dO=(t,e)=>{let n=t.offsetHeight,r=e?e.offsetHeight:0,o=e.offsetTop,i=o+r;o<t.scrollTop?t.scrollTop-=t.scrollTop-o+5:i>n+t.scrollTop&&(t.scrollTop+=i-n-t.scrollTop+5)};var Uo=B(X());function Zh(t){let{inputPlaceholder:e}=t,[n,r]=Dl.useState(t.defaultInputValue),[o,i]=Dl.useState(!1),s=Dl.useRef(null);return(0,Uo.jsxs)(Uo.Fragment,{children:[(0,Uo.jsx)("input",{type:"text",placeholder:e||"",ref:s,onChange:a=>{t.onInputChange(a.target.value),r(a.target.value),i(!0)},value:n}),s.current&&(0,Uo.jsx)(Df,{query:n||"",showSuggest:o,getItems:t.getItems,onSelected:a=>{t.onSelected(a),r(a.value),i(!1)},anchorElement:s.current,onOpenChange:a=>i(a)})]})}var Ds=B(se());var zt=B(X());function qh(t){let[e,n]=(0,Ds.useState)(""),[r,o]=(0,Ds.useState)(!1),[i,s]=(0,Ds.useState)(!1),{tags:a}=t,l=(0,Ds.useRef)(null),u=m=>{let c=a.filter(y=>y.id!==m);t.onChange(c)},d=m=>{let c=new Date().getTime().toString()+"_"+m,y=[...a,{id:c,value:m}];t.onChange(y)},f=m=>{var y,v;if(r)return;let{key:c}=m;if(!((y=t.excludeTriggerKeys)!=null&&y.includes(c))&&(c==="Tab"||c==="Enter"||c===" ")){m.preventDefault(),s(!1);let S=(v=l.current)==null?void 0:v.value;S&&(d(S),l.current.value="")}};return(0,zt.jsxs)(zt.Fragment,{children:[(0,zt.jsxs)("div",{className:"suggest-input-tags",children:[(0,zt.jsx)("div",{className:"tags",children:a==null?void 0:a.map((m,c)=>(0,zt.jsxs)("div",{className:"tag",children:[(0,zt.jsx)("span",{className:"icon",children:m.icon}),(0,zt.jsx)("span",{children:m.value}),(0,zt.jsx)("span",{className:"remove-button",onClick:()=>u(m.id),children:"x"})]},m.id))}),(0,zt.jsx)("input",{ref:l,className:"input",placeholder:t.inputPlaceholder,onFocus:()=>s(!0),onKeyDown:m=>f(m),onChange:m=>{n(m.target.value),i||s(!0)}})]}),l.current&&(0,zt.jsx)(Df,{query:e||"",showSuggest:i,getItems:()=>t.getItems?t.getItems(e):[],onSelected:(m,c)=>{c>=0&&(d(m.value),l.current.value=""),i&&s(!1)},onSelectChange:(m,c)=>{c>=0?o(!0):o(!1)},anchorElement:l.current,onOpenChange:m=>s(m)})]})}var te=B(X());function OS(t){var S,h,p,E,D,x,O,_,k,L,N,ee,oe;let{dataSource:e}=t,[n,r]=(0,vl.useState)((S=e.dateField)!=null&&S.format?"manual":"smart_detect"),[o,i]=(0,vl.useState)(e.type||"PAGE"),s=(b,$)=>{let T={...e,[b]:$};t.onChange(T)},a=(b,$)=>{let T={...e.dateField,[b]:$};s("dateField",T)},l=(b,$)=>{let T={...e.countField,[b]:$};s("countField",T)},u=(b,$,T)=>{var Y;let P=(Y=e.filters)==null?void 0:Y.map(Z=>Z.id==b?$=="type"&&T=="STATUS_IS"?{...Z,[$]:T,value:"COMPLETED"}:{...Z,[$]:T}:Z);s("filters",P)},d=b=>{let $=e.filters||[];$.push({id:b,type:"NONE"}),s("filters",$)},f=b=>{var T;let $=(T=e.filters)==null?void 0:T.filter(P=>P.id!=b);s("filters",$||[])},m=b=>{var T,P;let $={...e,type:b};b==="PAGE"&&($.filters=[],((T=$.dateField)==null?void 0:T.type)==="TASK_PROPERTY"&&($.dateField={type:"FILE_CTIME"}),((P=e.countField)==null?void 0:P.type)==="TASK_PROPERTY"&&(l("type","DEFAULT"),l("value",void 0),$.countField={type:"DEFAULT"})),t.onChange($)},c=b=>b.type!="CONTAINS_ANY_TAG"&&b.type!="STATUS_IN"?[]:b.value instanceof Array?b.value.map($=>({id:$,label:$,value:$})):[],y=["ALL_TASK","TASK_IN_SPECIFIC_PAGE"],v=Q.get();return(0,te.jsxs)(vl.Fragment,{children:[(0,te.jsxs)("div",{className:"form-item",children:[(0,te.jsx)("span",{className:"label",children:v.form_data_source_value}),(0,te.jsxs)("div",{className:"form-content",children:[(0,te.jsx)("select",{defaultValue:e.type||"PAGE",onChange:b=>{i(b.target.value),m(b.target.value)},children:wS.map(b=>(0,te.jsx)("option",{value:b.value,children:b.label},b.value))}),o!="ALL_TASK"&&(0,te.jsx)("input",{type:"text",defaultValue:e.value,placeholder:v.form_query_placeholder,onChange:b=>{s("value",b.target.value)}})]})]}),y.includes(e.type)&&(0,te.jsxs)("div",{className:"form-item",children:[(0,te.jsx)("span",{className:"label",children:v.form_data_source_filter_label}),(0,te.jsxs)("div",{className:"form-vertical-content",children:[(h=e.filters)==null?void 0:h.map((b,$)=>(0,te.jsxs)("div",{className:"form-content",children:[(0,te.jsx)("select",{value:b.type||"NONE",onChange:T=>{u(b.id,"type",T.target.value)},children:SS("TASK").map(T=>(0,te.jsx)("option",{value:T.value,children:T.label},T.value))}),b.type=="STATUS_IS"&&(0,te.jsx)("select",{defaultValue:(b==null?void 0:b.value)||"NONE",onChange:T=>{u(b.id,"value",T.target.value)},children:TS.map(T=>(0,te.jsx)("option",{value:T.value,children:T.label},T.value))}),(b==null?void 0:b.type)=="CONTAINS_ANY_TAG"?(0,te.jsx)(qh,{tags:c(b),onChange:T=>{u(b.id,"value",T.map(P=>P.value))},onRemove:T=>{var P;(b==null?void 0:b.value)instanceof Array&&u(b.id,"value",(P=b==null?void 0:b.value)==null?void 0:P.filter(Y=>Y!=T.value))},getItems:T=>_S(T,t.app),inputPlaceholder:v.form_datasource_filter_contains_tag_input_placeholder}):null,(b==null?void 0:b.type)=="STATUS_IN"?(0,te.jsx)(qh,{tags:c(b),onChange:T=>{u(b.id,"value",T.map(P=>P.value))},onRemove:T=>{var P;(b==null?void 0:b.value)instanceof Array&&u(b.id,"value",(P=b==null?void 0:b.value)==null?void 0:P.filter(Y=>Y!=T.value))},getItems:T=>[{id:"CANCELED",label:v.form_datasource_filter_task_status_canceled,value:"CANCELED",icon:on.CODE},{id:"COMPLETED",label:v.form_datasource_filter_task_status_completed,value:"COMPLETED",icon:on.CODE},{id:"INCOMPLETE",label:v.form_datasource_filter_task_status_incomplete,value:"INCOMPLETE",icon:on.CODE},{id:"ANY",label:v.form_datasource_filter_task_status_any,value:"ANY",icon:on.CODE},{id:"FULLY_COMPLETED",label:v.form_datasource_filter_task_status_fully_completed,value:"FULLY_COMPLETED",icon:on.CODE}],inputPlaceholder:v.form_datasource_filter_contains_tag_input_placeholder}):null,(0,te.jsx)("button",{className:"list-remove-button",onClick:T=>f(b.id),children:"x"})]},b.id)),(0,te.jsx)("div",{className:"form-content",children:(0,te.jsx)("button",{className:"list-add-button",onClick:b=>d(Date.now().toString()),children:"+"})})]})]}),(0,te.jsxs)("div",{className:"form-item",children:[(0,te.jsx)("span",{className:"label",children:v.form_date_field}),(0,te.jsxs)("div",{className:"form-content",children:[(0,te.jsx)("select",{defaultValue:((p=e.dateField)==null?void 0:p.type)||"FILE_CTIME",onChange:b=>{a("type",b.target.value)},children:xS(e.type).map(b=>(0,te.jsx)("option",{value:b.value,children:b.label},b.value))}),((E=e.dateField)==null?void 0:E.type)=="PAGE_PROPERTY"&&(0,te.jsx)(Zh,{defaultInputValue:(D=e.dateField)==null?void 0:D.value,onInputChange:b=>{a("value",b)},inputPlaceholder:v.form_date_field_placeholder,getItems:b=>Gh(b,t.app).map(($,T)=>({id:$.name,value:$.name,label:$.name,icon:on.CODE,description:$.sampleValue||""})),onSelected:b=>{a("value",b.value)}}),((x=e.dateField)==null?void 0:x.type)=="TASK_PROPERTY"&&(0,te.jsx)("input",{type:"text",defaultValue:((O=e.dateField)==null?void 0:O.value)||"",placeholder:v.form_date_field_placeholder,onChange:b=>{a("value",b.target.value)}})]})]}),(0,te.jsxs)("div",{className:"form-item",children:[(0,te.jsx)("span",{className:"label",children:v.form_date_field_format}),(0,te.jsxs)("div",{className:"form-vertical-content",children:[(0,te.jsxs)("select",{defaultValue:n,onChange:b=>{r(b.target.value),b.target.value=="smart_detect"&&a("format",void 0)},children:[(0,te.jsx)("option",{value:"smart_detect",children:v.form_date_field_format_type_smart}),(0,te.jsx)("option",{value:"manual",children:v.form_date_field_format_type_manual})]}),n=="manual"?(0,te.jsxs)(te.Fragment,{children:[(0,te.jsx)("input",{type:"text",defaultValue:((_=e.dateField)==null?void 0:_.format)||"",name:"dateFieldFormat",placeholder:v.form_date_field_format_placeholder,onChange:b=>{a("format",b.target.value)}}),(0,te.jsxs)("div",{className:"form-description",children:[(0,te.jsx)("a",{href:"https://moment.github.io/luxon/#/formatting?id=table-of-tokens",children:"Luxon Format"})," "+v.form_date_field_format_sample,":"," "+K.fromJSDate(new Date("2024-01-01 00:00:00")).toFormat(((k=e.dateField)==null?void 0:k.format)||"yyyy-MM-dd'T'HH:mm:ss")]})]}):null]})]}),(0,te.jsxs)("div",{className:"form-item",children:[(0,te.jsx)("span",{className:"label",children:v.form_count_field_count_field_label}),(0,te.jsxs)("div",{className:"form-vertical-content",children:[(0,te.jsx)("select",{defaultValue:((L=e.countField)==null?void 0:L.type)||"DEFAULT",onChange:b=>{l("type",b.target.value)},children:CS(e.type).map(b=>(0,te.jsx)("option",{value:b.value,children:b.label},b.value))}),((N=e.countField)==null?void 0:N.type)=="PAGE_PROPERTY"||((ee=e.countField)==null?void 0:ee.type)=="TASK_PROPERTY"?(0,te.jsx)(Zh,{defaultInputValue:((oe=e.countField)==null?void 0:oe.value)||"",onInputChange:b=>{l("value",b)},inputPlaceholder:v.form_count_field_count_field_input_placeholder,getItems:b=>Gh(b,t.app).map(($,T)=>({id:$.name,value:$.name,label:$.name,icon:on.CODE,description:$.sampleValue||""})),onSelected:b=>{l("value",b.value)}}):null]})]})]})}var RS=B(se());var Sr=B(X());function bS(t){return(0,Sr.jsxs)("div",{className:"contribution-graph-divider",children:[(0,Sr.jsx)("div",{}),t.text&&(0,Sr.jsxs)(Sr.Fragment,{children:[(0,Sr.jsx)("span",{children:t.text}),(0,Sr.jsx)("div",{})]})]})}var Ut=B(X());function vf(t){let[e,n]=(0,RS.useState)(t.activeIndex||0);return(0,Ut.jsxs)("div",{className:"tab-container",children:[(0,Ut.jsx)("div",{className:"tab-titles",children:t.tabs.map((r,o)=>(0,Ut.jsx)(mO,{active:o==e,...r,onClick:()=>{var i;n(o),(i=r.onClick)==null||i.call(r)}},o))}),(0,Ut.jsx)(bS,{}),(0,Ut.jsx)("div",{className:"tab-items",children:t.tabs.map((r,o)=>(0,Ut.jsx)(pO,{title:r.title,icon:r.icon,active:o==e,onClick:()=>{var i;n(o),(i=r.onClick)==null||i.call(r)},children:r.children},o))})]})}function mO(t){let{title:e,icon:n,active:r}=t;return(0,Ut.jsxs)("div",{className:`tab-item-title ${r?"active":""}`,onClick:o=>{var i;return(i=t.onClick)==null?void 0:i.call(t)},children:[n&&(0,Ut.jsx)("span",{children:n}),(0,Ut.jsx)("span",{children:e})]})}function pO(t){let{children:e,active:n}=t;return(0,Ut.jsx)("div",{className:`tab-item ${n?"active":""}`,children:(0,Ut.jsx)("div",{className:"tab-item-content",children:e})})}var Kh=require("obsidian"),Ef=B(se());var MS=B(X()),hO=t=>{let[e,n]=(0,Ef.useState)(t.defaultValue.toString()),r=Q.get(),o=i=>{let s=i.target.value,a=/^-?\d*$/;if(s===""){n("");return}if(a.test(s)){if(t.min!==void 0&&Number(s)<t.min){new Kh.Notice(r.form_number_input_min_warning.replace("{value}",t.min.toString())),n(t.min.toString());return}if(t.max!==void 0&&Number(s)>t.max){new Kh.Notice(r.form_number_input_max_warning.replace("{value}",t.max.toString())),n(t.max.toString());return}n(s)}};return(0,Ef.useEffect)(()=>{e==""?t.onChange(t.defaultValue):t.onChange(parseInt(e))},[e]),(0,MS.jsx)("input",{type:"text",value:e,onChange:o,placeholder:t.placeholder})},NS=hO;var IS=B(se()),On=B(X());function AS(t){var l;let[e,n]=(0,IS.useState)(!1),{refs:r,floatingStyles:o,context:i}=ls({open:e,onOpenChange:u=>n(u),middleware:[Zn(6),Dr(),Er(),vr()],whileElementsMounted:oo}),s=as(i),{getFloatingProps:a}=us([s]);return(0,On.jsxs)(On.Fragment,{children:[(0,On.jsx)("span",{className:"color-indicator",style:{backgroundColor:t.color},onClick:()=>n(!e)}),t.color&&(0,On.jsxs)("div",{className:"color-label",children:[(0,On.jsx)("span",{children:(l=t.color)!=null?l:""}),(0,On.jsx)("span",{className:"color-reset-button",onClick:()=>{t.onReset?t.onReset(t.defaultColor):t.onChange(t.defaultColor)},children:"x"})]}),e?(0,On.jsx)("div",{ref:r.setFloating,style:{...o},...a(),children:(0,On.jsx)(yl,{color:t.color||"#FFFFFF",onChange:u=>{t.onChange(u.hexa)}})}):null]})}var Ze=B(X());function LS(){let t=Q.get();return(0,Ze.jsxs)("div",{className:"about-container",children:[(0,Ze.jsxs)("div",{className:"about-item",children:[(0,Ze.jsx)("div",{className:"label",children:t.form_contact_me}),(0,Ze.jsx)("a",{href:"https://mp.weixin.qq.com/s/k5usslOZwWNFT5rlq3lAPA",children:"\u5FAE\u4FE1\u516C\u4F17\u53F7"}),(0,Ze.jsx)("a",{href:"https://github.com/vran-dev",children:"Github"})]}),(0,Ze.jsxs)("div",{className:"about-item",children:[(0,Ze.jsx)("div",{className:"label",children:t.form_project_url}),(0,Ze.jsx)("div",{children:(0,Ze.jsx)("a",{href:"https://github.com/vran-dev/obsidian-contribution-graph",children:"https://github.com/vran-dev/obsidian-contribution-graph"})})]}),(0,Ze.jsxs)("div",{className:"about-item",children:[(0,Ze.jsx)("div",{className:"label",children:t.form_sponsor}),(0,Ze.jsx)("div",{children:(0,Ze.jsx)(vf,{activeIndex:0,tabs:[{title:"\u5FAE\u4FE1",children:(0,Ze.jsx)("a",{href:"https://mp.weixin.qq.com/s/k5usslOZwWNFT5rlq3lAPA",children:(0,Ze.jsx)("img",{src:"https://s2.loli.net/2022/05/23/phDIKagHwjZl3kA.jpg"})})},{title:"Buy me a coffee",children:(0,Ze.jsx)("a",{href:"https://www.buymeacoffee.com/vran",children:(0,Ze.jsx)("img",{src:"https://cdn.buymeacoffee.com/buttons/v2/default-yellow.png"})})}]})})]})]})}var I=B(X());function PS(t){var p,E,D,x,O,_,k,L,N,ee,oe,b,$;let{yamlConfig:e}=t,n=Q.get(),r=(0,El.useRef)(null),[o,i]=(0,El.useState)(e),[s,a]=(0,El.useState)(e.cellStyleRules||[]),l=T=>{let{name:P,value:Y}=T.target;m(P,Y)},u=T=>{let{value:P}=T.target,Y=zh.find(Z=>Z.name==P);Y&&(m("cellStyleRules",Y.rules),a(Y.rules))},d=T=>{let{value:P}=T.target;m("cellStyle",{...o.cellStyle,borderRadius:P})},f=()=>{var T;return o.cellStyle&&o.cellStyle.borderRadius&&((T=$h.find(P=>{var Y;return P.value==((Y=o.cellStyle)==null?void 0:Y.borderRadius)}))==null?void 0:T.value)||""},m=(T,P)=>{i(Y=>({...Y,[T]:P}))},c=()=>{let T={id:new Date().getTime(),min:1,max:2,color:"#63aa82",text:""};a([...s,T])},y=()=>{o.cellStyleRules=s,t.onSubmit(o)},v=()=>{if(r.current){r.current.empty();let T=new Ti,P=JSON.parse(JSON.stringify(o));P.cellStyleRules=s,T.renderFromYaml(P,r.current,t.app)}},S=()=>{if(o.titleStyle&&o.titleStyle.fontSize){let T=o.titleStyle.fontSize;return parseInt(T.replace(/[^0-9]/,""))}return 16},h=(T,P)=>{if(!T)return 0;let Y=T.replace(/[^0-9]/,"")||"0";return parseInt(Y)};return(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)(vf,{activeIndex:0,tabs:[{title:n.form_basic_settings,children:(0,I.jsx)("div",{className:"contribution-graph-modal-form",children:(0,I.jsxs)("div",{className:"form-group",children:[(0,I.jsxs)("div",{className:"form-item",children:[(0,I.jsx)("span",{className:"label",children:n.form_title}),(0,I.jsxs)("div",{className:"form-content",children:[(0,I.jsx)("input",{name:"title",type:"text",defaultValue:o.title,placeholder:n.form_title_placeholder,onChange:l,style:{...o.titleStyle,fontSize:"inherits",fontWeight:((p=o.titleStyle)==null?void 0:p.fontWeight)||"normal",textAlign:((E=o.titleStyle)==null?void 0:E.textAlign)||"left"}}),(0,I.jsx)(Yw,{options:ES,defaultValue:((D=o.titleStyle)==null?void 0:D.textAlign)||"left",onChoose:T=>{m("titleStyle",{...o.titleStyle,textAlign:T.value})}})]})]}),(0,I.jsxs)("div",{className:"form-item",children:[(0,I.jsx)("span",{className:"label",children:n.form_graph_type}),(0,I.jsx)("div",{className:"form-content",children:(0,I.jsx)("select",{name:"graphType",defaultValue:o.graphType||((x=Uh.find(T=>T.selected))==null?void 0:x.value),onChange:l,children:Uh.map(T=>(0,I.jsx)("option",{value:T.value,children:T.label},T.value))})})]}),(0,I.jsxs)("div",{className:"form-item",children:[(0,I.jsx)("span",{className:"label",children:n.form_date_range}),(0,I.jsx)("div",{className:"form-content",children:(0,I.jsx)("select",{defaultValue:o.dateRangeType||"LATEST_DAYS",onChange:T=>{m("dateRangeType",T.target.value),T.target.type!="FIXED_DATE_RANGE"?(m("fromDate",void 0),m("toDate",void 0)):m("dateRangeValue",void 0)},children:FS.map(T=>(0,I.jsx)("option",{value:T.value,children:T.label},T.value))})})]}),(0,I.jsxs)("div",{className:"form-item",children:[(0,I.jsx)("span",{className:"label"}),(0,I.jsx)("div",{className:"form-content",children:o.dateRangeType!="FIXED_DATE_RANGE"?(0,I.jsx)(I.Fragment,{children:(0,I.jsx)("input",{type:"number",defaultValue:o.dateRangeValue,min:1,placeholder:n.form_date_range_input_placeholder,onChange:T=>m("dateRangeValue",parseInt(T.target.value))})}):(0,I.jsxs)(I.Fragment,{children:[(0,I.jsx)("input",{id:"fromDate",name:"fromDate",type:"date",defaultValue:o.fromDate,placeholder:"from date, such as 2023-01-01",onChange:l}),"\xA0-\xA0",(0,I.jsx)("input",{id:"toDate",name:"toDate",type:"date",defaultValue:o.toDate,placeholder:"to date, such as 2023-12-31",onChange:l})]})})]}),(0,I.jsx)(OS,{dataSource:o.dataSource,onChange:T=>{m("dataSource",T)},app:t.app})]})})},{title:n.form_style_settings,children:(0,I.jsx)("div",{className:"contribution-graph-modal-form",children:(0,I.jsxs)("div",{className:"form-group",children:[(0,I.jsxs)("div",{className:"form-item",children:[(0,I.jsx)("span",{className:"label",children:n.form_title_font_size_label}),(0,I.jsx)("div",{className:"form-content",children:(0,I.jsx)(NS,{defaultValue:S(),onChange:T=>{m("titleStyle",{...o.titleStyle,fontSize:T+"px"})},min:1,max:128})})]}),(0,I.jsxs)("div",{className:"form-item",children:[(0,I.jsx)("span",{className:"label",children:n.form_fill_the_screen_label}),(0,I.jsx)("div",{className:"form-content",children:(0,I.jsx)("input",{type:"checkbox",className:"checkbox",defaultChecked:o.fillTheScreen,onChange:()=>m("fillTheScreen",!o.fillTheScreen)})})]}),o.graphType=="month-track"?null:(0,I.jsxs)("div",{className:"form-item",children:[(0,I.jsx)("span",{className:"label",children:n.form_start_of_week}),(0,I.jsx)("div",{className:"form-content",children:(0,I.jsx)("select",{id:"startOfWeek",name:"startOfWeek",defaultValue:o.startOfWeek!=null?o.startOfWeek:(O=jh.find(T=>T.selected))==null?void 0:O.value,onChange:l,children:jh.map(T=>(0,I.jsx)("option",{value:T.value,children:T.label},T.value))})})]}),(0,I.jsxs)("div",{className:"form-item",children:[(0,I.jsx)("span",{className:"label",children:n.form_main_container_bg_color}),(0,I.jsx)("div",{className:"form-content",children:(0,I.jsx)(AS,{color:(_=o.mainContainerStyle)==null?void 0:_.backgroundColor,onChange:T=>{m("mainContainerStyle",{...o.mainContainerStyle,backgroundColor:T})},onReset:T=>{m("mainContainerStyle",{...o.mainContainerStyle,backgroundColor:T})}})})]}),(0,I.jsxs)("div",{className:"form-item",children:[(0,I.jsx)("span",{className:"label",children:n.form_enable_main_container_shadow}),(0,I.jsx)("div",{className:"form-content",children:(0,I.jsx)("input",{name:"enableMainContainerShadow",type:"checkbox",className:"checkbox",defaultChecked:((k=o.mainContainerStyle)==null?void 0:k.boxShadow)!=null,onChange:T=>{T.target.checked?m("mainContainerStyle",{...o.mainContainerStyle,boxShadow:"rgba(0, 0, 0, 0.16) 0px 1px 4px"}):m("mainContainerStyle",{...o.mainContainerStyle,boxShadow:void 0})}})})]}),(0,I.jsxs)("div",{className:"form-item",children:[(0,I.jsx)("span",{className:"label",children:n.form_show_cell_indicators}),(0,I.jsx)("div",{className:"form-content",children:(0,I.jsx)("input",{name:"showCellRuleIndicators",type:"checkbox",className:"checkbox",defaultChecked:o.showCellRuleIndicators,onChange:()=>m("showCellRuleIndicators",!o.showCellRuleIndicators)})})]}),(0,I.jsxs)("div",{className:"form-item",children:[(0,I.jsx)("span",{className:"label",children:n.form_cell_shape}),(0,I.jsx)("div",{className:"form-content",children:(0,I.jsx)("select",{name:"cellShape",defaultValue:f(),onChange:d,children:$h.map(T=>(0,I.jsx)("option",{value:T.value,children:T.label},T.label))})})]}),(0,I.jsxs)("div",{className:"form-item",children:[(0,I.jsx)("span",{className:"label",children:n.form_cell_min_width}),(0,I.jsxs)("div",{className:"form-content",children:[(0,I.jsx)("input",{type:"range",min:4,max:64,defaultValue:h((L=o.cellStyle)==null?void 0:L.minWidth,8),onChange:T=>{m("cellStyle",{...o.cellStyle,minWidth:T.target.value+"px"})}}),(0,I.jsx)("span",{className:"input-range-value-label",onClick:T=>{m("cellStyle",{...o.cellStyle,minWidth:void 0})},children:(N=o.cellStyle)!=null&&N.minWidth?(ee=o.cellStyle)==null?void 0:ee.minWidth:n.default})]})]}),(0,I.jsxs)("div",{className:"form-item",children:[(0,I.jsx)("span",{className:"label",children:n.form_cell_min_height}),(0,I.jsxs)("div",{className:"form-content",children:[(0,I.jsx)("input",{type:"range",min:4,max:64,defaultValue:h((oe=o.cellStyle)==null?void 0:oe.minHeight,8),onChange:T=>{m("cellStyle",{...o.cellStyle,minHeight:T.target.value+"px"})}}),(0,I.jsx)("span",{className:"input-range-value-label",onClick:T=>{m("cellStyle",{...o.cellStyle,minHeight:void 0})},children:(b=o.cellStyle)!=null&&b.minHeight?($=o.cellStyle)==null?void 0:$.minHeight:n.default})]})]}),(0,I.jsxs)("div",{className:"form-item",children:[(0,I.jsx)("span",{className:"label",children:n.form_theme}),(0,I.jsx)("div",{className:"form-content",children:(0,I.jsx)("select",{name:"theme","aria-placeholder":"select theme to generate style",onChange:u,children:zh.map(T=>(0,I.jsx)("option",{value:T.name,children:T.name},T.name))})})]}),(0,I.jsxs)("div",{className:"form-item",children:[(0,I.jsx)("span",{className:"label",children:n.form_cell_style_rules}),(0,I.jsxs)("div",{className:"form-vertical-content",children:[s.map(T=>(0,I.jsx)(vS,{rule:T,onChange:P=>{let Y=s.map(Z=>Z.id==P.id?P:Z);a(Y)},onRemove:P=>{let Y=s.filter(Z=>Z.id!=P);a(Y)}},T.id)),(0,I.jsx)("button",{onClick:()=>c(),className:"list-add-button",children:"+"})]})]})]})})},{title:n.form_about,children:(0,I.jsx)(LS,{})}]}),(0,I.jsxs)("div",{className:"contribution-graph-modal-form",children:[(0,I.jsx)("div",{className:"preview-container",ref:r}),(0,I.jsx)("div",{className:"form-item",children:(0,I.jsxs)("div",{className:"form-content",children:[(0,I.jsx)("button",{className:"button",onClick:v,children:n.form_button_preview}),(0,I.jsx)("button",{className:"button",onClick:y,children:n.form_button_save})]})})]})]})}var Qh=B(X()),jo=class extends sn.Modal{constructor(n,r,o){super(n);this.root=null;this.originalConfigContent=r,this.onSave=o}async onOpen(){let{contentEl:n}=this,r=createDiv({parent:n}),o,i=!1;this.originalConfigContent?o=this.parseFromOriginalConfig():(o=this.parseFromSelecttion(),o&&(i=!0)),o||(o=new Co);let s;this.onSave?s=a=>{this.close(),this.onSave((0,sn.stringifyYaml)(a))}:s=a=>{let l=this.app.workspace.getActiveViewOfType(sn.MarkdownView);if(!l)return;let u=l.editor;if(this.close(),i)u.replaceSelection((0,sn.stringifyYaml)(a));else{let d=`\`\`\`contributionGraph
${(0,sn.stringifyYaml)(a)}
\`\`\`
`;u.replaceSelection(d)}},o=Ir.reconcile(o),this.root=(0,VS.createRoot)(r),this.root.render((0,Qh.jsx)(BS.StrictMode,{children:(0,Qh.jsx)(PS,{yamlConfig:o,onSubmit:s,app:this.app})}))}async onClose(){var r;(r=this.root)==null||r.unmount();let{contentEl:n}=this;n.empty()}parseFromOriginalConfig(){if(this.originalConfigContent&&this.originalConfigContent.trim()!="")try{return(0,sn.parseYaml)(this.originalConfigContent)}catch(n){return null}else return null}parseFromSelecttion(){let n=this.app.workspace.getActiveViewOfType(sn.MarkdownView);if(!n)return null;let o=n.editor.getSelection();if(o&&o.trim()!="")try{return(0,sn.parseYaml)(o)}catch(i){return null}else return null}};var $o=require("obsidian");function WS(t,e,n){let r=document.createElement("div");r.className="contribution-graph-codeblock-edit-button";let o=(0,$o.getIcon)("gantt-chart");return o&&r.appendChild(o),n.addEventListener("mouseover",()=>{let i=t.workspace.getActiveViewOfType($o.MarkdownView);i&&i.getMode()!=="preview"&&(r.style.opacity="1",yO(n,r))}),n.addEventListener("mouseout",()=>{r.style.opacity="0"}),r.onclick=()=>{new jo(this.app,e,i=>{let s=this.app.workspace.getActiveViewOfType($o.MarkdownView);if(!s){new $o.Notice("No markdown view is active");return}let l=s.editor.cm,d=l.posAtDOM(n)+21;l.dispatch({changes:{from:d,to:d+(e?e.length:0),insert:i}})}).open()},n.appendChild(r),r}function yO(t,e){var o;let n=t.getElementsByClassName("edit-block-button"),r;n.length>0&&(r=(o=n[0].computedStyleMap().get("top"))==null?void 0:o.toString()),r?e.style.top=r:e.style.top="0"}var wf=class extends HS.Plugin{async onload(){this.registerGlobalRenderApi(),this.registerCodeblockProcessor(),this.registerContributionGraphCreateCommand(),this.registerContextMenu()}onunload(){window.renderContributionGraph=void 0}registerContextMenu(){this.registerEvent(this.app.workspace.on("editor-menu",(e,n,r)=>{e.addItem(o=>{o.setTitle(Q.get().context_menu_create),o.setIcon("gantt-chart"),o.onClick(()=>{new jo(this.app).open()})})}))}registerGlobalRenderApi(){window.renderContributionGraph=(e,n)=>{ln.render(e,n)}}registerCodeblockProcessor(){this.registerMarkdownCodeBlockProcessor("contributionGraph",(e,n,r)=>{new Ti().renderFromCodeBlock(e,n,r,this.app),n.parentElement&&WS(this.app,e,n.parentElement)})}registerContributionGraphCreateCommand(){this.addCommand({id:"create-graph",name:Q.get().context_menu_create,editorCallback:(e,n)=>{new jo(this.app).open()}})}};
/*! Bundled license information:

react/cjs/react.production.min.js:
  (**
   * @license React
   * react.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

scheduler/cjs/scheduler.production.min.js:
  (**
   * @license React
   * scheduler.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react-dom/cjs/react-dom.production.min.js:
  (**
   * @license React
   * react-dom.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)

react/cjs/react-jsx-runtime.production.min.js:
  (**
   * @license React
   * react-jsx-runtime.production.min.js
   *
   * Copyright (c) Facebook, Inc. and its affiliates.
   *
   * This source code is licensed under the MIT license found in the
   * LICENSE file in the root directory of this source tree.
   *)
*/
