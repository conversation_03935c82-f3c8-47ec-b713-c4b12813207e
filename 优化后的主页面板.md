---
cssclasses:
  - editor-full
banner: "https://images5.alphacoders.com/134/1348663.jpeg"
banner_y: 0.692
---

# 🚀 优化后的知识管理系统

## 📊 系统状态监控

```dataviewjs
// 使用优化后的查询系统
const stats = await window.quickQuery('content-stats', { sampleSize: 50 });

dv.header(3, "📈 核心指标");
dv.table(["指标", "数值", "状态"], [
    ["总文件数", stats.totalFiles, "📁"],
    ["平均字数", stats.avgWords, "📝"],
    ["平均阅读时间", `${stats.avgReadingTime} 分钟`, "⏱️"],
    ["系统状态", window.automationManager?.isRunning ? "运行中" : "已停止", window.automationManager?.isRunning ? "🟢" : "🔴"]
]);
```

## 🔍 智能搜索面板

```dataviewjs
// 创建快速搜索界面
const searchHtml = `
<div style="margin: 20px 0;">
    <input type="text" 
           id="smart-search" 
           placeholder="🔍 智能搜索..." 
           style="width: 100%; padding: 12px; border: 1px solid var(--background-modifier-border); border-radius: 8px; font-size: 16px;"
           onkeyup="handleSmartSearch(event)">
    <div id="search-results" style="margin-top: 10px;"></div>
</div>

<script>
async function handleSmartSearch(event) {
    const query = event.target.value;
    const resultsDiv = document.getElementById('search-results');
    
    if (query.length < 2) {
        resultsDiv.innerHTML = '';
        return;
    }
    
    if (window.fileIndexSystem) {
        const results = window.fileIndexSystem.search(query, { limit: 5 });
        
        if (results.length > 0) {
            resultsDiv.innerHTML = results.map(result => 
                \`<div style="padding: 8px; border-left: 3px solid var(--interactive-accent); margin: 4px 0; background: var(--background-secondary);">
                    <strong>\${result.name}</strong> <span style="color: var(--text-muted);">(\${result.matchType})</span><br>
                    <small style="color: var(--text-muted);">\${result.path}</small>
                </div>\`
            ).join('');
        } else {
            resultsDiv.innerHTML = '<div style="color: var(--text-muted); padding: 8px;">未找到匹配结果</div>';
        }
    }
}
</script>
`;

dv.paragraph(searchHtml);
```

## 📋 最近活动

```dataviewjs
// 使用优化查询获取最近文件
const recentFiles = await window.quickQuery('recent-files', { 
    limit: 10, 
    days: 7,
    excludeFolders: ['模板', 'Templates', '.obsidian']
});

dv.header(3, "📝 最近编辑的文件");
dv.table(["文件", "修改时间", "大小", "标签"], 
    recentFiles.map(file => [
        `[[${file.basename}]]`,
        file.modified.toLocaleDateString(),
        `${Math.round(file.size / 1024)}KB`,
        file.tags.slice(0, 3).join(", ") || "无标签"
    ])
);
```

## 🏷️ 标签云

```dataviewjs
// 获取标签分析数据
const tagAnalysis = await window.quickQuery('tag-analysis', { 
    minUsage: 2,
    includeHierarchy: false 
});

dv.header(3, "🏷️ 热门标签");

// 创建标签云HTML
const tagCloudHtml = tagAnalysis.tags.slice(0, 20).map(tag => {
    const size = Math.min(Math.max(tag.count / 5 + 0.8, 0.8), 2);
    const opacity = Math.min(tag.count / 10 + 0.5, 1);
    
    return `<span style="
        font-size: ${size}em; 
        opacity: ${opacity}; 
        margin: 4px 8px; 
        display: inline-block;
        color: var(--interactive-accent);
        cursor: pointer;
    " onclick="app.workspace.openLinkText('tag:${tag.tag}', '', false)">${tag.tag}</span>`;
}).join('');

dv.paragraph(`<div style="text-align: center; line-height: 2;">${tagCloudHtml}</div>`);
```

## 🔗 链接网络状态

```dataviewjs
// 获取链接分析
const linkAnalysis = await window.quickQuery('link-analysis', { 
    minConnections: 2,
    includeBrokenLinks: true 
});

dv.header(3, "🕸️ 知识网络");
dv.table(["指标", "数值"], [
    ["连接文件数", linkAnalysis.connectedFiles],
    ["孤立文件数", linkAnalysis.isolatedFiles],
    ["断链数量", linkAnalysis.brokenLinks.length],
    ["平均连接数", Math.round(linkAnalysis.avgConnections * 10) / 10]
]);

// 显示最连接的文件
if (linkAnalysis.linkStats.length > 0) {
    dv.header(4, "🌟 核心节点文件");
    dv.table(["文件", "总连接数", "入链", "出链"], 
        linkAnalysis.linkStats.slice(0, 5).map(stat => [
            `[[${stat.name}]]`,
            stat.totalConnections,
            stat.inlinks,
            stat.outlinks
        ])
    );
}
```

## 🤖 自动化任务状态

```dataviewjs
// 获取自动化任务状态
const taskStatus = window.automationManager?.getTaskStatus() || { 
    isRunning: false, 
    totalTasks: 0, 
    activeTasks: 0,
    tasks: []
};

dv.header(3, "🤖 自动化系统");
dv.table(["状态", "值"], [
    ["运行状态", taskStatus.isRunning ? "🟢 运行中" : "🔴 已停止"],
    ["总任务数", taskStatus.totalTasks],
    ["活跃任务", taskStatus.activeTasks],
    ["最后执行", taskStatus.recentHistory?.length > 0 ? 
        new Date(taskStatus.recentHistory[taskStatus.recentHistory.length - 1].timestamp).toLocaleString() : 
        "无记录"]
]);

// 显示任务列表
if (taskStatus.tasks.length > 0) {
    dv.header(4, "📋 任务列表");
    dv.table(["任务", "状态", "上次运行", "执行次数"], 
        taskStatus.tasks.map(task => [
            task.name,
            task.enabled ? "✅ 启用" : "❌ 禁用",
            task.lastRun ? new Date(task.lastRun).toLocaleString() : "从未运行",
            task.runCount
        ])
    );
}
```

## 📈 性能指标

```dataviewjs
// 显示性能信息
const performance = window.dataviewOptimizer?.getPerformanceReport() || {};

dv.header(3, "⚡ 性能监控");

if (performance.queryStats) {
    dv.table(["查询类型", "执行次数", "平均耗时", "最大耗时"], 
        performance.queryStats.map(stat => [
            stat.type,
            stat.count,
            `${Math.round(stat.avgTime)}ms`,
            `${Math.round(stat.maxTime)}ms`
        ])
    );
}

// 缓存状态
if (performance.cacheSize !== undefined) {
    dv.paragraph(`💾 缓存大小: ${performance.cacheSize} 项`);
}
```

## 🎯 快速操作

```dataviewjs
// 创建快速操作按钮
const quickActionsHtml = `
<div style="display: flex; flex-wrap: wrap; gap: 10px; margin: 20px 0;">
    <button onclick="window.fileIndexSystem?.initialize()" 
            style="padding: 10px 15px; background: var(--interactive-accent); color: white; border: none; border-radius: 5px; cursor: pointer;">
        🔄 重建索引
    </button>
    
    <button onclick="window.dataviewOptimizer?.cleanupCache()" 
            style="padding: 10px 15px; background: var(--interactive-accent); color: white; border: none; border-radius: 5px; cursor: pointer;">
        🧹 清理缓存
    </button>
    
    <button onclick="window.automationManager?.runTaskManually('check-broken-links')" 
            style="padding: 10px 15px; background: var(--interactive-accent); color: white; border: none; border-radius: 5px; cursor: pointer;">
        🔍 检查断链
    </button>
    
    <button onclick="window.automationManager?.runTaskManually('tag-cleanup-suggestions')" 
            style="padding: 10px 15px; background: var(--interactive-accent); color: white; border: none; border-radius: 5px; cursor: pointer;">
        🏷️ 标签建议
    </button>
</div>
`;

dv.paragraph(quickActionsHtml);
```

## 📊 文件夹分布

```dataviewjs
// 获取文件夹统计
const folderStats = await window.quickQuery('folder-stats', { minFiles: 3 });

dv.header(3, "📁 文件夹分布");
dv.table(["文件夹", "文件数", "平均大小", "最后修改"], 
    folderStats.slice(0, 10).map(folder => [
        folder.name,
        folder.fileCount,
        `${Math.round(folder.avgSize / 1024)}KB`,
        folder.lastModified.toLocaleDateString()
    ])
);
```

---

## 💡 使用提示

### 🔧 系统优化功能
- **智能搜索**: 使用上方搜索框进行快速文件查找
- **自动化任务**: 系统会自动执行维护任务，保持最佳性能
- **性能监控**: 实时监控系统性能，及时发现问题

### ⚡ 性能提升
- 文件索引系统提供更快的搜索速度
- 优化的Dataview查询减少系统负载
- 智能缓存机制提高响应速度

### 🎯 下一步优化
1. 根据使用情况调整自动化任务频率
2. 定期查看性能报告，优化查询
3. 使用标签建议功能整理知识结构

---

*系统版本: 优化版 v1.0 | 最后更新: {{date}}*
