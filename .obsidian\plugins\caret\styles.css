.caret-container {
    padding: 2px 15px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    width: 100%; /* Ensure it takes full width */
    background-color: ; /* Background color not specified */
    white-space: pre-wrap; /* Preserve whitespace and formatting */
}
.caret-messages-container {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    /* margin-bottom: 10px; */
    height: 80%;
    /* min-height: 0; Ensure it doesn't unnecessarily expand */
    width: 100%;
    white-space: pre-wrap; /* Preserve whitespace and formatting */
}

.caret-message {
    padding: 10px; /* Increased padding to prevent text cut-off */
    border-radius: 5px;
    max-width: calc(100% - 10px); /* Prevent overflow */
    word-wrap: break-word; /* Ensure long words do not cause overflow */
    white-space: pre-wrap; /* Preserve whitespace and formatting */
    user-select: text;
    color: black; /* Set text color to black */
    width: 100%;
}

.caret-message.user, .caret-message.system {
    align-self: flex-start;
    background-color: #e0e0e0; /* Light gray for better visibility in both light and dark modes */
    white-space: pre-wrap; /* Preserve whitespace and formatting */
}

.caret-message.assistant {
    background-color: #cfe9e3; /* Light green for better visibility in both light and dark modes */
    white-space: pre-wrap; /* Preserve whitespace and formatting */
}

.caret-full_width_text_container {
    width: 100%;
    resize: vertical;
    overflow-y: auto;
    min-height: 80px;
    margin: 0 0 20px 0;
    box-sizing: border-box;
    white-space: pre-wrap; /* Preserve whitespace and formatting */
}

.caret-button-container {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.caret-flex-col {
    display: flex;
    flex-direction: column;
}
.caret-w-full {
    width: 100%
}
.caret-mb-2 {
    margin-bottom: 0.5rem; /* 8px assuming the root font-size is 16px */
}
.caret-mb-4 {
    margin-bottom: 16px; /* 1rem typically equals 16px */
}
.theme-light .caret-settings_container {
    background-color: rgb(187, 187, 187); /* Light grey for light theme */
    padding: 10px; 
    margin-bottom: 10px;
    border-radius: 4px; /* Rounded corners */
    user-select: text;
    z-index: 1000000;
}

.theme-dark .caret-settings_container {
    background-color: #404040; /* Darker grey for dark theme */
    padding: 10px; 
    margin-bottom: 10px;
    border-radius: 4px; /* Rounded corners */
    user-select: text;
    z-index: 1000000;
}

.theme-light .caret-settings_code_block {
    background-color: #fbf4f4; /* Light grey background for light theme */
    border-radius: 4px; /* Rounded corners */
    padding: 4px;
    user-select: text;
    z-index: 1000000;
}

.theme-dark .caret-settings_code_block {
    background-color: #606060; /* Darker grey for dark theme */
    border-radius: 4px; /* Rounded corners */
    padding: 4px;
    user-select: text;
    z-index: 1000000;
}
.caret-hidden-value-unsecure {
    -webkit-text-security: disc;
}

.caret-insert-file-header {
    font-size: 18px;
    font-weight: bold;
}

.caret-insert-file-files-display {
    height: 200px;
    overflow: auto; /* Changed from 'hidden' to 'auto' to allow scrolling */
}
.caret-insert-file-file-name {
    font-size: 14px;
    padding-top: 1px;
    padding-bottom: 1px;
    padding-left: 2px;
    border: 1px solid transparent; /* Ensures layout doesn't change when selected */
    transition: font-size 0.2s, border-color 0.2s; /* Smooth transition for visual changes */
}
.caret-file-filter-input {
    margin-bottom: 4px;
    width: 100%;
}

.caret-insert-file-file-name.selected {
    font-size: 16px; /* Slightly larger font size for selected item */
    border-color: #cccccc; /* Light gray border for selected item */
    background-color: #f0f0f0; /* Optional: light background color for better visibility */
    border-radius: 4px;
}
.caret-custom-models-table {
    width: 100%;
    border-collapse: collapse;
}

.caret-custom-models-table th,
.caret-custom-models-table td {
    text-align: left;
    padding: 8px;
    border: 1px solid #ddd;
}

.caret-custom-models-table th {
    background-color: #f2f2f2;
}

.caret-custom-models-table tr:nth-child(even) {
    background-color: #f9f9f9;
}

.caret-custom-models-table tr:hover {
    background-color: #ddd;
}

.caret-mod-warning {
    color: red;
    font-weight: bold;
}

.caret-system-prompt-textarea {
    width: 100%;
    height: 400px;
    resize: vertical;
}
.caret-workflow_container {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
}

.caret-flex-row {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
}

.caret-w-8 {
    width: 360px
}
.caret-workflow_text_area {
    height: 150px
}
.caret-row_items_spacing {
    margin-right: 3px;
}

.caret-bottom-screen-padding {
    padding-bottom: 100px;
}
/* Add this CSS to your plugin's stylesheet */
.caret-clickable-icon.wand {
    position: relative;
    padding: 10px; /* Increase padding to make the button larger */
}

.caret-submenu {
    display: none;
    position: absolute;
    margin-top: 15px;
    top: 100%;
    left: 50%;
    transform: translateX(-50%); /* Center the submenu */
    /* background-color: white; */
    border: 1px solid #ccc;
    z-index: 100000;
    min-width: 150px;
    grid-template-columns: repeat(4, 1fr); /* Up to four divs wide */
    gap: 5px; /* Add some space between the icons */
    padding: 2px; /* Add some padding around the grid */
    background-color: #f0f0f0;
    border-radius: 4px;
}

.caret-submenu-item {
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1px;
    border-radius: 4px;
    border: 1px solid #ccc;
}

.caret-submenu-item:hover {
    /* background-color: #f0f0f0; */
}

.caret-message-container {
    display: flex;
    flex-direction: column;
    width: 100%; /* Adjusts to the width of the content */
}

.caret-chat-message-actions {
    /* width: 40px; */
    padding-top: 4px;
    display: flex;
    justify-content: left;
    margin-top: 1px;
}

.theme-light .caret-chat-message-actions {
    color: black;
}

.theme-dark .caret-chat-message-actions {
    background-color: transparent;
}
.caret-chat-message-convert-to-note {
    margin-right: 8px;
}
.caret-chat-message-checkbox {
    top: 3px;
}
.caret-input-container {
    /* background-color: #f0f0f0; */
    background-color: var(--background-primary);;
}
.caret-bulk-convert-label {
    margin-right: 3px;
}
/* .react-markdown {
    line-height: 1.5;
    margin: 0;
    padding: 0;
    white-space: pre-wrap; 
  }
  
.react-markdown code, .react-markdown pre {
    white-space: pre-wrap;
  }
  .styled-markdown {
    line-height: 1.5;
    margin-bottom: 0.5em;
  } */

.caret-markdown-body ul {
    list-style-type: disc;
    /* padding-left: 20px; */
}
.caret-markdown-body li {
    margin-top: 0px;
    margin-bottom: 0px;
}
.caret-markdown-body p {
    margin-top: 0px;
    margin-bottom: 0px;
}

.caret-markdown-body code {
    background-color: #f6f8fa;
    padding: 0.2em 0.4em;
    border-radius: 3px;
    margin-top: 0px;
    margin-bottom: 0px;
}
.caret-hidden {
    display: none;
}

.caret-tab {
    margin-right: 4px;
}

/* Modals CSS */
/* Inline Editing Modal */
.caret-inline-editing-container-div {
    height: 60px;
    overflow: scroll;
}
.caret-inline-editing-textarea {
    width: 100%;
    min-height: 100px;
    resize: none;
}

/* Canvas Styles */
#caret-custom-display {
    width: 100%;
    height: 40px;
    background-color: rgba(211, 211, 211, 0.8);
    padding: 2px;
    padding-left: 8px;
    padding-top: 4px;
}
.caret-submenu {
    display: none;
    /* other styles */
    grid-template-columns: repeat(4, 1fr); /* Up to four divs wide */
    gap: 5px; /* Add some space between the icons */
    padding: 2px; /* Add some padding around the grid */
    z-index: 100000;
}

.caret-submenu.visible {
    display: grid; /* Change this to grid */
}
/* Icon Grid */

/* Chat Component */
/* Chat Copy Icon */
.caret-chat-copy-icon {
    position: absolute;
    top: 2px;
    right: 2px;
    color: white;
    cursor: pointer;
    border-radius: 12px;
    transition: background-color 0.3s ease;
}
.caret-chat-copy-icon:hover {
    background-color: lightgray;
}
.caret-custom-model-modal-container {
    width: 800px;
    min-width: 600px;
}
.caret-system-prompt-modal-text-area {
    height: 400px;
    width: 100%;
}
.caret-workflow-editor-temperature-input {
    appearance: number-input;
    -webkit-appearance: number-input;
}