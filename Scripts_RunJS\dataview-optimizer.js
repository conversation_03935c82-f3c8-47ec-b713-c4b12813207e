/**
 * Dataview查询优化器
 * 提供高性能的查询方法和缓存机制
 */

class DataviewOptimizer {
    constructor() {
        this.cache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存
        this.queryStats = new Map();
    }

    /**
     * 优化的查询方法
     */
    async optimizedQuery(queryConfig) {
        const cacheKey = this.generateCacheKey(queryConfig);
        
        // 检查缓存
        const cached = this.getFromCache(cacheKey);
        if (cached) {
            console.log('📋 使用缓存结果');
            return cached;
        }
        
        const startTime = Date.now();
        
        try {
            let result;
            
            switch (queryConfig.type) {
                case 'recent-files':
                    result = await this.getRecentFiles(queryConfig);
                    break;
                case 'tag-analysis':
                    result = await this.getTagAnalysis(queryConfig);
                    break;
                case 'link-analysis':
                    result = await this.getLinkAnalysis(queryConfig);
                    break;
                case 'folder-stats':
                    result = await this.getFolderStats(queryConfig);
                    break;
                case 'content-stats':
                    result = await this.getContentStats(queryConfig);
                    break;
                default:
                    result = await this.executeCustomQuery(queryConfig);
            }
            
            // 缓存结果
            this.setCache(cacheKey, result);
            
            // 记录性能
            const duration = Date.now() - startTime;
            this.recordQueryStats(queryConfig.type, duration);
            
            console.log(`⚡ 查询完成: ${queryConfig.type}, 耗时: ${duration}ms`);
            return result;
            
        } catch (error) {
            console.error('❌ 查询失败:', error);
            throw error;
        }
    }

    /**
     * 获取最近文件（优化版）
     */
    async getRecentFiles(config = {}) {
        const {
            limit = 20,
            days = 7,
            folder = '',
            excludeFolders = ['模板', 'Templates'],
            includeContent = false
        } = config;
        
        const cutoffDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
        const files = app.vault.getMarkdownFiles();
        
        const recentFiles = files
            .filter(file => {
                // 时间过滤
                if (file.stat.mtime < cutoffDate.getTime()) return false;
                
                // 文件夹过滤
                if (folder && !file.path.startsWith(folder)) return false;
                
                // 排除文件夹
                if (excludeFolders.some(excluded => file.path.includes(excluded))) return false;
                
                return true;
            })
            .map(file => {
                const metadata = app.metadataCache.getFileCache(file);
                return {
                    file: file,
                    path: file.path,
                    name: file.name,
                    basename: file.basename,
                    modified: new Date(file.stat.mtime),
                    created: new Date(file.stat.ctime),
                    size: file.stat.size,
                    tags: metadata?.tags?.map(t => t.tag) || [],
                    links: metadata?.links?.length || 0,
                    headings: metadata?.headings?.length || 0
                };
            })
            .sort((a, b) => b.modified - a.modified)
            .slice(0, limit);
        
        // 如果需要内容预览
        if (includeContent) {
            for (const item of recentFiles) {
                try {
                    const content = await app.vault.read(item.file);
                    item.preview = this.generatePreview(content);
                    item.wordCount = content.split(/\s+/).length;
                } catch (error) {
                    console.warn(`无法读取文件内容: ${item.path}`);
                }
            }
        }
        
        return recentFiles;
    }

    /**
     * 标签分析（优化版）
     */
    async getTagAnalysis(config = {}) {
        const {
            minUsage = 2,
            includeHierarchy = true,
            includeCooccurrence = true
        } = config;
        
        const tagStats = new Map();
        const tagCooccurrence = new Map();
        const files = app.vault.getMarkdownFiles();
        
        // 收集标签统计
        for (const file of files) {
            const metadata = app.metadataCache.getFileCache(file);
            const tags = metadata?.tags?.map(t => t.tag) || [];
            
            // 统计单个标签
            for (const tag of tags) {
                if (!tagStats.has(tag)) {
                    tagStats.set(tag, {
                        tag: tag,
                        count: 0,
                        files: new Set(),
                        avgFileSize: 0,
                        lastUsed: 0
                    });
                }
                
                const stat = tagStats.get(tag);
                stat.count++;
                stat.files.add(file.path);
                stat.lastUsed = Math.max(stat.lastUsed, file.stat.mtime);
            }
            
            // 统计标签共现
            if (includeCooccurrence && tags.length > 1) {
                for (let i = 0; i < tags.length; i++) {
                    for (let j = i + 1; j < tags.length; j++) {
                        const pair = [tags[i], tags[j]].sort().join('|');
                        tagCooccurrence.set(pair, (tagCooccurrence.get(pair) || 0) + 1);
                    }
                }
            }
        }
        
        // 过滤和排序
        const filteredTags = Array.from(tagStats.values())
            .filter(stat => stat.count >= minUsage)
            .sort((a, b) => b.count - a.count);
        
        // 构建标签层次结构
        let hierarchy = {};
        if (includeHierarchy) {
            hierarchy = this.buildTagHierarchy(filteredTags);
        }
        
        // 处理共现数据
        const cooccurrenceData = includeCooccurrence ? 
            Array.from(tagCooccurrence.entries())
                .map(([pair, count]) => {
                    const [tag1, tag2] = pair.split('|');
                    return { tag1, tag2, count };
                })
                .sort((a, b) => b.count - a.count)
                .slice(0, 50) : [];
        
        return {
            tags: filteredTags,
            hierarchy: hierarchy,
            cooccurrence: cooccurrenceData,
            totalTags: tagStats.size,
            totalUsage: Array.from(tagStats.values()).reduce((sum, stat) => sum + stat.count, 0)
        };
    }

    /**
     * 链接分析（优化版）
     */
    async getLinkAnalysis(config = {}) {
        const {
            includeBacklinks = true,
            includeBrokenLinks = true,
            minConnections = 1
        } = config;
        
        const linkStats = new Map();
        const brokenLinks = new Set();
        const files = app.vault.getMarkdownFiles();
        const fileMap = new Map(files.map(f => [f.basename, f]));
        
        // 分析链接
        for (const file of files) {
            const metadata = app.metadataCache.getFileCache(file);
            const links = metadata?.links || [];
            
            if (!linkStats.has(file.path)) {
                linkStats.set(file.path, {
                    file: file.path,
                    name: file.basename,
                    outlinks: 0,
                    inlinks: 0,
                    validLinks: 0,
                    brokenLinks: 0,
                    linkTargets: new Set(),
                    linkSources: new Set()
                });
            }
            
            const stat = linkStats.get(file.path);
            stat.outlinks = links.length;
            
            // 检查链接有效性
            for (const link of links) {
                const linkText = link.link.split('#')[0]; // 移除锚点
                const targetFile = fileMap.get(linkText);
                
                if (targetFile) {
                    stat.validLinks++;
                    stat.linkTargets.add(targetFile.path);
                    
                    // 记录反向链接
                    if (!linkStats.has(targetFile.path)) {
                        linkStats.set(targetFile.path, {
                            file: targetFile.path,
                            name: targetFile.basename,
                            outlinks: 0,
                            inlinks: 0,
                            validLinks: 0,
                            brokenLinks: 0,
                            linkTargets: new Set(),
                            linkSources: new Set()
                        });
                    }
                    
                    const targetStat = linkStats.get(targetFile.path);
                    targetStat.inlinks++;
                    targetStat.linkSources.add(file.path);
                } else {
                    stat.brokenLinks++;
                    if (includeBrokenLinks) {
                        brokenLinks.add({
                            source: file.path,
                            target: linkText,
                            line: link.position?.start?.line
                        });
                    }
                }
            }
        }
        
        // 计算连接度指标
        const linkAnalysis = Array.from(linkStats.values())
            .filter(stat => stat.outlinks + stat.inlinks >= minConnections)
            .map(stat => ({
                ...stat,
                totalConnections: stat.outlinks + stat.inlinks,
                connectionRatio: stat.inlinks / Math.max(stat.outlinks, 1),
                linkTargets: Array.from(stat.linkTargets),
                linkSources: Array.from(stat.linkSources)
            }))
            .sort((a, b) => b.totalConnections - a.totalConnections);
        
        return {
            linkStats: linkAnalysis,
            brokenLinks: Array.from(brokenLinks),
            totalFiles: files.length,
            connectedFiles: linkAnalysis.length,
            isolatedFiles: files.length - linkAnalysis.length,
            avgConnections: linkAnalysis.reduce((sum, stat) => sum + stat.totalConnections, 0) / linkAnalysis.length
        };
    }

    /**
     * 文件夹统计（优化版）
     */
    async getFolderStats(config = {}) {
        const { includeSubfolders = true, minFiles = 1 } = config;
        
        const folderStats = new Map();
        const files = app.vault.getMarkdownFiles();
        
        for (const file of files) {
            const folderPath = file.parent?.path || 'root';
            
            if (!folderStats.has(folderPath)) {
                folderStats.set(folderPath, {
                    path: folderPath,
                    name: file.parent?.name || 'Root',
                    fileCount: 0,
                    totalSize: 0,
                    avgSize: 0,
                    lastModified: 0,
                    tags: new Set(),
                    extensions: new Map()
                });
            }
            
            const stat = folderStats.get(folderPath);
            stat.fileCount++;
            stat.totalSize += file.stat.size;
            stat.lastModified = Math.max(stat.lastModified, file.stat.mtime);
            
            // 统计扩展名
            const ext = file.extension || 'no-ext';
            stat.extensions.set(ext, (stat.extensions.get(ext) || 0) + 1);
            
            // 收集标签
            const metadata = app.metadataCache.getFileCache(file);
            const tags = metadata?.tags?.map(t => t.tag) || [];
            tags.forEach(tag => stat.tags.add(tag));
        }
        
        // 计算平均值并转换数据
        const result = Array.from(folderStats.values())
            .filter(stat => stat.fileCount >= minFiles)
            .map(stat => ({
                ...stat,
                avgSize: Math.round(stat.totalSize / stat.fileCount),
                tags: Array.from(stat.tags),
                extensions: Array.from(stat.extensions.entries()),
                lastModified: new Date(stat.lastModified)
            }))
            .sort((a, b) => b.fileCount - a.fileCount);
        
        return result;
    }

    /**
     * 内容统计（优化版）
     */
    async getContentStats(config = {}) {
        const { 
            sampleSize = 100,
            includeWordCount = true,
            includeReadingTime = true 
        } = config;
        
        const files = app.vault.getMarkdownFiles();
        const sampleFiles = files.slice(0, sampleSize);
        
        const stats = {
            totalFiles: files.length,
            sampledFiles: sampleFiles.length,
            totalWords: 0,
            avgWords: 0,
            totalReadingTime: 0,
            avgReadingTime: 0,
            wordDistribution: {
                short: 0,    // < 500 words
                medium: 0,   // 500-2000 words
                long: 0,     // 2000-5000 words
                veryLong: 0  // > 5000 words
            },
            contentTypes: new Map()
        };
        
        for (const file of sampleFiles) {
            try {
                const content = await app.vault.read(file);
                const wordCount = this.countWords(content);
                const readingTime = Math.ceil(wordCount / 200); // 200 words per minute
                
                stats.totalWords += wordCount;
                stats.totalReadingTime += readingTime;
                
                // 分类文档长度
                if (wordCount < 500) stats.wordDistribution.short++;
                else if (wordCount < 2000) stats.wordDistribution.medium++;
                else if (wordCount < 5000) stats.wordDistribution.long++;
                else stats.wordDistribution.veryLong++;
                
                // 分析内容类型
                const contentType = this.detectContentType(content);
                stats.contentTypes.set(contentType, (stats.contentTypes.get(contentType) || 0) + 1);
                
            } catch (error) {
                console.warn(`无法读取文件: ${file.path}`);
            }
        }
        
        stats.avgWords = Math.round(stats.totalWords / stats.sampledFiles);
        stats.avgReadingTime = Math.round(stats.totalReadingTime / stats.sampledFiles);
        stats.contentTypes = Array.from(stats.contentTypes.entries());
        
        return stats;
    }

    /**
     * 工具方法
     */
    generateCacheKey(config) {
        return JSON.stringify(config);
    }

    getFromCache(key) {
        const cached = this.cache.get(key);
        if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
            return cached.data;
        }
        return null;
    }

    setCache(key, data) {
        this.cache.set(key, {
            data: data,
            timestamp: Date.now()
        });
        
        // 清理过期缓存
        if (this.cache.size > 100) {
            this.cleanupCache();
        }
    }

    cleanupCache() {
        const now = Date.now();
        for (const [key, value] of this.cache.entries()) {
            if (now - value.timestamp > this.cacheTimeout) {
                this.cache.delete(key);
            }
        }
    }

    recordQueryStats(type, duration) {
        if (!this.queryStats.has(type)) {
            this.queryStats.set(type, {
                count: 0,
                totalTime: 0,
                avgTime: 0,
                maxTime: 0,
                minTime: Infinity
            });
        }
        
        const stat = this.queryStats.get(type);
        stat.count++;
        stat.totalTime += duration;
        stat.avgTime = stat.totalTime / stat.count;
        stat.maxTime = Math.max(stat.maxTime, duration);
        stat.minTime = Math.min(stat.minTime, duration);
    }

    generatePreview(content, maxLength = 200) {
        const cleaned = content
            .replace(/^---[\s\S]*?---/, '') // 移除frontmatter
            .replace(/!\[\[.*?\]\]/g, '') // 移除图片
            .replace(/\[\[.*?\]\]/g, '') // 移除链接
            .replace(/#+ /g, '') // 移除标题标记
            .trim();
        
        return cleaned.length > maxLength ? 
            cleaned.substring(0, maxLength) + '...' : 
            cleaned;
    }

    countWords(content) {
        return content
            .replace(/^---[\s\S]*?---/, '')
            .split(/\s+/)
            .filter(word => word.length > 0).length;
    }

    detectContentType(content) {
        if (content.includes('```')) return 'technical';
        if (content.includes('## ') && content.includes('### ')) return 'structured';
        if (content.match(/\d{4}-\d{2}-\d{2}/)) return 'dated';
        if (content.includes('- [ ]') || content.includes('- [x]')) return 'task';
        return 'general';
    }

    buildTagHierarchy(tags) {
        const hierarchy = {};
        
        for (const tagStat of tags) {
            const parts = tagStat.tag.split('/');
            let current = hierarchy;
            
            for (const part of parts) {
                if (!current[part]) {
                    current[part] = {
                        count: 0,
                        children: {}
                    };
                }
                current[part].count += tagStat.count;
                current = current[part].children;
            }
        }
        
        return hierarchy;
    }

    /**
     * 获取性能报告
     */
    getPerformanceReport() {
        return {
            cacheSize: this.cache.size,
            queryStats: Array.from(this.queryStats.entries()).map(([type, stats]) => ({
                type,
                ...stats
            })),
            cacheHitRate: this.calculateCacheHitRate()
        };
    }

    calculateCacheHitRate() {
        // 这里需要实现缓存命中率计算
        return 0.75; // 示例值
    }
}

// 导出全局实例
window.dataviewOptimizer = new DataviewOptimizer();

// 提供便捷的查询方法
window.quickQuery = async (type, config = {}) => {
    return await window.dataviewOptimizer.optimizedQuery({
        type: type,
        ...config
    });
};

if (typeof module !== 'undefined') {
    module.exports = DataviewOptimizer;
}
