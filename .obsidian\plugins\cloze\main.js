/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/main.ts
var main_exports = {};
__export(main_exports, {
  default: () => ClozePlugin
});
module.exports = __toCommonJS(main_exports);
var import_obsidian3 = require("obsidian");

// src/lang/en.ts
var langs = {
  add_cloze: "Create cloze",
  add_cloze_with_hint: "Create cloze with hint",
  remove_cloze: "Remove cloze",
  toggle_cloze: "Toggle all clozes",
  reveal_more_hint: "More hint",
  setting_selector_tag: "Required tag",
  setting_selector_tag_desc: "If you provide a tag here, the plugin will only activate on notes with that tag i.e. #cloze.",
  setting_hide_by_default: "Hide by default",
  setting_hide_by_default_desc: "Enable this setting, all clozes will be hidden by default when reading the page. \u{1F648}",
  setting_hover_to_reveal: "Hover to reveal",
  setting_hover_to_reveal_desc: "Enable this setting, clozes will be be revealed on hover.",
  setting_auto_convert: "Auto Convert",
  setting_highlight: "Highlighted text",
  setting_highlight_desc: "Enable this setting, all ==highlighted texts== will be converted to cloze.",
  setting_bold: "Bolded text",
  setting_bold_desc: "Enable this setting, all **bolded texts** will be converted to cloze.",
  setting_underline: "Underlined text",
  setting_underline_desc: "Enable this setting, all <u>underlined texts</u> will be converted to cloze.",
  setting_italics: "Italic text",
  setting_italics_desc: "Enable this setting, all *italic texts* and _italic texts_ will be converted to cloze.",
  setting_bracket: "Bracketed text",
  setting_bracket_desc: "Enable this setting, all [bracketed texts] will be converted to cloze.",
  setting_curly_bracket: "Curly bracketed text",
  setting_curly_bracket_desc: "Enable this setting, all {text enclosed in curly brackets} will be converted to cloze.",
  setting_editor_menu: "Editor menu",
  setting_editor_menu_add_cloze: "Display add cloze button",
  setting_editor_menu_add_cloze_with_hint: "Display add cloze with hint button",
  setting_editor_menu_remove_cloze: "Display remove cloze button",
  setting_custom_setting: "Custom settings",
  setting_fixed_cloze_width: "Fixed cloze width",
  setting_fixed_cloze_width_desc: "Enable this setting, clozes will have the same default width, which helps to ensure that the original text length is not revealed.",
  setting_hint: "Hint",
  setting_hint_strategy: "Hint strategy",
  setting_hint_strategy_desc: "If you would like the cloze to automatically display the hint based on its content, you have two strategies to choose from: by text count or by text length percentage.",
  setting_hint_by_count: "By count",
  setting_hint_by_count_desc: "Set this setting, clozes that are in the hidden state will display the first n letters/characters as a hint.",
  setting_hint_by_percentage: "By percentage",
  setting_hint_by_percentage_desc: "Set this setting, clozes that are in the hidden state will display hint based on the percentage of the cloze content. For example, 20% of a cloze containing 10 letters would show 2 of its 1st letters.",
  setting_contact: "Thank you for using Cloze! Any feedback is welcomed"
};
var en_default = langs;

// src/lang/zh.ts
var langs2 = {
  add_cloze: "\u6DFB\u52A0\u586B\u7A7A",
  add_cloze_with_hint: "\u6DFB\u52A0\u5E26\u63D0\u793A\u7684\u586B\u7A7A",
  remove_cloze: "\u79FB\u9664\u586B\u7A7A",
  toggle_cloze: "\u663E/\u9690\u6240\u6709\u586B\u7A7A",
  reveal_more_hint: "\u66F4\u591A\u63D0\u793A",
  setting_selector_tag: "\u4F5C\u7528\u6807\u7B7E",
  setting_selector_tag_desc: "\u8BE5\u63D2\u4EF6\u5C06\u4EC5\u4F5C\u7528\u4E8E\u5E26\u6709\u8BE5\u6807\u7B7E\u7684\u7B14\u8BB0\u4E0A\uFF0C\u4E3A\u7A7A\u5219\u4F5C\u7528\u4E8E\u6240\u6709\u7B14\u8BB0 i.e. #cloze\u3002",
  setting_hide_by_default: "\u9ED8\u8BA4\u9690\u85CF",
  setting_hide_by_default_desc: "\u542F\u7528\u6B64\u8BBE\u7F6E\u540E\uFF0C\u6253\u5F00\u9875\u9762\u65F6\u6240\u6709\u586B\u7A7A\u5185\u5BB9\u5C06\u9ED8\u8BA4\u9690\u85CF\u3002\u{1F648}",
  setting_hover_to_reveal: "\u9F20\u6807\u60AC\u505C\u663E\u793A",
  setting_hover_to_reveal_desc: "\u542F\u7528\u6B64\u8BBE\u7F6E\u540E\uFF0C\u9F20\u6807\u60AC\u505C\u5728\u586B\u7A7A\u4E0A\u65F6\u5C06\u663E\u793A\u5185\u5BB9\u3002",
  setting_auto_convert: "\u81EA\u52A8\u8F6C\u6362",
  setting_highlight: "\u9AD8\u4EAE\u6587\u5B57",
  setting_highlight_desc: "\u542F\u7528\u6B64\u8BBE\u7F6E\u540E\uFF0C\u6240\u6709==\u9AD8\u4EAE\u6587\u5B57==\u4E5F\u5C06\u8F6C\u6362\u4E3A\u586B\u7A7A\u3002",
  setting_bold: "\u7C97\u4F53\u6587\u5B57",
  setting_bold_desc: "\u542F\u7528\u6B64\u8BBE\u7F6E\u540E\uFF0C\u6240\u6709**\u7C97\u4F53\u6587\u5B57**\u4E5F\u5C06\u8F6C\u6362\u4E3A\u586B\u7A7A\u3002",
  setting_underline: "\u4E0B\u5212\u7EBF\u6587\u5B57",
  setting_underline_desc: "\u542F\u7528\u6B64\u8BBE\u7F6E\u540E\uFF0C\u6240\u6709<u>\u4E0B\u5212\u7EBF\u6587\u5B57</u>\u4E5F\u5C06\u8F6C\u6362\u4E3A\u586B\u7A7A\u3002",
  setting_italics: "\u659C\u4F53\u6587\u5B57",
  setting_italics_desc: "\u542F\u7528\u6B64\u8BBE\u7F6E\u540E\uFF0C\u6240\u6709<i>\u659C\u4F53\u6587\u5B57</i>\u4E5F\u5C06\u8F6C\u6362\u4E3A\u586B\u7A7A\u3002",
  setting_bracket: "\u62EC\u53F7\u6587\u5B57",
  setting_bracket_desc: "\u542F\u7528\u6B64\u8BBE\u7F6E\u540E\uFF0C\u6240\u6709[\u62EC\u53F7\u6587\u5B57]\u4E5F\u5C06\u8F6C\u6362\u4E3A\u586B\u7A7A\u3002",
  setting_curly_bracket: "\u82B1\u62EC\u53F7\u6587\u5B57",
  setting_curly_bracket_desc: "\u542F\u7528\u6B64\u8BBE\u7F6E\u540E\uFF0C\u6240\u6709{\u82B1\u62EC\u53F7\u6587\u5B57}\u4E5F\u5C06\u8F6C\u6362\u4E3A\u586B\u7A7A\u3002",
  setting_editor_menu: "\u7F16\u8F91\u5668\u83DC\u5355",
  setting_editor_menu_add_cloze: "\u663E\u793A\u6DFB\u52A0\u586B\u7A7A\u6309\u94AE",
  setting_editor_menu_add_cloze_with_hint: "\u663E\u793A\u6DFB\u52A0\u5E26\u63D0\u793A\u7684\u586B\u7A7A\u6309\u94AE",
  setting_editor_menu_remove_cloze: "\u663E\u793A\u79FB\u9664\u586B\u7A7A\u6309\u94AE",
  setting_custom_setting: "\u81EA\u5B9A\u4E49\u8BBE\u7F6E",
  setting_fixed_cloze_width: "\u56FA\u5B9A\u586B\u7A7A\u5BBD\u5EA6",
  setting_fixed_cloze_width_desc: "\u542F\u7528\u6B64\u8BBE\u7F6E\u540E\uFF0C\u6240\u6709\u586B\u7A7A\u7684\u5BBD\u5EA6\u9ED8\u8BA4\u76F8\u540C\uFF08\u53EF\u907F\u514D\u900F\u9732\u539F\u6587\u5B57\u957F\u5EA6\uFF09\u3002",
  setting_hint: "\u63D0\u793A",
  setting_hint_strategy: "\u63D0\u793A\u7B56\u7565",
  setting_hint_strategy_desc: "\u5982\u679C\u4F60\u5E0C\u671B\u586B\u7A7A\u9ED8\u8BA4\u5C55\u793A\u63D0\u793A\uFF0C\u53EF\u9009\u62E9\u81EA\u52A8\u63D0\u793A\u7B56\u7565\uFF1A\u9996\u5B57\u6BCD\u6216\u662F\u767E\u5206\u6BD4\u3002",
  setting_hint_by_count: "\u63D0\u793A\u5B57\u6570",
  setting_hint_by_count_desc: "\u586B\u7A7A\u5728\u9690\u85CF\u72B6\u6001\u4E0B\u4F1A\u5C55\u793A\u539F\u4F4D\u7684\u9996n\u4E2A\u5B57\u7B26\u3002",
  setting_hint_by_percentage: "\u63D0\u793A\u767E\u5206\u6BD4",
  setting_hint_by_percentage_desc: "\u586B\u7A7A\u5728\u9690\u85CF\u72B6\u6001\u4E0B\u4F1A\u5C55\u793A\u539F\u4F4D\u7684\u9996n%\u4E2A\u5B57\u7B26\u3002",
  setting_contact: "\u8C22\u8C22\u4F60\u7684\u4F7F\u7528~ \u6B22\u8FCE\u53CD\u9988\uFF01\u6233\u8FD9\u91CC\uFF1A"
};
var zh_default = langs2;

// src/lang/index.ts
var langs3 = {
  en: en_default,
  zh: zh_default
};
var language = window.localStorage.getItem("language") || "en";
var lang_default = langs3[language] || en_default;

// src/settings/settingData.ts
var HINT_STRATEGY = {
  none: 0,
  count: 1,
  percentage: 2
};
var DEFAULT_SETTINGS = {
  defaultHide: true,
  hoverToReveal: false,
  selectorTag: "#",
  includeHighlighted: false,
  includeUnderlined: false,
  includeBolded: false,
  includeItalics: false,
  includeBracketed: false,
  includeCurlyBrackets: false,
  fixedClozeWidth: false,
  editorMenuAddCloze: true,
  editorMenuAddClozeWithHint: true,
  editorMenuRemoveCloze: true,
  hintStrategy: HINT_STRATEGY.none,
  hintCount: 2,
  hintPercentage: 0.2
  // 20%
};
var settingData_default = DEFAULT_SETTINGS;

// src/settings/settingTab.ts
var import_obsidian = require("obsidian");
var SettingTab = class extends import_obsidian.PluginSettingTab {
  constructor(app, plugin) {
    super(app, plugin);
    this.plugin = plugin;
  }
  display() {
    const { containerEl } = this;
    containerEl.empty();
    containerEl.createEl("h1", { text: "Cloze" });
    this.displayAutoConvert(containerEl);
    this.displayCustomSetting(containerEl);
    this.displayHintSetting(containerEl);
    this.displayEditorMenuSetting(containerEl);
    this.displayContact(containerEl);
  }
  displayAutoConvert(containerEl) {
    containerEl.createEl("h2", { text: lang_default.setting_auto_convert });
    new import_obsidian.Setting(containerEl).setName(lang_default.setting_highlight).setDesc(lang_default.setting_highlight_desc).addToggle((toggle) => toggle.setValue(this.plugin.settings.includeHighlighted).onChange((value) => {
      this.plugin.settings.includeHighlighted = value;
      this.plugin.saveSettings();
    }));
    new import_obsidian.Setting(containerEl).setName(lang_default.setting_bold).setDesc(lang_default.setting_bold_desc).addToggle((toggle) => toggle.setValue(this.plugin.settings.includeBolded).onChange((value) => {
      this.plugin.settings.includeBolded = value;
      this.plugin.saveSettings();
    }));
    new import_obsidian.Setting(containerEl).setName(lang_default.setting_underline).setDesc(lang_default.setting_underline_desc).addToggle((toggle) => toggle.setValue(this.plugin.settings.includeUnderlined).onChange((value) => {
      this.plugin.settings.includeUnderlined = value;
      this.plugin.saveSettings();
    }));
    new import_obsidian.Setting(containerEl).setName(lang_default.setting_italics).setDesc(lang_default.setting_italics_desc).addToggle((toggle) => toggle.setValue(this.plugin.settings.includeItalics).onChange((value) => {
      this.plugin.settings.includeItalics = value;
      this.plugin.saveSettings();
    }));
    new import_obsidian.Setting(containerEl).setName(lang_default.setting_bracket).setDesc(lang_default.setting_bracket_desc).addToggle((toggle) => toggle.setValue(this.plugin.settings.includeBracketed).onChange((value) => {
      this.plugin.settings.includeBracketed = value;
      this.plugin.saveSettings();
    }));
    new import_obsidian.Setting(containerEl).setName(lang_default.setting_curly_bracket).setDesc(lang_default.setting_curly_bracket_desc).addToggle((toggle) => toggle.setValue(this.plugin.settings.includeCurlyBrackets).onChange((value) => {
      this.plugin.settings.includeCurlyBrackets = value;
      this.plugin.saveSettings();
    }));
  }
  displayCustomSetting(containerEl) {
    containerEl.createEl("h2", { text: lang_default.setting_custom_setting });
    new import_obsidian.Setting(containerEl).setName(lang_default.setting_selector_tag).setDesc(lang_default.setting_selector_tag_desc).addText((text) => text.setValue(this.plugin.settings.selectorTag).onChange(async (value) => {
      this.plugin.settings.selectorTag = this.sanitizeTag(value);
      await this.plugin.saveSettings();
    }));
    new import_obsidian.Setting(containerEl).setName(lang_default.setting_hide_by_default).setDesc(lang_default.setting_hide_by_default_desc).addToggle((toggle) => toggle.setValue(this.plugin.settings.defaultHide).onChange((value) => {
      this.plugin.settings.defaultHide = value;
      this.plugin.saveSettings();
    }));
    new import_obsidian.Setting(containerEl).setName(lang_default.setting_hover_to_reveal).setDesc(lang_default.setting_hover_to_reveal_desc).addToggle((toggle) => toggle.setValue(this.plugin.settings.hoverToReveal).onChange((value) => {
      this.plugin.settings.hoverToReveal = value;
      this.plugin.saveSettings();
    }));
    new import_obsidian.Setting(containerEl).setName(lang_default.setting_fixed_cloze_width).setDesc(lang_default.setting_fixed_cloze_width_desc).addToggle((toggle) => toggle.setValue(this.plugin.settings.fixedClozeWidth).onChange((value) => {
      this.plugin.settings.fixedClozeWidth = value;
      this.plugin.saveSettings();
    }));
  }
  displayHintSetting(containerEl) {
    const settingEl = containerEl.createEl("div");
    settingEl.createEl("h2", { text: "Hint" });
    new import_obsidian.Setting(settingEl).setName(lang_default.setting_hint_strategy).setDesc(lang_default.setting_hint_strategy_desc).addDropdown((comp) => {
      comp.addOptions({ [HINT_STRATEGY.none]: "Off", [HINT_STRATEGY.count]: "By Count", [HINT_STRATEGY.percentage]: "By Percentage" });
      comp.setValue(this.plugin.settings.hintStrategy.toString());
      comp.onChange((val) => {
        this.plugin.settings.hintStrategy = Number(val);
        initHintStrategyVaule(Number(val));
        this.plugin.saveSettings();
      });
    });
    let hintStrategySetting;
    const initHintStrategyVaule = (strategy) => {
      var _a;
      if (hintStrategySetting)
        (_a = hintStrategySetting.settingEl.parentElement) == null ? void 0 : _a.removeChild(hintStrategySetting.settingEl);
      switch (strategy) {
        case HINT_STRATEGY.none:
          break;
        case HINT_STRATEGY.count:
          hintStrategySetting = new import_obsidian.Setting(settingEl).setName(lang_default.setting_hint_by_count).setDesc(lang_default.setting_hint_by_count_desc).addText((text) => {
            text.setValue(this.plugin.settings.hintCount.toString()).onChange(async (value) => {
              const valueNumber = Number(value);
              if (isNaN(valueNumber))
                return;
              this.plugin.settings.hintCount = valueNumber;
              this.plugin.saveSettings();
            });
          });
          break;
        case HINT_STRATEGY.percentage:
          hintStrategySetting = new import_obsidian.Setting(settingEl).setName(lang_default.setting_hint_by_percentage).setDesc(lang_default.setting_hint_by_percentage_desc).addText((text) => {
            text.setValue(this.plugin.settings.hintPercentage * 100 + "%").onChange(async (value) => {
              const matches = value.match(/^(\d+)%$/);
              if (!matches)
                return;
              const valueNumber = Number(matches[1]) / 100;
              if (isNaN(valueNumber))
                return;
              this.plugin.settings.hintPercentage = valueNumber;
              this.plugin.saveSettings();
            });
          });
          break;
      }
    };
    initHintStrategyVaule(Number(this.plugin.settings.hintStrategy));
  }
  displayEditorMenuSetting(containerEl) {
    containerEl.createEl("h2", { text: lang_default.setting_editor_menu });
    new import_obsidian.Setting(containerEl).setName(lang_default.setting_editor_menu_add_cloze).addToggle((toggle) => toggle.setValue(this.plugin.settings.editorMenuAddCloze).onChange((value) => {
      this.plugin.settings.editorMenuAddCloze = value;
      this.plugin.saveSettings();
    }));
    new import_obsidian.Setting(containerEl).setName(lang_default.setting_editor_menu_add_cloze_with_hint).addToggle((toggle) => toggle.setValue(this.plugin.settings.editorMenuAddClozeWithHint).onChange((value) => {
      this.plugin.settings.editorMenuAddClozeWithHint = value;
      this.plugin.saveSettings();
    }));
    new import_obsidian.Setting(containerEl).setName(lang_default.setting_editor_menu_remove_cloze).addToggle((toggle) => toggle.setValue(this.plugin.settings.editorMenuRemoveCloze).onChange((value) => {
      this.plugin.settings.editorMenuRemoveCloze = value;
      this.plugin.saveSettings();
    }));
  }
  displayContact(containerEl) {
    containerEl.createEl("p", {
      text: lang_default.setting_contact + " ",
      cls: "setting-item-description"
    }).createEl("a", {
      text: "here",
      href: "https://github.com/DearVikki/obsidian-cloze-plugin/issues"
    });
  }
  // Check and clean up tags that are not (what I understand to be) well formed Obsidian tags.
  sanitizeTag(tagInput) {
    const allowedCharacters = /^[a-zA-Z0-9-_]+$/;
    const tagBody = tagInput.startsWith("#") ? tagInput.slice(1) : tagInput;
    if (allowedCharacters.test(tagBody)) {
      return "#" + tagBody;
    }
    const sanitizedTagBody = tagBody.replace(/[^a-zA-Z0-9-_]/g, "_");
    return "#" + sanitizedTagBody;
  }
};
var settingTab_default = SettingTab;

// src/components/modal-hint.ts
var import_obsidian2 = require("obsidian");
var HintModal = class extends import_obsidian2.Modal {
  constructor(app, clozedText, onSubmit) {
    super(app);
    this.clozedText = clozedText;
    this.onSubmit = onSubmit;
  }
  onOpen() {
    const { contentEl } = this;
    contentEl.createEl("p", { text: "Clozed text: " + this.clozedText });
    new import_obsidian2.Setting(contentEl).setClass("modal-hint-setting").setName("Hint text: ").addText((text) => text.onChange((value) => {
      this.result = value;
    }));
    new import_obsidian2.Setting(contentEl).addButton((btn) => btn.setButtonText("Submit").setCta().onClick(() => {
      this.close();
      this.onSubmit(this.result);
    }));
  }
  onClose() {
    const { contentEl } = this;
    contentEl.empty();
  }
};

// src/const.ts
var ATTRS = {
  hide: "data-cloze-hide",
  hint: "data-cloze-hint",
  hover: "data-cloze-hover",
  content: "data-cloze-content"
};
var CLASSES = {
  cloze: "cloze",
  clozeContent: "cloze-content",
  highlight: "cloze-highlight",
  bold: "cloze-bold",
  underline: "cloze-underline",
  hint: "cloze-hint",
  fixedWidth: "cloze-fixed-width",
  colzeHide: "cloze-hide"
};

// src/utils.ts
var utils = {
  getClozeEl: (target) => {
    return target.closest("." + CLASSES.cloze);
  },
  getClozeContentEl: (target) => {
    return target.querySelector("." + CLASSES.clozeContent);
  },
  getClozeHintEl: (target) => {
    return target.querySelector("." + CLASSES.hint);
  },
  getClozeContent: (clozeEl) => {
    const $content = clozeEl.querySelector("." + CLASSES.clozeContent);
    if ($content) {
      return $content.textContent || "";
    }
    return "";
  },
  hasCustomHint: (clozeEl) => {
    return !!clozeEl.getAttribute(ATTRS.hint);
  },
  getClozeCustomHint: (clozeEl) => {
    return clozeEl.getAttribute(ATTRS.hint) || "";
  },
  getClozeCurrentHint: (clozeEl) => {
    const $hint = clozeEl.querySelector("." + CLASSES.hint);
    if ($hint) {
      return $hint.textContent || "";
    }
    return "";
  },
  setClozeHint: (clozeEl, hint) => {
    const $hint = utils.getClozeHintEl(clozeEl);
    if (!$hint || hint === void 0)
      return;
    $hint.textContent = hint;
    if ($hint.parentElement)
      $hint.parentElement.title = hint;
  },
  isClozeHide: (clozeEl) => {
    return !!clozeEl.getAttribute(ATTRS.hide);
  }
};
var utils_default = utils;

// src/main.ts
var ClozePlugin = class extends import_obsidian3.Plugin {
  constructor(app, manifest) {
    super(app, manifest);
    this.isSourceHide = false;
    this.isPreviewHide = true;
    this.clozeSelector = () => {
      const selectors = [".cloze-span"];
      if (this.settings.includeHighlighted) {
        selectors.push("mark");
        selectors.push(".cm-highlight");
      }
      if (this.settings.includeUnderlined) {
        selectors.push("u");
      }
      if (this.settings.includeBolded) {
        selectors.push("strong");
        selectors.push(".cm-strong");
      }
      if (this.settings.includeItalics) {
        selectors.push("em");
        selectors.push(".cm-em");
      }
      return selectors.join(", ");
    };
    this.transformBracketedText = (element) => {
      const items = element.querySelectorAll("p, h1, h2, h3, h4, h5, li, td, th, code");
      items.forEach((item) => {
        item.innerHTML = item.innerHTML.replace(/\[(.*?)\]/g, '<span class="cloze-span">$1</span>');
      });
    };
    this.transformCurlyBracketedText = (element) => {
      const items = element.querySelectorAll("p, h1, h2, h3, h4, h5, li, td, th, code");
      items.forEach((item) => {
        item.innerHTML = item.innerHTML.replace(/\{(.*?)\}/g, '<span class="cloze-span">$1</span>');
      });
    };
    this.renderCloze = ($cloze) => {
      $cloze.classList.add(CLASSES.cloze);
      $cloze.innerHTML = `<span class="cloze-hint"></span><span class="cloze-content">${$cloze.innerHTML}</span>`;
      this.initHint($cloze);
    };
    this.initHint = ($cloze) => {
      let hint = "";
      if (utils_default.hasCustomHint($cloze)) {
        hint = utils_default.getClozeCustomHint($cloze);
      } else {
        const textContent = utils_default.getClozeContent($cloze);
        if (this.settings.hintStrategy === HINT_STRATEGY.count) {
          hint = textContent.slice(0, this.settings.hintCount);
        } else if (this.settings.hintStrategy === HINT_STRATEGY.percentage) {
          hint = textContent.slice(0, Math.ceil(textContent.length * this.settings.hintPercentage));
        }
      }
      utils_default.setClozeHint($cloze, hint);
    };
    // ----------- cloze interaction ------------
    this.hideClozeContent = (target) => {
      if (!target)
        return;
      if (!target.getAttribute(ATTRS.hide)) {
        target.setAttribute(ATTRS.hide, "true");
      }
      this.updateClozeClass(target);
      this.initHint(target);
    };
    this.showClozeContent = (target) => {
      if (!target)
        return;
      if (target.getAttribute(ATTRS.hide)) {
        target.removeAttribute(ATTRS.hide);
      }
      this.updateClozeClass(target);
    };
    this.setClozeOnHover = (target, hoverState) => {
      if (!target)
        return;
      if (hoverState) {
        target.setAttribute(ATTRS.hover, "true");
      } else {
        target.removeAttribute(ATTRS.hover);
      }
      this.updateClozeClass(target);
    };
    this.updateClozeClass = (target) => {
      if (target.getAttribute(ATTRS.hover) || !target.getAttribute(ATTRS.hide)) {
        target.classList.remove(CLASSES.colzeHide);
      } else {
        target.classList.add(CLASSES.colzeHide);
      }
    };
    this.addCloze = (editor, needHint) => {
      const currentStr = editor.getSelection();
      const content = currentStr.replace(/<span class="cloze-span">(.*?)<\/span>/g, "$1");
      if (needHint) {
        new HintModal(this.app, content, (hint) => {
          const newStr = `<span class="cloze-span" data-cloze-hint="${hint}">` + content + "</span>";
          editor.replaceSelection(newStr);
          editor.blur();
        }).open();
      } else {
        const newStr = '<span class="cloze-span">' + content + "</span>";
        editor.replaceSelection(newStr);
        editor.blur();
      }
    };
    this.removeCloze = (editor) => {
      const currentStr = editor.getSelection();
      const newStr = currentStr.replace(/<span.*?class="cloze-span".*?>(.*?)<\/span>/g, "$1");
      editor.replaceSelection(newStr);
    };
    this.revealMoreHint = ($cloze) => {
      const currentHint = utils_default.getClozeCurrentHint($cloze);
      const hintLength = currentHint.length + 3;
      utils_default.setClozeHint($cloze, utils_default.getClozeContent($cloze).slice(0, hintLength));
    };
  }
  async onload() {
    console.log("load cloze plugin");
    await this.loadSettings();
    this.addSettingTab(new settingTab_default(this.app, this));
    this.initRibbon();
    this.initEditorMenu();
    this.initCommand();
    this.initMarkdownPostProcessor();
    this.initPageClickEvent();
    this.initNewWindowPageClickEvent();
  }
  initRibbon() {
    this.addRibbonIcon("fish", lang_default.toggle_cloze, (evt) => {
      if (this.checkTags()) {
        this.togglePageAllHide();
      }
    });
  }
  initEditorMenu() {
    this.registerEvent(
      this.app.workspace.on("editor-menu", (menu, editor) => {
        const selection = editor.getSelection();
        if (selection && this.checkTags()) {
          if (this.settings.editorMenuAddCloze) {
            menu.addItem((item) => {
              item.setTitle(lang_default.add_cloze).onClick((e) => {
                this.addCloze(editor);
              });
            });
          }
          if (this.settings.editorMenuAddClozeWithHint) {
            menu.addItem((item) => {
              item.setTitle(lang_default.add_cloze_with_hint).onClick((e) => {
                this.addCloze(editor, true);
              });
            });
          }
          if (this.settings.editorMenuRemoveCloze) {
            menu.addItem((item) => {
              item.setTitle(lang_default.remove_cloze).onClick((e) => {
                this.removeCloze(editor);
              });
            });
          }
        }
      })
    );
  }
  initCommand() {
    this.addCommand({
      id: "add-cloze",
      name: lang_default.add_cloze,
      icon: "fish",
      editorCallback: (editor, ctx) => {
        const selection = editor.getSelection();
        if (selection && this.checkTags()) {
          this.addCloze(editor);
        }
      }
    });
    this.addCommand({
      id: "add-cloze-with-hint",
      name: lang_default.add_cloze_with_hint,
      icon: "fish-symbol",
      editorCallback: (editor, ctx) => {
        const selection = editor.getSelection();
        if (selection && this.checkTags()) {
          this.addCloze(editor, true);
        }
      }
    });
    this.addCommand({
      id: "remove-cloze",
      name: lang_default.remove_cloze,
      icon: "fish-off",
      editorCallback: (editor, ctx) => {
        const selection = editor.getSelection();
        if (selection && this.checkTags()) {
          this.removeCloze(editor);
        }
      }
    });
    this.addCommand({
      id: "toggle-cloze",
      name: lang_default.toggle_cloze,
      callback: () => {
        if (this.checkTags()) {
          this.togglePageAllHide();
        }
      }
    });
  }
  initMarkdownPostProcessor() {
    this.registerMarkdownPostProcessor((element, context) => {
      if (!this.checkTags()) {
        return;
      }
      if (this.settings.fixedClozeWidth) {
        const containerEl = context.containerEl;
        if (containerEl) {
          containerEl.classList.add(CLASSES.fixedWidth);
        } else {
          new import_obsidian3.Notice("Cloze plugin: No containerEl.");
        }
      }
      if (this.settings.includeBracketed) {
        this.transformBracketedText(element);
      }
      if (this.settings.includeCurlyBrackets) {
        this.transformCurlyBracketedText(element);
      }
      element.querySelectorAll(this.clozeSelector()).forEach(($cloze) => {
        this.renderCloze($cloze);
        if (this.settings.hoverToReveal) {
          this.initClozeMouseOverReveal($cloze);
        }
      });
      this.toggleAllHide(element, this.isAllHide());
    });
  }
  initClozeMouseOverReveal($cloze) {
    this.registerDomEvent($cloze, "mouseenter", (event) => {
      if (this.isPreviewMode()) {
        this.setClozeOnHover($cloze, true);
      }
    });
    this.registerDomEvent($cloze, "mouseleave", (event) => {
      if (this.isPreviewMode()) {
        this.setClozeOnHover($cloze, false);
      }
    });
  }
  initPageClickEvent() {
    this.registerDomEvent(document, "click", (event) => {
      if (this.isPreviewMode()) {
        this.toggleHide(utils_default.getClozeEl(event.target));
      }
    });
    this.registerDomEvent(document, "contextmenu", (event) => {
      if (this.isPreviewMode()) {
        this.onRightClick(event, utils_default.getClozeEl(event.target));
      }
    });
  }
  // init for new window
  initNewWindowPageClickEvent() {
    const handler = (event) => {
      this.toggleHide(utils_default.getClozeEl(event.target));
    };
    this.app.workspace.on("window-open", (a, win) => {
      if (win !== null) {
        win.document.addEventListener("click", handler);
      }
    });
    this.app.workspace.on("window-close", (a, win) => {
      if (win !== null) {
        win.document.removeEventListener("click", handler);
      }
    });
  }
  onRightClick(event, $cloze) {
    if (!$cloze)
      return;
    if (!utils_default.isClozeHide($cloze))
      return;
    if (utils_default.hasCustomHint($cloze))
      return;
    const menu = new import_obsidian3.Menu();
    menu.addItem(
      (item) => item.setTitle(en_default.reveal_more_hint).setIcon("snail").onClick(() => {
        this.revealMoreHint($cloze);
      })
    );
    menu.showAtMouseEvent(event);
  }
  isPreviewMode() {
    const view = this.app.workspace.getActiveViewOfType(import_obsidian3.MarkdownView);
    if (view == null)
      return true;
    return view.getMode() === "preview";
  }
  isAllHide() {
    return this.isPreviewMode() ? this.isPreviewHide : this.isSourceHide;
  }
  // Extract and verify tags - works in both preview and edit mode
  checkTags() {
    var _a;
    if (this.settings.selectorTag === "" || this.settings.selectorTag === "#") {
      return true;
    }
    const activeView = this.app.workspace.getActiveViewOfType(import_obsidian3.MarkdownView);
    if (activeView) {
      const { app, file } = activeView;
      if (file) {
        const cachedMetadata = app.metadataCache.getFileCache(file);
        const tags = ((cachedMetadata == null ? void 0 : cachedMetadata.tags) || []).map((t) => t.tag);
        const frontmatterTags = ((_a = cachedMetadata == null ? void 0 : cachedMetadata.frontmatter) == null ? void 0 : _a.tags) || [];
        return [...frontmatterTags, ...tags].some((t) => {
          if (!t.startsWith("#")) {
            t = "#" + t;
          }
          return t.toLowerCase() === this.settings.selectorTag.toLowerCase();
        });
      }
    }
    return false;
  }
  async loadSettings() {
    this.settings = Object.assign({}, settingData_default, await this.loadData());
    this.isPreviewHide = this.settings.defaultHide;
  }
  async saveSettings() {
    await this.saveData(this.settings);
    this.isPreviewHide = this.settings.defaultHide;
  }
  toggleHide(target) {
    if (!target)
      return;
    if (target.getAttribute(ATTRS.hide)) {
      this.showClozeContent(target);
    } else {
      this.hideClozeContent(target);
    }
  }
  toggleAllHide(dom = document, hide) {
    if (dom && this.checkTags()) {
      const marks = dom.querySelectorAll(this.clozeSelector());
      if (hide) {
        marks.forEach((mark) => {
          this.hideClozeContent(mark);
        });
      } else {
        marks.forEach((mark) => {
          this.showClozeContent(mark);
        });
      }
    }
  }
  togglePageAllHide() {
    const mostRecentLeaf = this.app.workspace.getMostRecentLeaf();
    if (!mostRecentLeaf)
      return;
    const leafContainer = mostRecentLeaf.containerEl;
    if (!leafContainer)
      return;
    if (this.isPreviewMode()) {
      const nodeContainers = leafContainer.querySelectorAll(".markdown-preview-view");
      nodeContainers.forEach((nodeContainer) => {
        this.toggleAllHide(nodeContainer, !this.isPreviewHide);
      });
      this.isPreviewHide = !this.isPreviewHide;
    } else {
      const nodeContainers = leafContainer.querySelectorAll(".markdown-source-view");
      nodeContainers.forEach((nodeContainer) => {
        this.toggleAllHide(nodeContainer, !this.isSourceHide);
      });
      this.isSourceHide = !this.isSourceHide;
    }
  }
};

/* nosourcemap */