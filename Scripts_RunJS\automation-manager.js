/**
 * 基础自动化管理器
 * 提供自动化任务调度和管理功能
 */

class AutomationManager {
    constructor() {
        this.tasks = new Map();
        this.scheduledTasks = new Map();
        this.isRunning = false;
        this.config = {
            autoStart: true,
            checkInterval: 30000, // 30秒检查一次
            maxConcurrentTasks: 3,
            logLevel: 'info'
        };
        this.taskHistory = [];
        this.currentTasks = new Set();
    }

    /**
     * 初始化自动化系统
     */
    async initialize() {
        console.log('🤖 初始化自动化管理器...');
        
        // 注册基础自动化任务
        this.registerBasicTasks();
        
        // 加载用户配置
        await this.loadConfig();
        
        // 启动调度器
        if (this.config.autoStart) {
            this.start();
        }
        
        console.log('✅ 自动化管理器初始化完成');
    }

    /**
     * 注册基础自动化任务
     */
    registerBasicTasks() {
        // 1. 文件索引更新任务
        this.registerTask('update-file-index', {
            name: '更新文件索引',
            description: '定期更新文件索引以提高搜索性能',
            schedule: 'every-5-minutes',
            priority: 'high',
            action: async () => {
                if (window.fileIndexSystem) {
                    await window.fileIndexSystem.updateIncrementalIndex();
                    return { status: 'success', message: '文件索引已更新' };
                }
                return { status: 'skipped', message: '文件索引系统未初始化' };
            }
        });

        // 2. 缓存清理任务
        this.registerTask('cleanup-cache', {
            name: '清理缓存',
            description: '清理过期的缓存数据',
            schedule: 'every-hour',
            priority: 'medium',
            action: async () => {
                let cleaned = 0;
                
                // 清理Dataview缓存
                if (window.dataviewOptimizer) {
                    window.dataviewOptimizer.cleanupCache();
                    cleaned++;
                }
                
                // 清理其他缓存
                if (window.fileIndexSystem) {
                    // 这里可以添加文件索引的缓存清理
                    cleaned++;
                }
                
                return { 
                    status: 'success', 
                    message: `已清理 ${cleaned} 个缓存组件` 
                };
            }
        });

        // 3. 断链检查任务
        this.registerTask('check-broken-links', {
            name: '检查断链',
            description: '检查并报告断开的链接',
            schedule: 'daily',
            priority: 'medium',
            action: async () => {
                try {
                    const linkAnalysis = await window.quickQuery('link-analysis', {
                        includeBrokenLinks: true
                    });
                    
                    const brokenCount = linkAnalysis.brokenLinks.length;
                    
                    if (brokenCount > 0) {
                        // 创建断链报告
                        await this.createBrokenLinksReport(linkAnalysis.brokenLinks);
                        
                        return {
                            status: 'warning',
                            message: `发现 ${brokenCount} 个断链，已生成报告`
                        };
                    }
                    
                    return {
                        status: 'success',
                        message: '未发现断链'
                    };
                } catch (error) {
                    return {
                        status: 'error',
                        message: `断链检查失败: ${error.message}`
                    };
                }
            }
        });

        // 4. 标签整理建议任务
        this.registerTask('tag-cleanup-suggestions', {
            name: '标签整理建议',
            description: '分析标签使用情况并提供整理建议',
            schedule: 'weekly',
            priority: 'low',
            action: async () => {
                try {
                    const tagAnalysis = await window.quickQuery('tag-analysis', {
                        minUsage: 1,
                        includeCooccurrence: true
                    });
                    
                    const suggestions = this.generateTagSuggestions(tagAnalysis);
                    
                    if (suggestions.length > 0) {
                        await this.createTagSuggestionsReport(suggestions);
                        
                        return {
                            status: 'info',
                            message: `生成了 ${suggestions.length} 条标签整理建议`
                        };
                    }
                    
                    return {
                        status: 'success',
                        message: '标签结构良好，无需整理'
                    };
                } catch (error) {
                    return {
                        status: 'error',
                        message: `标签分析失败: ${error.message}`
                    };
                }
            }
        });

        // 5. 性能监控任务
        this.registerTask('performance-monitor', {
            name: '性能监控',
            description: '监控系统性能并生成报告',
            schedule: 'every-hour',
            priority: 'low',
            action: async () => {
                const performance = {
                    timestamp: Date.now(),
                    memory: this.getMemoryUsage(),
                    fileCount: app.vault.getMarkdownFiles().length,
                    pluginCount: Object.keys(app.plugins.plugins).length,
                    cacheSize: window.dataviewOptimizer?.cache?.size || 0
                };
                
                // 存储性能数据
                this.storePerformanceData(performance);
                
                return {
                    status: 'success',
                    message: '性能数据已记录'
                };
            }
        });

        // 6. 备份提醒任务
        this.registerTask('backup-reminder', {
            name: '备份提醒',
            description: '提醒用户进行数据备份',
            schedule: 'daily',
            priority: 'medium',
            action: async () => {
                const lastBackup = await this.getLastBackupTime();
                const daysSinceBackup = (Date.now() - lastBackup) / (1000 * 60 * 60 * 24);
                
                if (daysSinceBackup > 7) {
                    return {
                        status: 'warning',
                        message: `距离上次备份已过去 ${Math.floor(daysSinceBackup)} 天，建议进行备份`
                    };
                }
                
                return {
                    status: 'success',
                    message: '备份状态正常'
                };
            }
        });
    }

    /**
     * 注册自动化任务
     */
    registerTask(id, taskConfig) {
        this.tasks.set(id, {
            id,
            ...taskConfig,
            registered: Date.now(),
            lastRun: null,
            runCount: 0,
            enabled: true
        });
        
        console.log(`📝 已注册任务: ${taskConfig.name}`);
    }

    /**
     * 启动自动化调度器
     */
    start() {
        if (this.isRunning) {
            console.log('⚠️ 自动化调度器已在运行');
            return;
        }
        
        this.isRunning = true;
        console.log('🚀 启动自动化调度器');
        
        // 主调度循环
        this.scheduleInterval = setInterval(() => {
            this.checkAndRunTasks();
        }, this.config.checkInterval);
        
        // 立即执行一次检查
        setTimeout(() => this.checkAndRunTasks(), 1000);
    }

    /**
     * 停止自动化调度器
     */
    stop() {
        if (!this.isRunning) {
            console.log('⚠️ 自动化调度器未在运行');
            return;
        }
        
        this.isRunning = false;
        
        if (this.scheduleInterval) {
            clearInterval(this.scheduleInterval);
            this.scheduleInterval = null;
        }
        
        console.log('⏹️ 自动化调度器已停止');
    }

    /**
     * 检查并运行到期的任务
     */
    async checkAndRunTasks() {
        if (this.currentTasks.size >= this.config.maxConcurrentTasks) {
            return; // 达到最大并发数
        }
        
        const now = Date.now();
        
        for (const [id, task] of this.tasks) {
            if (!task.enabled || this.currentTasks.has(id)) {
                continue;
            }
            
            if (this.shouldRunTask(task, now)) {
                this.runTask(id);
            }
        }
    }

    /**
     * 判断任务是否应该运行
     */
    shouldRunTask(task, now) {
        if (!task.lastRun) {
            return true; // 从未运行过
        }
        
        const timeSinceLastRun = now - task.lastRun;
        
        switch (task.schedule) {
            case 'every-minute':
                return timeSinceLastRun >= 60 * 1000;
            case 'every-5-minutes':
                return timeSinceLastRun >= 5 * 60 * 1000;
            case 'every-hour':
                return timeSinceLastRun >= 60 * 60 * 1000;
            case 'daily':
                return timeSinceLastRun >= 24 * 60 * 60 * 1000;
            case 'weekly':
                return timeSinceLastRun >= 7 * 24 * 60 * 60 * 1000;
            default:
                return false;
        }
    }

    /**
     * 运行指定任务
     */
    async runTask(taskId) {
        const task = this.tasks.get(taskId);
        if (!task) {
            console.error(`❌ 任务不存在: ${taskId}`);
            return;
        }
        
        this.currentTasks.add(taskId);
        
        const startTime = Date.now();
        console.log(`▶️ 开始执行任务: ${task.name}`);
        
        try {
            const result = await task.action();
            const duration = Date.now() - startTime;
            
            // 更新任务状态
            task.lastRun = startTime;
            task.runCount++;
            
            // 记录历史
            this.taskHistory.push({
                taskId,
                taskName: task.name,
                startTime,
                duration,
                result,
                timestamp: Date.now()
            });
            
            // 限制历史记录数量
            if (this.taskHistory.length > 1000) {
                this.taskHistory = this.taskHistory.slice(-500);
            }
            
            this.logTaskResult(task, result, duration);
            
        } catch (error) {
            console.error(`❌ 任务执行失败: ${task.name}`, error);
            
            this.taskHistory.push({
                taskId,
                taskName: task.name,
                startTime,
                duration: Date.now() - startTime,
                result: { status: 'error', message: error.message },
                timestamp: Date.now()
            });
        } finally {
            this.currentTasks.delete(taskId);
        }
    }

    /**
     * 记录任务结果
     */
    logTaskResult(task, result, duration) {
        const emoji = {
            'success': '✅',
            'warning': '⚠️',
            'error': '❌',
            'info': 'ℹ️',
            'skipped': '⏭️'
        };
        
        const statusEmoji = emoji[result.status] || '❓';
        console.log(`${statusEmoji} 任务完成: ${task.name} (${duration}ms) - ${result.message}`);
    }

    /**
     * 生成标签整理建议
     */
    generateTagSuggestions(tagAnalysis) {
        const suggestions = [];
        
        // 查找单次使用的标签
        const singleUseTags = tagAnalysis.tags.filter(tag => tag.count === 1);
        if (singleUseTags.length > 10) {
            suggestions.push({
                type: 'cleanup',
                priority: 'medium',
                message: `发现 ${singleUseTags.length} 个只使用一次的标签，建议清理`,
                tags: singleUseTags.slice(0, 10).map(t => t.tag)
            });
        }
        
        // 查找相似标签
        const similarTags = this.findSimilarTags(tagAnalysis.tags);
        if (similarTags.length > 0) {
            suggestions.push({
                type: 'merge',
                priority: 'high',
                message: `发现 ${similarTags.length} 组相似标签，建议合并`,
                groups: similarTags
            });
        }
        
        return suggestions;
    }

    /**
     * 查找相似标签
     */
    findSimilarTags(tags) {
        const similar = [];
        
        for (let i = 0; i < tags.length; i++) {
            for (let j = i + 1; j < tags.length; j++) {
                const tag1 = tags[i].tag.toLowerCase();
                const tag2 = tags[j].tag.toLowerCase();
                
                // 简单的相似度检查
                if (this.calculateSimilarity(tag1, tag2) > 0.8) {
                    similar.push([tags[i].tag, tags[j].tag]);
                }
            }
        }
        
        return similar;
    }

    /**
     * 计算字符串相似度
     */
    calculateSimilarity(str1, str2) {
        const longer = str1.length > str2.length ? str1 : str2;
        const shorter = str1.length > str2.length ? str2 : str1;
        
        if (longer.length === 0) return 1.0;
        
        const editDistance = this.levenshteinDistance(longer, shorter);
        return (longer.length - editDistance) / longer.length;
    }

    /**
     * 计算编辑距离
     */
    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }

    /**
     * 创建断链报告
     */
    async createBrokenLinksReport(brokenLinks) {
        const reportContent = `# 断链检查报告

生成时间: ${new Date().toLocaleString()}

## 概要
- 发现断链数量: ${brokenLinks.length}

## 详细列表

${brokenLinks.map(link => `- **${link.source}** → \`${link.target}\``).join('\n')}

## 建议操作
1. 检查目标文件是否存在
2. 确认文件名拼写是否正确
3. 考虑创建缺失的文件
4. 更新链接指向正确的文件

---
*此报告由自动化系统生成*`;

        const reportPath = `Reports/断链检查报告-${new Date().toISOString().split('T')[0]}.md`;
        
        try {
            await app.vault.create(reportPath, reportContent);
            console.log(`📄 断链报告已创建: ${reportPath}`);
        } catch (error) {
            console.error('创建断链报告失败:', error);
        }
    }

    /**
     * 创建标签建议报告
     */
    async createTagSuggestionsReport(suggestions) {
        const reportContent = `# 标签整理建议报告

生成时间: ${new Date().toLocaleString()}

${suggestions.map(suggestion => `
## ${suggestion.type === 'cleanup' ? '清理建议' : '合并建议'}

**优先级**: ${suggestion.priority}
**说明**: ${suggestion.message}

${suggestion.tags ? 
    '**涉及标签**:\n' + suggestion.tags.map(tag => `- ${tag}`).join('\n') :
    '**建议合并**:\n' + suggestion.groups.map(group => `- ${group[0]} → ${group[1]}`).join('\n')
}
`).join('\n')}

---
*此报告由自动化系统生成*`;

        const reportPath = `Reports/标签整理建议-${new Date().toISOString().split('T')[0]}.md`;
        
        try {
            await app.vault.create(reportPath, reportContent);
            console.log(`📄 标签建议报告已创建: ${reportPath}`);
        } catch (error) {
            console.error('创建标签建议报告失败:', error);
        }
    }

    /**
     * 获取内存使用情况
     */
    getMemoryUsage() {
        if (performance.memory) {
            return {
                used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
                total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
                limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
            };
        }
        return null;
    }

    /**
     * 存储性能数据
     */
    storePerformanceData(data) {
        // 这里可以实现性能数据的持久化存储
        console.log('📊 性能数据:', data);
    }

    /**
     * 获取上次备份时间
     */
    async getLastBackupTime() {
        // 这里应该实现实际的备份时间检查
        // 暂时返回一个示例值
        return Date.now() - (3 * 24 * 60 * 60 * 1000); // 3天前
    }

    /**
     * 加载配置
     */
    async loadConfig() {
        try {
            const configPath = '.obsidian/automation-config.json';
            const configData = await app.vault.adapter.read(configPath);
            const config = JSON.parse(configData);
            this.config = { ...this.config, ...config };
            console.log('⚙️ 已加载自动化配置');
        } catch (error) {
            console.log('⚙️ 使用默认配置');
        }
    }

    /**
     * 保存配置
     */
    async saveConfig() {
        try {
            const configPath = '.obsidian/automation-config.json';
            await app.vault.adapter.write(configPath, JSON.stringify(this.config, null, 2));
            console.log('💾 配置已保存');
        } catch (error) {
            console.error('❌ 保存配置失败:', error);
        }
    }

    /**
     * 获取任务状态
     */
    getTaskStatus() {
        return {
            isRunning: this.isRunning,
            totalTasks: this.tasks.size,
            activeTasks: this.currentTasks.size,
            recentHistory: this.taskHistory.slice(-10),
            tasks: Array.from(this.tasks.values()).map(task => ({
                id: task.id,
                name: task.name,
                enabled: task.enabled,
                lastRun: task.lastRun,
                runCount: task.runCount,
                schedule: task.schedule
            }))
        };
    }

    /**
     * 手动运行任务
     */
    async runTaskManually(taskId) {
        const task = this.tasks.get(taskId);
        if (!task) {
            throw new Error(`任务不存在: ${taskId}`);
        }
        
        console.log(`🔧 手动执行任务: ${task.name}`);
        await this.runTask(taskId);
    }

    /**
     * 启用/禁用任务
     */
    toggleTask(taskId, enabled) {
        const task = this.tasks.get(taskId);
        if (task) {
            task.enabled = enabled;
            console.log(`${enabled ? '✅' : '❌'} 任务 ${task.name} 已${enabled ? '启用' : '禁用'}`);
        }
    }
}

// 导出全局实例
window.automationManager = new AutomationManager();

// 自动初始化
if (typeof module !== 'undefined') {
    module.exports = AutomationManager;
} else {
    // 在Obsidian中延迟初始化，确保其他系统已加载
    setTimeout(() => {
        window.automationManager.initialize();
    }, 2000);
}
