/**
 * 高效文件索引系统
 * 用于加速搜索和文件操作
 */

class FileIndexSystem {
    constructor() {
        this.index = new Map();
        this.contentIndex = new Map();
        this.linkIndex = new Map();
        this.tagIndex = new Map();
        this.lastUpdate = null;
        this.indexPath = '.obsidian/file-index.json';
    }

    /**
     * 初始化索引系统
     */
    async initialize() {
        console.log('🚀 初始化文件索引系统...');
        
        try {
            // 尝试加载现有索引
            await this.loadExistingIndex();
            
            // 检查是否需要重建索引
            const needsRebuild = await this.checkIfRebuildNeeded();
            
            if (needsRebuild) {
                console.log('📊 重建文件索引...');
                await this.buildFullIndex();
            } else {
                console.log('✅ 使用现有索引');
                await this.updateIncrementalIndex();
            }
            
            console.log(`📈 索引完成: ${this.index.size} 个文件`);
            
        } catch (error) {
            console.error('❌ 索引初始化失败:', error);
            // 如果加载失败，重建索引
            await this.buildFullIndex();
        }
    }

    /**
     * 构建完整索引
     */
    async buildFullIndex() {
        const startTime = Date.now();
        
        // 清空现有索引
        this.clearIndexes();
        
        // 获取所有文件
        const files = app.vault.getMarkdownFiles();
        console.log(`📁 开始索引 ${files.length} 个文件...`);
        
        let processed = 0;
        
        for (const file of files) {
            try {
                await this.indexFile(file);
                processed++;
                
                // 每100个文件显示进度
                if (processed % 100 === 0) {
                    console.log(`⏳ 已处理 ${processed}/${files.length} 个文件`);
                }
                
            } catch (error) {
                console.warn(`⚠️ 索引文件失败: ${file.path}`, error);
            }
        }
        
        // 保存索引
        await this.saveIndex();
        
        const duration = Date.now() - startTime;
        console.log(`✅ 索引构建完成，耗时 ${duration}ms`);
    }

    /**
     * 索引单个文件
     */
    async indexFile(file) {
        const content = await app.vault.read(file);
        const metadata = app.metadataCache.getFileCache(file);
        
        // 基本文件信息
        const fileInfo = {
            path: file.path,
            name: file.name,
            basename: file.basename,
            extension: file.extension,
            size: content.length,
            created: file.stat.ctime,
            modified: file.stat.mtime,
            folder: file.parent?.path || '',
            
            // 内容摘要
            wordCount: this.countWords(content),
            lineCount: content.split('\n').length,
            
            // 元数据
            tags: metadata?.tags?.map(tag => tag.tag) || [],
            links: metadata?.links?.map(link => link.link) || [],
            headings: metadata?.headings?.map(h => ({
                text: h.heading,
                level: h.level,
                line: h.position.start.line
            })) || [],
            
            // 搜索关键词
            keywords: this.extractKeywords(content),
            
            // 最后索引时间
            indexed: Date.now()
        };
        
        // 存储到各个索引
        this.index.set(file.path, fileInfo);
        this.indexContent(file.path, content);
        this.indexLinks(file.path, fileInfo.links);
        this.indexTags(file.path, fileInfo.tags);
        
        return fileInfo;
    }

    /**
     * 内容索引（用于全文搜索）
     */
    indexContent(filePath, content) {
        // 提取关键词并建立倒排索引
        const words = this.tokenizeContent(content);
        
        for (const word of words) {
            if (!this.contentIndex.has(word)) {
                this.contentIndex.set(word, new Set());
            }
            this.contentIndex.get(word).add(filePath);
        }
    }

    /**
     * 链接索引
     */
    indexLinks(filePath, links) {
        for (const link of links) {
            if (!this.linkIndex.has(link)) {
                this.linkIndex.set(link, {
                    inbound: new Set(),
                    outbound: new Set()
                });
            }
            
            // 记录出链
            this.linkIndex.get(link).inbound.add(filePath);
            
            // 记录入链
            if (!this.linkIndex.has(filePath)) {
                this.linkIndex.set(filePath, {
                    inbound: new Set(),
                    outbound: new Set()
                });
            }
            this.linkIndex.get(filePath).outbound.add(link);
        }
    }

    /**
     * 标签索引
     */
    indexTags(filePath, tags) {
        for (const tag of tags) {
            if (!this.tagIndex.has(tag)) {
                this.tagIndex.set(tag, new Set());
            }
            this.tagIndex.get(tag).add(filePath);
        }
    }

    /**
     * 快速搜索
     */
    search(query, options = {}) {
        const results = new Map();
        const queryLower = query.toLowerCase();
        
        // 文件名搜索
        if (options.searchFiles !== false) {
            for (const [path, info] of this.index) {
                if (info.name.toLowerCase().includes(queryLower) ||
                    info.basename.toLowerCase().includes(queryLower)) {
                    results.set(path, {
                        ...info,
                        matchType: 'filename',
                        score: this.calculateScore(query, info.name)
                    });
                }
            }
        }
        
        // 内容搜索
        if (options.searchContent !== false) {
            const words = this.tokenizeContent(query);
            for (const word of words) {
                const files = this.contentIndex.get(word.toLowerCase());
                if (files) {
                    for (const filePath of files) {
                        const info = this.index.get(filePath);
                        if (info && !results.has(filePath)) {
                            results.set(filePath, {
                                ...info,
                                matchType: 'content',
                                score: this.calculateContentScore(query, filePath)
                            });
                        }
                    }
                }
            }
        }
        
        // 标签搜索
        if (options.searchTags !== false) {
            for (const [tag, files] of this.tagIndex) {
                if (tag.toLowerCase().includes(queryLower)) {
                    for (const filePath of files) {
                        const info = this.index.get(filePath);
                        if (info && !results.has(filePath)) {
                            results.set(filePath, {
                                ...info,
                                matchType: 'tag',
                                score: this.calculateScore(query, tag)
                            });
                        }
                    }
                }
            }
        }
        
        // 排序结果
        return Array.from(results.values())
            .sort((a, b) => b.score - a.score)
            .slice(0, options.limit || 50);
    }

    /**
     * 获取文件统计信息
     */
    getStatistics() {
        const stats = {
            totalFiles: this.index.size,
            totalWords: 0,
            totalTags: this.tagIndex.size,
            totalLinks: this.linkIndex.size,
            
            // 文件类型分布
            fileTypes: new Map(),
            
            // 文件夹分布
            folderDistribution: new Map(),
            
            // 标签使用频率
            tagFrequency: new Map(),
            
            // 最近修改的文件
            recentFiles: [],
            
            // 最大的文件
            largestFiles: []
        };
        
        for (const [path, info] of this.index) {
            stats.totalWords += info.wordCount;
            
            // 文件类型统计
            const ext = info.extension || 'no-ext';
            stats.fileTypes.set(ext, (stats.fileTypes.get(ext) || 0) + 1);
            
            // 文件夹统计
            const folder = info.folder || 'root';
            stats.folderDistribution.set(folder, (stats.folderDistribution.get(folder) || 0) + 1);
        }
        
        // 标签频率统计
        for (const [tag, files] of this.tagIndex) {
            stats.tagFrequency.set(tag, files.size);
        }
        
        // 最近文件
        stats.recentFiles = Array.from(this.index.values())
            .sort((a, b) => b.modified - a.modified)
            .slice(0, 10);
        
        // 最大文件
        stats.largestFiles = Array.from(this.index.values())
            .sort((a, b) => b.size - a.size)
            .slice(0, 10);
        
        return stats;
    }

    /**
     * 工具方法
     */
    countWords(content) {
        return content.split(/\s+/).filter(word => word.length > 0).length;
    }

    tokenizeContent(content) {
        return content.toLowerCase()
            .replace(/[^\w\s\u4e00-\u9fff]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 1);
    }

    extractKeywords(content) {
        const words = this.tokenizeContent(content);
        const frequency = new Map();
        
        for (const word of words) {
            frequency.set(word, (frequency.get(word) || 0) + 1);
        }
        
        return Array.from(frequency.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 20)
            .map(([word]) => word);
    }

    calculateScore(query, text) {
        const queryLower = query.toLowerCase();
        const textLower = text.toLowerCase();
        
        if (textLower === queryLower) return 100;
        if (textLower.startsWith(queryLower)) return 80;
        if (textLower.includes(queryLower)) return 60;
        
        // 模糊匹配评分
        let score = 0;
        const queryChars = queryLower.split('');
        let textIndex = 0;
        
        for (const char of queryChars) {
            const found = textLower.indexOf(char, textIndex);
            if (found !== -1) {
                score += 1;
                textIndex = found + 1;
            }
        }
        
        return (score / queryChars.length) * 40;
    }

    calculateContentScore(query, filePath) {
        // 基于内容匹配度计算分数
        // 这里可以实现更复杂的TF-IDF算法
        return 50;
    }

    clearIndexes() {
        this.index.clear();
        this.contentIndex.clear();
        this.linkIndex.clear();
        this.tagIndex.clear();
    }

    async saveIndex() {
        const indexData = {
            version: '1.0',
            lastUpdate: Date.now(),
            index: Array.from(this.index.entries()),
            contentIndex: Array.from(this.contentIndex.entries()).map(([key, value]) => [key, Array.from(value)]),
            linkIndex: Array.from(this.linkIndex.entries()).map(([key, value]) => [key, {
                inbound: Array.from(value.inbound),
                outbound: Array.from(value.outbound)
            }]),
            tagIndex: Array.from(this.tagIndex.entries()).map(([key, value]) => [key, Array.from(value)])
        };
        
        try {
            await app.vault.adapter.write(this.indexPath, JSON.stringify(indexData, null, 2));
            console.log('💾 索引已保存');
        } catch (error) {
            console.error('❌ 保存索引失败:', error);
        }
    }

    async loadExistingIndex() {
        try {
            const data = await app.vault.adapter.read(this.indexPath);
            const indexData = JSON.parse(data);
            
            // 恢复索引
            this.index = new Map(indexData.index);
            this.contentIndex = new Map(indexData.contentIndex.map(([key, value]) => [key, new Set(value)]));
            this.linkIndex = new Map(indexData.linkIndex.map(([key, value]) => [key, {
                inbound: new Set(value.inbound),
                outbound: new Set(value.outbound)
            }]));
            this.tagIndex = new Map(indexData.tagIndex.map(([key, value]) => [key, new Set(value)]));
            this.lastUpdate = indexData.lastUpdate;
            
            console.log('📂 已加载现有索引');
        } catch (error) {
            console.log('📂 未找到现有索引，将创建新索引');
            throw error;
        }
    }

    async checkIfRebuildNeeded() {
        if (!this.lastUpdate) return true;
        
        // 检查是否有文件在索引后被修改
        const files = app.vault.getMarkdownFiles();
        
        for (const file of files) {
            const indexedInfo = this.index.get(file.path);
            if (!indexedInfo || file.stat.mtime > indexedInfo.indexed) {
                return true;
            }
        }
        
        return false;
    }

    async updateIncrementalIndex() {
        const files = app.vault.getMarkdownFiles();
        let updated = 0;
        
        for (const file of files) {
            const indexedInfo = this.index.get(file.path);
            if (!indexedInfo || file.stat.mtime > indexedInfo.indexed) {
                await this.indexFile(file);
                updated++;
            }
        }
        
        if (updated > 0) {
            await this.saveIndex();
            console.log(`🔄 增量更新了 ${updated} 个文件`);
        }
    }
}

// 导出全局实例
window.fileIndexSystem = new FileIndexSystem();

// 自动初始化
if (typeof module !== 'undefined') {
    module.exports = FileIndexSystem;
} else {
    // 在Obsidian中自动初始化
    setTimeout(() => {
        window.fileIndexSystem.initialize();
    }, 1000);
}
