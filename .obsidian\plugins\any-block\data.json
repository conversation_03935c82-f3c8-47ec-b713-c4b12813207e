{"select_list": "ifhead", "select_quote": "ifhead", "select_code": "ifhead", "select_heading": "ifhead", "select_brace": "yes", "decoration_source": "none", "decoration_live": "block", "decoration_render": "block", "is_neg_level": false, "alias_use_default": true, "alias_user": [{"regex": "|alias_demo|", "replacement": "|addClass(ab-custom-text-red)|addClass(ab-custom-bg-blue)|"}, {"regex": "/\\|alias_reg_demo\\|/", "replacement": "|addClass(ab-custom-text-red)|addClass(ab-custom-bg-blue)|"}], "user_processor": [{"id": "alias2_demo", "name": "alias2_demo", "match": "alias2_demo", "process_alias": "|addClass(ab-custom-text-blue)|addClass(ab-custom-bg-red)|"}], "is_debug": false}