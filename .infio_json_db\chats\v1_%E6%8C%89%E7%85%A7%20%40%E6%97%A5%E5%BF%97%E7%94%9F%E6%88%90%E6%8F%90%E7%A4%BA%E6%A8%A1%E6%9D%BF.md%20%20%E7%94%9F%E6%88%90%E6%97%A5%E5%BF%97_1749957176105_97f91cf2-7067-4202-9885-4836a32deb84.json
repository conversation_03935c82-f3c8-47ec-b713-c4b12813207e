{"id": "97f91cf2-7067-4202-9885-4836a32deb84", "title": "按照 @日志生成提示模板.md  生成日志", "messages": [{"role": "user", "applyStatus": 0, "content": {"root": {"children": [{"children": [{"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "按照 ", "type": "text", "version": 1}, {"detail": 1, "format": 0, "mode": "token", "style": "", "text": "@日志生成提示模板.md", "type": "mention", "version": 1, "mentionName": "日志生成提示模板.md", "mentionable": {"type": "file", "file": "数据分析/日志生成提示模板.md"}}, {"detail": 0, "format": 0, "mode": "normal", "style": "", "text": "  生成日志", "type": "text", "version": 1}], "direction": "ltr", "format": "", "indent": 0, "type": "paragraph", "version": 1, "textFormat": 0, "textStyle": ""}], "direction": "ltr", "format": "", "indent": 0, "type": "root", "version": 1}}, "promptContent": [{"type": "text", "text": "<task>\n按照 @日志生成提示模板.md  生成日志\n</task>\n\n<file_content path=\"数据分析/日志生成提示模板.md\">\n 1 | ---\n 2 | cssclasses:\n 3 |   - editor-full\n 4 | ---\n 5 | # 生成路径 \n 6 | * `历史起源/编年史/{YYYY-MM-DD}.md` 按照该路径以及格式生成笔记\n 7 | \n 8 | ---\n 9 | # 笔记内容\n10 | \n11 | ## 天气\n12 | * 通过 https://www.msn.cn/zh-cn/weather/forecast/in-Longquanyi-District 获取今天的天气信息\n13 | \t* 将天气的具体情况以及变化进行精确描述 (温度，湿度等)\n14 | \t* 将之后3天的天气进行简单描述\n15 | \n16 | ---\n17 | \n18 | \n</file_content>\n\n<environment_details>\n# Obsidian Current File\n数据分析/日志生成提示模板.md\n\n# Obsidian Open Tabs\nMaster_Home🧬.md\n数据分析/日志生成提示模板.md\n历史起源/编年史/2025-06-14.md\n地理数据/笔记创建面板.md\n\n# Current Time\n2025/6/15 上午11:12:09 (Asia/Hong_Kong, UTC+8:00)\n\n# Current Mode\n<slug>write</slug>\n<name>Write</name>\n</environment_details>"}], "id": "4cc4b503-a8ce-4596-a247-77802679c4cc", "mentionables": [{"type": "file", "file": "数据分析/日志生成提示模板.md"}]}, {"role": "assistant", "applyStatus": 1, "content": "用户要求根据已提供的“日志生成提示模板.md”文件来生成一篇日志。我已经分析了模板内容，确定了日志文件的生成路径为`历史起源/编年史/2025-06-15.md`，并需要在日志中包含今天和未来三天的天气信息。\n\n现在，我将使用 `playwright_navigate` 工具访问指定的MSN天气预报URL，以获取天气数据。\n\n<use_mcp_tool>\n<server_name>github.com/executeautomation/mcp-playwright</server_name>\n<tool_name>playwright_navigate</tool_name>\n<arguments>\n{\n  \"url\": \"https://www.msn.cn/zh-cn/weather/forecast/in-Longquanyi-District\"\n}\n</arguments>\n</use_mcp_tool>", "reasoningContent": "", "id": "d028e330-72c5-4ccb-9618-4b05eb0f1f68", "metadata": {"usage": {"prompt_tokens": 25041, "completion_tokens": 193, "total_tokens": 25492}, "model": {"provider": "Google", "modelId": "gemini-2.5-flash-preview-05-20"}}}, {"role": "assistant", "applyStatus": 0, "content": "<tool_result>[use_mcp_tool for 'github.com/executeautomation/mcp-playwright'] Result:\nNavigated to https://www.msn.cn/zh-cn/weather/forecast/in-Longquanyi-District\n</tool_result>", "reasoningContent": "", "id": "f58f8c3f-49ed-4330-a0d5-404bad377dd5", "metadata": {}}, {"role": "user", "applyStatus": 0, "content": null, "promptContent": "[use_mcp_tool for 'github.com/executeautomation/mcp-playwright'] Result:\nNavigated to https://www.msn.cn/zh-cn/weather/forecast/in-Longquanyi-District\n", "id": "92ce63df-9ecc-428d-9a2c-336f5da772ea", "mentionables": []}, {"role": "assistant", "applyStatus": 1, "content": "思考\n我已经成功导航到MSN天气预报页面。现在我需要获取该页面的HTML内容，以便从中提取今天的具体天气情况（温度、湿度等）以及未来三天的天气描述。`playwright_get_visible_html` 工具可以获取当前页面的HTML内容。\n\n考虑到需要从HTML中精确提取信息并进一步处理，我将使用 `playwright_get_visible_html`。我已经成功访问了MSN天气预报页面。现在我将获取该页面的HTML内容，以便从中提取所需的天气信息。\n\n<use_mcp_tool>\n<server_name>github.com/executeautomation/mcp-playwright</server_name>\n<tool_name>playwright_get_visible_html</tool_name>\n<arguments>{}</arguments>\n</use_mcp_tool>", "reasoningContent": "", "id": "bb45542d-a597-4689-b3a0-7e14b9e69b31", "metadata": {"usage": {"prompt_tokens": 25289, "completion_tokens": 186, "total_tokens": 25475}, "model": {"provider": "Google", "modelId": "gemini-2.5-flash-preview-05-20"}}}, {"role": "assistant", "applyStatus": 0, "content": "<tool_result>[use_mcp_tool for 'github.com/executeautomation/mcp-playwright'] Result:\nHTML content:\n<!DOCTYPE html><html lang=\"ja-jp\" dir=\"ltr\" style=\"background-color: rgb(255, 255, 255);\"><head data-info=\"f:msnallexpusers,prg-sp-liveapi,prg-hp-fsnossr,prg-hp-nofs,prg-hp-ssr,prg-hp-stsw,ads-cfv42-c,prg-views-respad,prg-1sw-sarfmrt3,prg-adspeek,btrecrow3,prg-ad-cta-logo-va-c,1s-fcrypt,prg-upsaip-w1-t,1s-rpssecautht,traffic-p2-osc-c,prg-pr2-optreload,preprg-health-staging,cprg-ad-win-ban,chatn_v2_t1,1s-wpo-pr2-maibar-t3,prg-pr2-ntf-morebn3nf,1s-logdb-preview-mbn3nf,prg-pr2-ntf-mbn,prg-pr2-ntf-mbn3,prg-csacclink,prg-1s-acclnk,prg-accountlink,1s-shp-rc-t-gsant88,prg-sh-rmitmlnk,nopinglancecardit,prg-cg-notf-landing-t,prg-cg-notf-sub,prg-cg-notf-sub-btn,prg-ad-cms-cnt,prg-1sw-ucsem,ads-nopostsq-t,ads-nopostsq,1s-uasdisf-t,ads-creativelog-c,ads-usepme,ads-anjson-migt,sh-bdvid,prg-sh-bd-video,ads-nooutbrain,release-outlook-app,ads-prcrid-bi,prg-cg-ab-testing,prg-1sw-tvid-t20,1s-p1-vid-vs,prg-1sw-tvid-p1,prg-1sw-videopb,prg-1sw-videosxap,prg-p1-txt2,prg-p1-uc3,prg-pr1-videos,prg-tv-api,prg-tv-segcap10,prg-vid-cd,prg-vid-trdcache,prg-1sw-reclaim2,prg-1sw-reclaim,1s-p2-bg-appanon,ads-bcn-cndomain,prg-cg-int-ad-pod,prg-1sw-tvid-t20-vp,1s-p2-vid-vp,prg-1sw-tvid-p2,prg-p2-txt2,prg-p2-uc3,prg-pr2-setdur-t2,prg-pr2-videos,prg-pr2-wtab-oo,prg-tv-p2-api,prg-tv-p2-segcap10,msph-benchmark,prg-pr2-lifecyclebac,1s-routenotexpc,prg-1sw-sa-dnec,prg-1sw-crypinf,prg-cg-lock_c,prg-wx-nsever,prg-1sw-cryptren,traffic-n-tthero-t,prg-ntp-tfherott-on,msph-adsrevpd,prg-cg-cfzhcnfx,prg-cg-lstfix,prg-1sw-tbrfltr,prg-1sw-tvid-int-t1,1s-p1-vid-vs-int,prg-1sw-tvid-p1-int,prg-1sw-tvid-int-t2,1s-p2-vid-vp-int,prg-1sw-tvid-p2-int,prg-fin-rmar,1s-vid-dwt,prg-vdw-enable,prg-pr2-wwidgets-t,bing_native_chat_t,prg-1s-dwvid-wpo,1s-wpo-ntp-videos,1s-newsfeed-worknews,bing_uni_iab_t,prg-pr2-imghttd-t,prg-pr2-imghtdd-t,prg-1sw-wxaqifct,prg-wx-btide,prg-wx-ntide,prg-cg-countermoniccc,prg-1sw-safitopo,prg-1sw-sasptopo,prg-1sw-newe2e,1s-ls-uppermuid,prg-vdw-auto-cur,prg-vdw-automated,prg-1sw-wxaqepp,prg-1sw-wxaqoff,prg-wx-saqi0,prg-wx-saqi,prg-wx-nhroll,1s-wxmixcache,prg-1sw-wxncvf,prg-1sw-wxtrlog,prg-cg-rps3,prg-ad-dv-on,prg-ad-infopane-dv,msph-follv2,prg-1sw-wxtrlogp,1s-ntf2-pradasimiv2,1s-ntf1-pradasimiv2,prg-1sw-santf-vd4u-c,prg-pr2-ntf-rel,prg-1sw-pawpor1,prg-1sw-spawpor1,prg-1sw-dwvid-wpo-2,1s-wpo-ntp-videos2,prg-hp-c-lockscreen,prg-1sw-sfexpdat,1s-ntfa-fpmeta,prg-1sw-analysisdata,prg-ad-ssr-dpr-rf,1s-ads-dpr-r,prg-1sw-cs-rank,1s-cs-video,1s-xaparray-c,prg-1sw-p2-txt3,prg-fin-ruby,ads-3pdmn-filter,ads-3pctve-filter,cprg-xdr303a,cprg-xdr303,prg-1sw-wxdtdeprev,prg-1sw-otrim,prg-1sw-wxwtnyrev,prg-pw-bcn-card,prg-beacon-card,ads-3pdmn-hld-c,prg-1sw-l3v1ship,1s-wpo-pr1-layout3wv1,prg-1sw-version-fix,prg-1sw-l3v2,1s-wpo-pr1-layout3wv2;\" data-canvas-info=\"f:prg-hp-ssr,prg-accountlink,prg-ad-infopane-dv\" data-ads-numberline=\"f:moneyhz3=ads-cfv42-c,5qos=ads-nopostsq-t,5sk0=ads-creativelog-c,5sov=ads-usepme,5ui9=ads-anjson-migt,63hy=ads-nooutbrain,649x=ads-prcrid-bi,6965=ads-bcn-cndomain,7khf=ads-3pdmn-filter,7khg=ads-3pctve-filter,7n5l=ads-3pdmn-hld-c;\" data-client-settings=\"{&quot;aid&quot;:&quot;215E82E2BBE34EA190516A86CB73D223&quot;, &quot;fd_muid&quot;:&quot;3FDB89BC40236CDB115E9FB041BA6DA2&quot;, &quot;static_page&quot;:&quot;false&quot;, &quot;queryparams&quot;:&quot;&quot;, &quot;apptype&quot;:&quot;homePage&quot;, &quot;pagetype&quot;:&quot;hp&quot;, &quot;configRootUrl&quot;:&quot;https://assets.msn.com/config/v1/&quot;, &quot;feedBaseDomain&quot;:&quot;&quot;, &quot;deviceFormFactor&quot;:&quot;desktop&quot;, &quot;pageGenTime&quot;:&quot;2025-06-15T03:12:17Z&quot;, &quot;pcsInfo&quot;:{&quot;v&quot;:&quot;********.5_master&quot;, &quot;env&quot;:&quot;prod&quot;}, &quot;locale&quot;:{&quot;language&quot;:&quot;ja&quot;, &quot;market&quot;:&quot;jp&quot;, &quot;content&quot;:{&quot;language&quot;:&quot;ja&quot;, &quot;market&quot;:&quot;jp&quot;}, &quot;display&quot;:{&quot;language&quot;:&quot;ja&quot;, &quot;market&quot;:&quot;jp&quot;}}, &quot;featureFlags&quot;:{}, &quot;market&quot;:{ &quot;requestInput&quot;:{ &quot;language&quot;:&quot;ja&quot;,&quot;region&quot;:&quot;jp&quot;, &quot;geo_country&quot;:&quot;JP&quot;}, &quot;detection&quot;:{ &quot;type&quot;:&quot;url&quot;,&quot;geo_country&quot;:&quot;&quot;,&quot;geo_countrycf&quot;:&quot;&quot;, &quot;recommendLanguageCoachmark&quot;: &quot;false&quot;}}, &quot;servicesEndpoints&quot;:{&quot;staticConfig&quot;: {&quot;domain&quot;: &quot;https://assets.msn.com&quot;, &quot;path&quot;: &quot;/config/v3/&quot;, &quot;v&quot;: &quot;&quot;},&quot;crs&quot;: {&quot;domain&quot;: &quot;https://assets.msn.com&quot;, &quot;path&quot;: &quot;/resolver/api/resolve/&quot;, &quot;v&quot;: &quot;v3&quot;},&quot;feedService&quot;: {&quot;domain&quot;: &quot;https://assets.msn.com&quot;, &quot;path&quot;: &quot;&quot;, &quot;v&quot;: &quot;&quot;},&quot;enterpriseFeedService&quot;: {&quot;domain&quot;: &quot;https://ent-api.msn.com/&quot;, &quot;path&quot;: &quot;&quot;, &quot;v&quot;: &quot;&quot;}}, &quot;bundleInfo&quot;:{&quot;v&quot;:&quot;20250613.83&quot;, &quot;alias&quot;:&quot;latest&quot;}, &quot;os&quot;:&quot;windows&quot;, &quot;browser&quot;:{&quot;browserType&quot;:&quot;chrome&quot;, &quot;version&quot;:&quot;134&quot;, &quot;ismobile&quot;:&quot;false&quot;}, &quot;domain&quot;:&quot;www.msn.com&quot;, &quot;requestTrailInfo&quot;:{&quot;flightVersion&quot;:&quot;53531360&quot;}, &quot;detection&quot;:{&quot;scrubbedIp&quot;:&quot;*************&quot;, &quot;tier&quot;:&quot;b&quot;}, &quot;geo_country&quot;:&quot;JP&quot;, &quot;geo_countryname&quot;:&quot;Japan&quot;, &quot;geo_subdivision&quot;:&quot;Tokyo&quot;, &quot;geo_zip&quot;:&quot;151-0053&quot;, &quot;geo_city&quot;:&quot;Tokyo&quot;, &quot;geo_lat&quot;:&quot;35.6807&quot;, &quot;geo_long&quot;:&quot;139.6943&quot;, &quot;geo_citycf&quot;:&quot;8&quot;, &quot;geo_countrycf&quot;:&quot;8&quot;, &quot;requestMuid&quot;:&quot;3FDB89BC40236CDB115E9FB041BA6DA2&quot;}\" data-diagnostics=\"{&quot;mc&quot;:{&quot;spt&quot;:&quot;ja-jp%7c%7cjp%7cja-jp%7cja-jp%7cja%7c%7ccf%3d8%7cRefA%3dAA3942D0BC7E42E28D3224414F432CAA.RefC%3d2025-06-15T03%3a12%3a17Z&quot;}}\"><script id=\"display-sdk\" type=\"text/javascript\" src=\"https://adsdk.microsoft.com/ast/ast.js\" async=\"\"></script>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n    \n    \n\n    <title>MSN | パーソナライズされたニュース、トップ ヘッドライン、ライブ アップデートなど</title>\n\n    \n    \n\n\n    \n            <link rel=\"stylesheet\" href=\"https://assets.msn.com/bundles/v1/homePage/latest/experience.69f3918934450044f420.css\" crossorigin=\"anonymous\">\n    \n\n    \n\n    <script type=\"text/javascript\" nonce=\"\">\n        try {\n            window._pageTimings = { TTJSStart: Math.round(performance.now()) };\n            window._clientSettings = JSON.parse(document.head.dataset.clientSettings);\n        } catch (e) {\n            console.error(\"Error in adding TTJSStart marker\");\n        }\n    </script>\n\n        <script nonce=\"\">\n            function getCookieConsentRequired(){return false;}\n            window._ssrServiceEntryUrl=\"/bundles/v1/homePage/latest/SSR-service-entry.253e46f1e0b461971ee7.js\";\n        </script>\n\n\n        <script type=\"text/javascript\" id=\"inlinehead-inline-script\" nonce=\"\">\n            window[\"_webWorkerBundle\"] = \"/bundles/v1/homePage/latest/web-worker.063abe45ff34c5111974.js\";\n            window[\"_authCookieName\"] = \"ace\";\n            !function(){\"use strict\";var e={},t={};function n(o){var i=t[o];if(void 0!==i)return i.exports;var r=t[o]={exports:{}};return e[o](r,r.exports,n),r.exports}let o,i;function r(){if(i)return i;const e=document.head.getAttribute(\"data-info\");return i=((/f:\\s*([^;]+)/i.exec(e)||{})[1]||\"\").toLowerCase(),i}n.g=function(){if(\"object\"==typeof globalThis)return globalThis;try{return this||new Function(\"return this\")()}catch(e){if(\"object\"==typeof window)return window}}(),function(){if(void 0!==n){const e=n.e,t={};n.e=function(o){return e(o).catch((function(e){const i=t.hasOwnProperty(o)?t[o]:2;if(i<1)throw e;return new Promise((function(e){setTimeout((function(){t[o]=i-1,e(n.e(o))}),100)}))}))}}}();let s=new Map;const c=Object.freeze({set(e,t){return s.set(e,t),this},get:function(e,t){let n=s.get(e);return void 0===n&&t&&(n=t(),s.set(e,n)),n},clear(){s.clear()},delete:e=>s.delete(e),has:e=>s.has(e)});function a(){return!(\"undefined\"==typeof window||!window.document||!window.document.createElement||window.isRenderServiceEnv)}function l(e){let t,n=!1;const o=function(){return n||(n=!0,t=e()),t};return o.cache={clear:()=>{n=!1,t=void 0}},o}const d=\"OnOOutlookMobile\",u=\"OnOStartApp\",h=\"xmweather-\",g=\"launcherntp\",p=\"launcherInterests\",m=\"swiftKey\",w=\"superappdhp\",f=\"_ocidIsomorphicKey\",v=()=>(c.get(f)||c.set(f,W(\"ocid\").toLowerCase()),c.get(f)),b=\"_platformFlagKey\",y=\"_hostAppNameKey\",C=\"_clientTypeKey\",S=()=>(c.get(b)||c.set(b,W(\"chpltfm\")),c.get(b)),k=()=>{if(!c.get(y)){const e=S();c.set(y,e.toLowerCase().split(\"-\")[0])}return c.get(y)},I=()=>{if(!c.get(C)){const e=S();c.set(C,e.toLowerCase().split(\"-\")[1])}return c.get(C)};function A(){if(!a()||O())return!1;const e=window.sapphireWebViewBridge,t=window.webkit,n=e&&e.send,o=t&&t.messageHandlers&&t.messageHandlers.send&&t.messageHandlers.send.postMessage;return!(!n&&!o)}function q(){return!!window._isCopilotBridge||\"copilot\"===W(\"MBApp\")}function E(){const e=S()&&\"outlook\"===k()&&[\"ios\",\"android\",\"ipados\"].includes(I()),t=v()===d.toLowerCase();return e||t}function x(){return!(!a()||O())&&(A()||v()===u.toLowerCase()||v()===w.toLowerCase())}function L(){const e=W(\"edge\");return(\"emmx\"===k()||\"1\"===e)&&x()||\"mmx\"===v()}function _(){return\"3rdp\"===k()||v().startsWith(h)||O()}function T(){return[g.toLowerCase(),p.toLowerCase()].includes(v())&&x()}function O(){return v().toLowerCase()===m.toLowerCase()||\"swiftkey\"==k().toLowerCase()}const N=l((()=>A()||E()||x()||v()===\"weather-app-win\".toLowerCase()||v()===\"finance-app-win\".toLowerCase()||L()||_()||T()||O()||-1!==window.location.href.indexOf(\"metaos=true\")||q()));function W(e){try{return new URL(location.href).searchParams.get(e)||\"\"}catch(e){return\"\"}}function R(e,t,n){const o=n&&\"phone\"===n.toLowerCase(),i=t&&\"enterprise\"===t;let r=\"Edge\";O()?r=\"swiftKey\":T()?r=\"Launcher\":function(){var e;if(!a())return!1;if(\"bingHomepageMobile\"===(null===(e=window._clientSettings)||void 0===e?void 0:e.pagetype))return!0;{var t;const e=null===(t=document.getElementsByClassName(\"peregrine-widget-settings\")[0])||void 0===t?void 0:t.getAttribute(\"data-client-settings\");if(e){const t=JSON.parse(e);return t&&t.pagetype&&\"bingHomepageMobile\"===t.pagetype}return!1}}()&&(r=\"bingHomepageMobile\");return{bingHomepage:\"binghomepage\",mmx:\"emmx\",edge:\"spartan\",edgeChromium:i?\"entnews\":\"anaheim\",hybrid:\"spartan\",hub:o?\"prime_mobile\":\"prime\",microsoftNews:\"msnews\",office:\"entnews\",views:o?\"prime_mobile\":\"prime\",homePage:o?\"prime_mobile\":\"prime\",windowsShell:\"windowsshell\",edgeMobile:r}[e]}const M={homePage:{vertical:\"homepage\",category:\"\",id:\"\",domainId:\"13041\",title:\"undefined\"!=typeof document?document.title:\"\"}};function P(e){let t=e;switch(e){case\"windowsshellhp\":t=\"dhp\";break;case\"video\":t=\"watch\";break;case\"EdgeMobile\":t=L()?\"ntp\":\"dhp\"}return t}function U(){try{var e;const t={};return null===(e=Error.captureStackTrace)||void 0===e||e.call(Error,t,U),t.stack}catch(e){return(new Error).stack}}function D(e){try{return decodeURIComponent(e)}catch(e){}}let F,$;function H(e,t){if(!e)return null;if(t)return function(e){var t;const n=null===(t=document.cookie)||void 0===t?void 0:t.match(`\\\\b${e}=([^;]*)`);return n?D(n[1]):null}(e);if(!F){const e=\"undefined\"!=typeof document&&document.cookie.split(\"; \");F={};const t=e&&e.length;for(let n=0;n<t;n++){const t=e[n].indexOf(\"=\");F[e[n].slice(0,t).toLocaleLowerCase()]=D(e[n].slice(t+1))}}const n=e.toLocaleLowerCase();return F[n]||null}function B(e){const t=H(e);return t&&encodeURIComponent(t)||null}var j;!function(e){e[e.Alert=0]=\"Alert\",e[e.Deprecated=1]=\"Deprecated\",e[e.HighImpact=2]=\"HighImpact\",e[e.Critical=3]=\"Critical\"}(j||(j={}));const J=new class{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20;this.maxLength=e,this.list=[]}push(e){this.list.push(e),this.list.length>this.maxLength&&this.list.shift()}get data(){return this.list}};function V(e,t,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:j.Alert,s=arguments.length>4?arguments[4]:void 0,c=arguments.length>5?arguments[5]:void 0,a=arguments.length>6?arguments[6]:void 0;try{const l=function(){if(!o){const e=document.head.getAttribute(\"data-client-settings\");e&&(o=JSON.parse(e))}return o}(),d=r(),u=function(e){if(e){const{pcsInfo:t,pageGenTime:n}=e,o=new Date(n).getTime(),i=!t||[\"prod\",\"prod-ssr\",\"prod-ssrntp\"].includes(t.env);$=i?\"browser.events.data.msn.com\":\"events-sandbox.data.msn.com\";return{cors:\"true\",\"content-type\":\"application/x-json-stream\",\"client-id\":\"NO_AUTH\",\"client-version\":\"1DS-Web-JS-2.2.2\",apikey:i?\"0ded60c75e44443aa3484c42c1c43fe8-9fc57d3f-fdac-4bcf-b927-75eafe60192e-7279\":\"f8857dedc6f54ca8962cfb713e01e7d7-e9250191-fe0b-446f-95ae-07516262f98c-7028\",\"upload-time\":o,w:\"0\",anoncknm:\"app_anon\"}}return null}(l);let h=\"\";u&&u.apikey&&\"\"!==u.apikey&&(h=function(e){if(e){const t=e.indexOf(\"-\");if(t>0)return e.substring(0,t)}return\"\"}(u.apikey));const g=function(e,t,n,o,i,r){let s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:j.Alert,c=arguments.length>7&&void 0!==arguments[7]?arguments[7]:\"\";if(n){i=i||{};const{aid:u,apptype:h,audienceMode:g,pagetype:p,pageGenTime:m,bundleInfo:w,deviceFormFactor:f=\"\",fd_muid:v,os:b,static_page:y}=n;i.pageGenTime=m,i.build=w&&w.v,i.appType=h,i.pageVisible=document.visibilityState;const C=R(h,g,f),S=C||h,k=document.getElementsByTagName(\"html\")[0].getAttribute(\"lang\");let I,A=\"\",q=\"muid\";try{if(\"edgeChromium\"===h&&\"object\"==typeof window&&window.location&&window.location.search){const e=new URLSearchParams(window.location.search);I=e.has(\"startpage\")?\"msedgdhp\":\"msedgntp\",\"enterprise\"===g?I=\"entnewsntp\":\"xbox\"===b&&(I=\"xboxntp\")}window&&window.getCookieConsentRequired&&\"function\"==typeof window.getCookieConsentRequired&&window.getCookieConsentRequired()||(A=(null===(l=window)||void 0===l||null===(l=l.__SSRUserConfigEarlyLog)||void 0===l?void 0:l.consistentMuid)||(null===(d=window)||void 0===d?void 0:d.__muid)||H(\"muid\"))}catch{}A||(A=n.aid,q=\"aid\");const E={name:\"MS.News.Web.AppError\",time:m,ver:\"4.0\",iKey:`o:${o}`,data:{baseData:{},baseType:\"MS.News.Web.Base\",page:{name:\"default\",product:S,type:P(p),content:M[h]??{category:\"standaloneError\"},ocid:I,url:location.href,isStatic:y},browser:{clientId:A,clientIdType:q},flight:{id:r,tmpl:c},request:{activityId:u,requestId:u,afdMuid:v},locale:{mkt:k},extSchema:{id:e,severity:s,pb:i,message:t}}};var a;if(A&&\"muid\"===q)E.ext={...null==E?void 0:E.ext,user:{...null==E||null===(a=E.ext)||void 0===a?void 0:a.user,localId:`t:${A}`}};return\"object\"==typeof window&&(window.isSSREnabled&&(E.data.flight.tmpl+=\";ssr-enabled:1\"),window.isSSRCompleted&&(E.data.flight.tmpl+=\";ssr-completed:1\")),E?JSON.stringify(E):null}var l,d;return null}(t,e,l,h,n,d,i,a);if(c&&!function(e){if(null==e)return!1;return e.startsWith(\"1\")||e.startsWith(\"2\")||e.startsWith(\"3\")||e.startsWith(\"4\")}(null==l?void 0:l.aid))return console.error(g),void console.error(`This App error Id: ${t} will not be sent due to app error sampling!`);if(u&&g){console.error(g),J.push(g);const e=\"https://\"+$+\"/OneCollector/1.0\"+function(e){return\"?\"+Object.keys(e).map((function(t){return t+\"=\"+encodeURIComponent(e[t])})).join(\"&\")}(u);if(s&&!performance.getEntriesByType(\"visibility-state\").some((e=>\"visible\"===e.name))){const t=()=>{\"visible\"===document.visibilityState&&navigator.sendBeacon(e,g)};document.addEventListener(\"visibilitychange\",t,{once:!0})}else navigator.sendBeacon(e,g)}}catch{}}function G(e){V(\"Error when loading bundle: \"+e,20202,{...arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}})}function K(e,t,n,o,i){const r=(i&&i.stack)??U(),s={source:t,customMessage:e??(null==i?void 0:i.message),line:n,column:o,stack:r};V(\"JS Exception\"+(s.customMessage?`- ${s.customMessage.substring(0,60)}`:\"\"),20203,s)}function X(e){const t={customMessage:e&&e.reason&&e.reason.message,stack:(e&&e.reason&&e.reason.stack)??U()};V(\"JS Exception on unhandled promise rejection\"+(t.customMessage?`- ${t.customMessage.substring(0,60)}`:\"\"),20203,t)}const z=\"experience\",Q=[\"common-monthly\"],Y=Object.create(null),Z=2;function ee(e,t){if(!(t=t||Array.from(document.scripts).find((t=>t.src.indexOf(`/${e}.`)>-1))))return;const n=function(e,t){const n=document.createElement(\"script\");return n.type=\"text/javascript\",n.crossOrigin=\"anonymous\",n.src=e,n.onerror=()=>{let e=Y[t]||0;Y[t]=++e,e<Z?ee(t,n):ne()},n.onload=()=>{delete Y[t];const e=window._pageTimings.retriedBundles;window._pageTimings.retriedBundles=e?`${e},${t}`:t,ne()},n}((o=t.src,Y[e],o),e);var o;setTimeout((()=>t.replaceWith(n)),100)}function te(){const[e={}]=performance.getEntriesByType(\"navigation\");return e.type}function ne(){if(!Object.values(Y).every((e=>e===Z)))return;let e=\"\";Object.keys(Y).forEach((t=>{e=e?`${e},${t}`:t,delete Y[t]})),e&&V(`Error when loading bundle(s): ${e}`,20202,{timeElapsed:Math.round(performance.now()),navType:te()})}function oe(e){try{if(e)return JSON.parse(e)}catch(e){}return null}\"undefined\"!=typeof window&&window.document&&window.document.createElement&&(window._pageTimings=window._pageTimings||{},window.requestIdleCallback=window.requestIdleCallback||window.setTimeout);const ie=\"Authorization\";var re,se;!function(e){e.Presentation=\"presentation\"}(re||(re={})),function(e){e.Unknown=\"Unknown\",e.Portrait=\"Portrait\",e.Landscape=\"Landscape\"}(se||(se={}));var ce,ae;!function(e){e[e.Undefined=0]=\"Undefined\",e[e.Basic=1]=\"Basic\",e[e.Advanced=2]=\"Advanced\",e[e.Premium=3]=\"Premium\"}(ce||(ce={})),function(e){e.Init=\"init\",e.Config=\"config\",e.Targeted=\"targeted\",e.Sticky=\"sticky\",e.NoSticky=\"no_sticky\",e.Admin=\"admin\",e.Forced=\"forced\",e.Manual=\"manual\"}(ae||(ae={}));const le=new Set([\"finance-app-win\",\"weather-app-win\",\"winpstoreapp\"]),de=new Set([\"hpmsn\"]);new Set([\"chromentpnews\"]);function ue(){var e;return a()?(window._clientSettings=window._clientSettings||oe(document.head.dataset.clientSettings||(null===(e=document.getElementsByClassName(\"peregrine-widget-settings\")[0])||void 0===e?void 0:e.getAttribute(\"data-client-settings\"))),window._clientSettings):null}const he=\"feed/personalize/settings\";function ge(e,t){const n=t.replace(/[[\\]]/g,\"\\\\$&\"),o=new RegExp(\"[?&]\"+n+\"(=([^&#]*)|&|#|$)\").exec(e);if(!o)return null;const i=o[2];return decodeURIComponent(i.replace(/\\+/g,\" \"))||\"\"}let pe=new class{constructor(){a()?(this.isDebugEnabled=(ge(window.location.href,\"debug\")||\"\").toLowerCase()in{1:1,true:1}||\"vp\"===(ge(window.location.href,\"reqsrc\")||\"\").toLowerCase(),this.isDebugEnabled&&!n.g.TEST_ENV&&(window.webpackRequire=n)):this.isDebugEnabled=!1}getLoggingService(){return null}isDebug(){return this.isDebugEnabled}setDebug(e){this.isDebugEnabled=e}setTags(e){}log(e,t){this.isDebug()&&console.info(e)}logError(e){console.error(e)}logCallback(e){this.isDebug()&&console.info(e())}logObjects(){this.isDebug()&&console.log(...arguments)}logSingleObject(e,t){this.isDebug()&&console.log(e)}};class me{get supported(){return!!this.storage}constructor(e){this.storage=e}getItem(e){if(this.supported)return this.storage.getItem(e)}getObject(e,t){const n=this.getItem(e);if(null!=n){const e=oe(n);if(null!=e)return e}return t}key(e){if(this.supported&&e>=0)return this.storage.key(e)}keys(){return this.supported?Object.keys(this.storage):[]}setObject(e,t){void 0!==t?this.setItem(e,JSON.stringify(t)):this.removeItem(e)}removeObject(e){const t=this.removeItem(e);if(null!=t)return oe(t)}setItem(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(this.supported)try{if(!t)throw\"Attempted to store null/undefined value: \"+t;this.storage.setItem(e,t)}catch(e){if(!n)throw e;pe.logError(e)}else if(!n)throw new Error(\"WebStorage not supported\")}get length(){if(this.supported)return this.storage.length}removeItem(e){if(this.supported){const t=this.getItem(e);return this.storage.removeItem(e),t}}clear(){this.supported&&this.storage.clear()}removeSubstringKeys(e){if(!this.supported||!e)return;const t=[];for(let n=0;n<this.storage.length;n++){const o=this.key(n);o&&o.includes(e)&&t.push(o)}for(let e=0;e<t.length;e++)this.removeItem(t[e])}}let we=new class extends me{constructor(){let e=null;if(a())try{e=localStorage}catch(e){}super(e)}};const fe=\"1s-tokens\";var ve;let be,ye;function Ce(){return be=Se(),be&&be.accessToken?be:void 0}function Se(){return be=be||void 0===be&&we.getObject(fe)||null,be}!function(e){e.NotAvailable=\"notAvailable\",e.Expired=\"expired\",e.Valid=\"valid\"}(ve||(ve={}));const ke=\"currentaccount\",Ie=l((()=>{const e=H(ke,!0),t=e&&oe(e),{login_hint:n}=t||{};return n&&t})),Ae=l((()=>!!H(ke)));const qe=\"__RequestDataInstance__\";class Ee{constructor(e,t){if(this.url=new URL(e.href),this.innerHeight=e.innerHeight,this.devicePixelRatio=e.devicePixelRatio,this.canUseCssGrid=e.canUseCssGrid,this.requestId=e.requestId,this.cookie=e.cookie,this.referer=e.referer,this.userAgent=e.userAgent,this.clientData=e.clientData,this.vpCetoToken=e.vpCetoToken,this.oneServiceHeaders=Le(e.oneServiceHeaders)||{},this.isPssrMode=t,t){const e=xe(\"OSATE\",this.cookie),t=!!e&&\"1\"===e,n=xe(\"OSAT\",this.cookie);if(t&&n||!e&&!n)return this.msalAuthReady=!0,void(n&&(this.oneServiceHeaders.Authorization=`Bearer ${n}`));this.msalAuthReady=!1,this.pssrRejectedReason=e&&!t?\"interactiveLogin\":e&&!n?\"missOSAT\":\"missOSATE\"}}static getInstance(){const e=c.get(qe);return e||(n.g.TEST_ENV?_e({href:\"http://localhost:8080/\",innerHeight:768,devicePixelRatio:1,canUseCssGrid:!1,requestId:\"0\",cookie:\"\",userAgent:\"\",referer:\"\",oneServiceHeaders:\"\",vpCetoToken:\"\"}):_e({href:\"http://localhost:8080/\",innerHeight:0,devicePixelRatio:0,canUseCssGrid:!1,requestId:\"0\",cookie:\"\",userAgent:\"\",referer:\"\",oneServiceHeaders:\"\",vpCetoToken:\"\"}))}static resetInstance(e){const t=new Ee(e,arguments.length>1&&void 0!==arguments[1]&&arguments[1]);return c.set(qe,t),t}}function xe(e,t){if(t&&e){const n=new RegExp(\"\\\\b\"+e+\"\\\\s*=\\\\s*([^;]*)\",\"i\").exec(t);return n&&n.length>1?n[1]:null}return null}function Le(e){try{if(e)return JSON.parse(e)}catch(e){}}const _e=Ee.resetInstance,Te=()=>Ee.getInstance();function Oe(){return a()?window.location.search:Te().url.search}function Ne(){try{return localStorage}catch(e){return null}}const We=\"__PageExperimentInstance__\";function Re(e){c.set(We,e)}function Me(e){if(!c.has(We)&&a()){var t;Re(Pe(document.head.dataset.info||(null===(t=window.document.getElementsByClassName(\"peregrine-widget-settings\")[0])||void 0===t||null===(t=t.dataset)||void 0===t?void 0:t.info)||\"\"))}const n=c.get(We);return n&&n.has(e)}function Pe(e){const t=(e||\"\").replace(/(^f:|;.*$)/g,\"\").split(\",\");return new Set(t)}const Ue=\"uxlogin\",De=\"uxlogout\",Fe=\"uxedit\",$e=\"useRedirect\",He=\"uxswitch\",Be=l((()=>{const e=Ne();return e&&\"1\"===e.getItem(Ue)||a()&&location.search&&location.search.includes(`${Ue}=1`)}));const je=l((()=>{const e=function(){try{return sessionStorage}catch(e){return null}}();return e&&!!e.getItem(De)}));const Je=l((()=>{const e=Ne();return e&&\"1\"===e.getItem(Fe)}));const Ve=l((()=>{const e=Ne();return!Me(\"prg-noredirect\")&&e&&\"1\"===e.getItem($e)&&function(){const{browser:e}=window._clientSettings||{},{browserType:t=\"\"}=e||{};return!!/safari/i.test(t)}()}));const Ge=l((()=>{const e=Ne();return e&&\"1\"===e.getItem(He)}));const Ke=ue()||{};const Xe={winWidgets:1,webWidgets:1,windowsShell:1,windowsShellV2:1,distribution:1,superApp:1,channelmobile:1,edgeMobile:1},ze={edgeChromium:1},Qe={winWidgets:1,webWidgets:1},Ye={edgeChromium:1,shopping:1,winWidgets:1,webWidgets:1,windowsShell:1,windowsShellV2:1,msn:1},Ze={edgeChromium:1,channelmobile:1,cgHomePage:1,distribution:1,winWidgets:1,webWidgets:1,windowsShell:1,windowsShellV2:1,superApp:1,edgeMobile:1},et=l((()=>Ke&&Ke.apptype)),tt=(l((()=>a()&&et()in Qe)),l((()=>a()&&!N()&&!(et()in Xe)&&!it()&&!rt()))),nt=l((()=>tt()&&!(et()in ze)));l((()=>tt()&&(!(et()in ze)||!Ae()))),l((()=>tt()&&!ct()&&!at()));const ot=l((()=>at()||ct())),it=l((()=>!(et()in Ye)&&lt()&&function(){if(\"homePage\"==et())return!0;const e=new URLSearchParams(Oe()).get(\"ocid\")||(null==Ke?void 0:Ke.ocid),t=null==e?void 0:e.toLowerCase();if(de.has(t))return!0;return!le.has(t)}()&&!function(){const e=\"homePage\"==et()&&\"hp\"==(Ke&&Ke.pagetype)&&(a()?window.location.href:Te().url.href).includes(`${he}`);return e}()));const rt=()=>a()&&document&&document.body&&document.body.getElementsByClassName(\"peregrine-widgets\").length>0;l((()=>et()in Ze||it()));function st(){if(ye)return ye;const e=Ce();return e||void 0}function ct(){return nt()&&Be()||function(){const e=nt()&&!dt()&&!Be()&&Ie()&&(\"expired\"==function(){if(be=Se(),!be)return\"notAvailable\";const e=be.expiresOn,t=new Date(e),n=\"expired\";return t.getTime()&&t>new Date?\"valid\":n}()||Je());return e}()||je()||Ve()}function at(){return nt()&&!Ie()&&Ge()}const lt=l((()=>{const{browser:e}=Ke,{browserType:t=\"\",detected:n={}}=e||{},{browserType:o=\"\"}=n||{};return/edgeChromium/i.test(t)||/edgeAndroid/i.test(t)||/edgeiOS/i.test(t)||/edge/i.test(o)}));l((()=>nt()&&!it()));const dt=()=>{const e=Ie();if(!e)return!1;let t;switch(e.account_type){case\"MSA\":t=nt()?\"ace\":\"aace\";break;case\"AAD\":t=nt()?\"WID\":\"APP_WID\";break;default:return!1}return!!H(t)};const ut=\"__core_auth_authHeaderPromiseParts__\",ht=()=>c.get(ut);let gt;function pt(){return gt}!function(){let e;const t={promise:new Promise((t=>e=t)),resolver:e};c.set(ut,t)}();const mt=l((()=>oe(H(\"AL_STATE\",!0))));class wt{constructor(){this.childCookieName=\"child\",this.command=\"command\",this.domInfo=document.head.dataset.info,this.canvasInfo=document.head.dataset.canvasInfo||\"\"}init(e,t){this.clientSettings=e||ue(),this.authCookieName=pt()||window._authCookieName||\"\",this.bundle=window._webWorkerBundle||\"\",t=t||{};const n=this.buildWebWorkerUrl(this.clientSettings,t),o=window.webWorker=this.createWebWorkerInstance(n);o?this.initCore(o):console.warn(\"Error while initializing the web worker.\")}initCore(e){const t=window.webWorkerMessages=[];e.onmessage=e=>{t.push(e.data)};const n=window.webWorkerErrors=[];e.onerror=e=>{n.push(e)},ot()&&ht().promise.then((t=>e.postMessage({id:\"AuthHeaders\",type:this.command,payload:t||{}})))}createWebWorkerInstance(e){return new Worker(e,{name:\"web-worker\"})}shouldFetchAppAnonCookie(){return!0}getAudienceMode(e){const t=H(this.authCookieName),n=H(this.childCookieName),{child:o}=t&&JSON.parse(t)||{};return t&&(1===o||n)?\"kids\":e.audienceMode||\"adult\"}buildWebWorkerUrl(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;return(this.bundle||\"\")+\"#\"+this.qsFromDocument()+this.qsFromCookies()+this.qsFromDataAttribute(e||this.clientSettings)+this.qsMisc(t)+this.qsAuth(e||this.clientSettings)+this.qsFromUrl()+this.qsFromServiceWorker()+this.qsSelectedPivot()+this.qsXboxXToken()}qs(e,t,n){return e?t+\"=\"+(n||e)+\"&\":\"\"}qsFromDocument(){var e;return this.qs(document.getElementsByTagName(\"html\")[0].getAttribute(\"lang\"),\"lang\")+this.qs(encodeURIComponent(window.location.href),\"adsReferer\")+this.qs(null===(e=a()?window.devicePixelRatio:Te().devicePixelRatio)||void 0===e?void 0:e.toString(),\"devicePixelRatio\")}qsFromServiceWorker(){if(!navigator.serviceWorker||!navigator.serviceWorker.controller)return\"\";const e=navigator.serviceWorker.controller;if(\"activated\"!==e.state||!e.scriptURL)return\"\";if(e.scriptURL){const t=new URL(e.scriptURL).searchParams.toString();return t?\"&\"+t+\"&\":\"\"}return\"\"}qsFromCookies(){return this.qs(B(this.authCookieName),\"aace\",\"1\")+this.qs(B(\"muid\"),\"muid\")+this.qs(B(this.childCookieName),\"child\")+this.qs(B(\"cbypass\"),\"cbypass\")+this.qs(this.cookieBannerConsentRequired().toString(),\"ccRequired\")+this.qs(B(\"ecasession\"),\"ecasession\")+this.qs(B(\"TOptOut\"),\"browserOptOut\")}qsFromDataAttributeHelper(e,t,n){var o;e&&e.featureFlags&&e.featureFlags.wpoEnabled&&t&&!t.includes(\"remwpofltww\")&&(t=this.appendWpoFlightsToInfo(e,t));const i=(null===(o=e.modes)||void 0===o?void 0:o.uxMode)||void 0;return this.qs(e.pcsInfo&&e.pcsInfo.env||e.env,\"env\")+this.qs(e.aid,\"aid\")+this.qs(e.apptype,\"apptype\")+this.qs(e.pagetype,\"pagetype\")+this.qs(e.audienceMode,\"audienceMode\")+this.qs(e.configIndexDocId,\"configIndexDocId\")+this.qs(e.deviceFormFactor,\"deviceFormFactor\")+this.qs(e.domain,\"domain\")+this.qs(e.configRootUrl,\"configRootUrl\")+this.qs(this.getHighestCbid(e.cbid,e.apptype),\"cbid\")+this.qs(e.ocid,\"ocid\")+this.qs(e.os,\"os\")+this.qs(JSON.stringify(e.locale),\"locale\")+this.qs(e.geo_lat,\"lat\")+this.qs(e.geo_long,\"long\")+this.qs(JSON.stringify(e.featureFlags),\"featureFlags\")+this.qs(JSON.stringify(e.browser),\"browser\")+this.qs(JSON.stringify(e.servicesEndpoints),\"servicesEndpoints\")+this.qs(e.bundleInfo&&e.bundleInfo.v||\"\",\"buildVersion\")+this.qs(t,\"dataInfo\")+this.qs(n,\"canvasInfo\")+this.qs(i,\"uxMode\")}qsSelectedPivot(){const e=\"selectedPivot\";let t;try{const n=Ne();n&&(t=n.getItem(e))}catch(e){console.warn(\"Error getting pivot id from local storage. \"+e)}return this.qs(t,e)}qsXboxXToken(){const e=\"xboxXTokenId\";let t;try{const n=Ne();n&&(t=n.getItem(e))}catch(e){console.warn(\"Error getting xbox XToken from local storage. \"+e)}return this.qs(t,e)}appendWpoFlightsToInfo(e,t){const n=this.getWpoFlightsFromLocalStorage(e);if(n&&n.length){const e=\";\",o=t.split(e);for(let e=0;e<o.length;e++){const t=o[e];if(t&&t.startsWith(\"f:\")){o[e]=t+\",\"+n.join();break}}return o.join(e)}return t}getWpoFlightsFromLocalStorage(e){const t=Ne(),n=e.locale,o=`wpo_data_ ${e.audienceMode}_${n.language}_${n.market}_${e.pagetype}_${e.apptype}`;try{const e=t.getItem(o);if(e){return JSON.parse(e).treatments.map((e=>e.id))}}catch(e){console.warn(`Error getting wpo flights from ls for ${o} Error:${e}`)}return null}getHighestCbid(e,t){try{const n=Ne(),o=parseInt(e),i=parseInt(JSON.parse(n.getItem(`cbid_${t||\"\"}`)));let r;if(o&&!isNaN(o)&&i&&!isNaN(i)&&(r=o>i?o:i),r=r||o||i,r)return window._cbid=r.toString()}catch(e){console.warn(\"Error getting highest CBID\"+e)}}qsFromDataAttribute(e){let t,n,o;const i=Ne();if(i){const o=JSON.parse(i.getItem(\"shd_\"+e.pagetype)||null);o&&o.clientSettings&&o.info&&(t={...o.clientSettings,bundleInfo:e.bundleInfo},n=o.info)}return(!t||!n||e.pageGenTime>=t.pageGenTime)&&(t=e,n=this.domInfo,o=this.canvasInfo),t.audienceMode=this.getAudienceMode(t),this.qsFromDataAttributeHelper(t,n,o)}qsFromUrl(){const e=location.search;return e&&e.length?e.substring(1,e.length):\"\"}qsMisc(e){return this.qs(performance.timeOrigin&&performance.timeOrigin.toString(),\"mainTimeOrigin\")+this.qs(e.disableWWConfig&&\"1\",\"disableWWConfig\")}qsAuth(e){let t=this.qs(nt()&&\"1\",\"disableWWAuth\");if(!ot()){const e=nt()&&st()||{};t+=this.qs(encodeURIComponent(JSON.stringify(function(e){const t={};return e&&(t[ie]=`Bearer ${e}`),t}(e.accessToken))),\"authHeaders\")}return t+=this.qs(function(e){const t=e||pt()||\"al_app_anon\",n=mt();return!(!n||!([\"anon\",\"al_anon\"].includes(t)&&n.AL_ANON||[\"app_anon\",\"al_app_anon\"].includes(t)&&n.AL_APP_ANON))}()&&\"1\",\"isAccountLinked\"),t+=this.qs(this.shouldFetchAppAnonCookie()&&\"1\",\"fetchAppAnonCookie\"),t}cookieBannerConsentRequired(){const e=null!=document.getElementById(\"consent-banner-container\")&&null===H(\"MSCC\"),t=null!=document.getElementById(\"onetrust-sdk\")&&null===H(\"eupubconsent-v2\");return e||t}}function ft(e){return new Promise((t=>setTimeout(t,e)))}var vt,bt;!function(e){e.Anon=\"ANON\",e.AppAnon=\"APP_ANON\",e.Unknown=\"\"}(vt||(vt={})),function(e){e.AnonCookieExists=\"ace\",e.AppAnonCookieExists=\"aace\"}(bt||(bt={}));const yt=\"child\";function Ct(e,t){if(e){const n=new RegExp(\"\\\\b\"+t+\"\\\\s*=\\\\s*([^;]*)\",\"i\").exec(e);return n&&n.length>1?n[1]:null}return null}const St=\"prg-pr2-\",kt=\"prg-pw-\",It=\"prg-\",At=\"prg-1sw-\";function qt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:It;const n=t.length,o=[];return e&&e.length&&(e.forEach((e=>{const i=e&&e.trim();i&&i.length>=n&&i.substring(0,n).toLowerCase()===t&&o.push(i.toLowerCase())})),o.sort()),o}function Et(e){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:\"\",n=It;var o,i;return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?n=kt:t.toLocaleLowerCase()===\"windowsShell\".toLocaleLowerCase()||t.toLocaleLowerCase()===\"windowsShellV2\".toLocaleLowerCase()?n=At:t.toLocaleLowerCase()!==\"winWidgets\".toLocaleLowerCase()||(null===(o=window)||void 0===o||null===(o=o.location)||void 0===o||null===(o=o.search)||void 0===o?void 0:o.includes(\"reqsrc=vp\"))||null!==(i=window)&&void 0!==i&&null!==(i=i.location)&&void 0!==i&&null!==(i=i.search)&&void 0!==i&&i.includes(\"vptest\")||(n=St),n}function xt(e){var t;return document.head.dataset[e]||(null===(t=_t())||void 0===t?void 0:t.dataset[e])}let Lt;function _t(){return void 0===Lt&&(Lt=document.getElementsByClassName(\"peregrine-widget-settings\")[0]||null),Lt}function Tt(e){var t;if(!e||!e.startsWith(\"f:\"))return;const n=(e.match(/f:\\s*([^;]+)/i)||[])[1];return n?null===(t=n.toLowerCase().split(\",\"))||void 0===t?void 0:t.sort():[]}!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t={};try{var n,o,i;if(!a())return t;const r=oe(xt(\"clientSettings\"))||{};let s=null===(n=r.apptype)||void 0===n?void 0:n.toLocaleLowerCase();const c=e.crsOptions||{};\"winwidgets\"!==s&&\"homepage\"!==s&&\"travel\"!==s||(c.crsPrefetch=!0),null!==(o=r.widgetAttributes)&&void 0!==o&&null!==(o=o.clientPerf)&&void 0!==o&&o.earlyCRSCall&&(c.crsPrefetch=!0),\"edgechromium\"===s&&(s=\"edgechr\");const l=!(null===(i=r.widgetAttributes)||void 0===i||null===(i=i.clientPerf)||void 0===i||!i.shouldUsePWOnlyFlights),{all:d,prg:u}=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"\";const n=Tt(xt(\"info\"))||[];let o=Tt(xt(\"canvasInfo\"));if(!o){o=qt(n,Et(n,e,t))}return{all:n,prg:o}}(l,s);(c.crsPrefetch||d.includes(\"inlinecrscall\")||d.includes(`icrscall-${s}`))&&(!function(e,t,n){const o=function(e,t){let n;const o=decodeURIComponent(Ct(e,\"aace\")),{child:i}=o&&oe(o)||{},r=Ct(e,yt);var s;return n=o&&(1===i||r)?\"kids\":(null===(s=t.modes)||void 0===s?void 0:s.audienceMode)||t.audienceMode||\"adult\",n}(document.cookie,e),{apptype:i,browser:r,domain:s,bundleInfo:{v:c}={},deviceFormFactor:l,locale:{content:d,display:u}={},ocid:h,os:g,modes:p,pagetype:m,servicesEndpoints:{crs:w}={}}=e,f=(_t()?a()?window.location.hostname:Te().url.hostname:s)||\"\";if(!i||!c||!w)return;const v={audienceMode:o,uxMode:null==p?void 0:p.uxMode},b={audienceMode:o,browser:r,deviceFormFactor:l,domain:f,locale:{content:d,display:u},ocid:h,os:g,modes:v,platform:\"web\",pageType:m,pageExperiments:t},y=new URLSearchParams([[\"expType\",\"AppConfig\"],[\"expInstance\",\"default\"],[\"apptype\",i],[\"v\",c]]),C=Oe(),S=C.includes(\"?item=\")||C.includes(\"&item=\")?\"&citem=dev:true&instance=Int1\":\"\",k=`${w.path}${w.v}/config/?${y}&targetScope=${JSON.stringify(b)}${S}`,I=e=>{const t=new URL(`https://${e}${k}`),{timeout:o=5e3}=n;let i;if(window.crsRequestData={url:t.toString()},o>=0){const e=new AbortController;setTimeout((()=>e.abort()),o),i=e.signal}return fetch(t,{signal:i})},A=\"msn.\"+(function(e,t){return!!(e.hostname.toLowerCase().endsWith(\".cn\")||t.isChinaCompliance||t.useCnDomain||e.search.match(/isChinaBuild=true/i)||e.search.match(/item=revip:cn/))}(location,e)?\"cn\":\"com\"),{domain:q}=w,E=q&&new URL(q).host;let x=I(E||`assets.${A}`);n.disableFallback||(x=x.catch((()=>{const{hostname:e}=location,t=e.match(/\\.msn\\.(com|cn)$/)?e:`www.${A}`;return I(t)})));n.silenceUnhandledError&&(x=x.catch((e=>{console.error(\"InlineHeadCommon: CRS fetch error in silence\",e)})));window.crsPromise=x}(r,u,c),t.inlineCRS=!0)}catch(e){console.error(\"InlineHeadCommon: \"+e)}}();const Ot=document.head.attributes[\"data-info\"].value;let Nt;if(Ot){const e=Ot.split(\";\").find((e=>e.startsWith(\"f:\")));e&&(Nt=e.substring(2),Nt.indexOf(\"1sw-pcsreturnhp\")>-1&&V(\"PCS returned. Executing inline JS.\",8e4,{customMessage:location.href},j.Deprecated))}function Wt(e){var t;return((null===(t=document.head)||void 0===t||null===(t=t.getAttribute(\"data-info\"))||void 0===t?void 0:t.split(\";\").find((e=>e.startsWith(\"f:\"))))||\"\").includes(e)}if(Wt(\"hp-noww\")||(new wt).init(),window.onerror=K,void 0!==window.onunhandledrejection&&(window.onunhandledrejection=X),window.onErrorHandler=G,function(e){const t=[\"vendors\",\"microsoft\",\"common\"];e&&t.push(...Q),window.addEventListener(\"load\",(()=>{window._pageTimings.navType=te(),window.requestIdleCallback((()=>{const e=function(e){const t=window.homePageWebpackChunks;if(!t)return[...e,z];if(t.length>e.length)return[];t.forEach((t=>e.splice(e.indexOf(t[0][0]),1))),t.push===Array.prototype.push&&e.push(z);return e}(t);e.length&&e.forEach((e=>ee(e)))}))}),{once:!0})}(),Wt(\"prg-hp-fvrf\")||async function(){window.apntag=window.apntag||{anq:[]},window.apntag.anq=window.apntag.anq||[],await async function(e){if(!a())return;const{src:t,id:n,async:o=!0,container:i=document.head,retryNumber:r=0,retryDelayMs:s=0}=e;try{await async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:100;if(t<=1)return e();let o,i=1;do{try{o=await e()}catch(e){if(i===t)throw e&&e.message&&(e.message=`${e.message} after ${i} attempts`),e;await ft(n)}}while(!o&&++i<=t);return o}((()=>function(e,t,n){let o=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];return new Promise(((i,r)=>{const s=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:\"anonymous\",i=arguments.length>4?arguments[4]:void 0,r=arguments.length>5?arguments[5]:void 0;if(!a())throw new Error(\"createScriptTag is unsupported server side. This call should be wrapped in canUseDOM().\");const s=document.createElement(\"script\");return t&&(s.id=t),i&&s.setAttribute(\"class\",i),r&&(s.onload=r),s.type=\"text/javascript\",e&&(window&&window.trustedTypesURLValidation?s.src=window.trustedTypesURLValidation(e,\"scriptSrcUrlPolicy\"):s.src=e,s.async=n,s.crossOrigin=o),window&&window.NONCE_ID&&(s.nonce=window.NONCE_ID),s}(e,t,o,void 0,void 0,(()=>i(!0)));s.crossOrigin=void 0,s.onerror=r,n.prepend(s)}))}(t,n,i,o)),r,s)}catch(e){V(\"Failed to download Static JS\",11103,{scriptId:n,scriptSrc:t},j.Alert)}}({id:\"display-sdk\",src:\"https://adsdk.microsoft.com/ast/ast.js\",container:document.head,retryDelayMs:200,retryNumber:5})}(),Wt(\"prg-hp-ssr\")||Wt(\"prg-c-hp-ssr\")){var Rt;const e=null===(Rt=navigator.serviceWorker)||void 0===Rt?void 0:Rt.controller;setTimeout((()=>{V(\"Early Log - No Alert\",19018,{swURL:null==e?void 0:e.scriptURL,swState:null==e?void 0:e.state,timeToSendEarlyLog:performance.now()},j.Deprecated)}),Wt(\"prg-c-hp-ssr\")?30:0)}}();\n        </script>\n\t\t<script src=\"/bundles/v1/homePage/latest/SSR-extension.6f92a39cf2897aa9b6ce.js\" type=\"text/javascript\" onerror=\"onErrorHandler(this.src)\" nonce=\"\" crossorigin=\"anonymous\"></script>\n\n\n<link rel=\"dns-prefetch\" href=\"//img-s-msn-com.akamaized.net\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//sb.scorecardresearch.com\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//th.bing.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//img-s-msn-com.akamaized.net\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//sb.scorecardresearch.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//th.bing.com\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//api.msn.com\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//assets.msn.com\" crossorigin=\"anonymous\"><link rel=\"dns-prefetch\" href=\"//assets.msn.com\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//c.bing.com\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//c.msn.com\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//www.bing.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//api.msn.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//assets.msn.com\" crossorigin=\"anonymous\"><link rel=\"preconnect\" href=\"//assets.msn.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//c.bing.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//c.msn.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//www.bing.com\" crossorigin=\"use-credentials\">\n        <script type=\"text/javascript\" nonce=\"\">\n            if(matchMedia(\"(prefers-color-scheme:dark)\").matches){const s=document.createElement(\"style\");document.head.appendChild(s);s.type=\"text/css\";s.appendChild(document.createTextNode(\"body{background:#242424}\"));}\n        </script>\n\n        <script type=\"text/javascript\" nonce=\"\">\n            function preloadConfigIDB(){const o=window.indexedDB;if(!o)return;const e=\"Configurations_12\",n=\"IndexedDB-\"+e+\".\",r=window._appTimings||(window._appTimings={});r[n+\"open-Start\"]=performance.now(),window._idbConfigLookup={};const i=window._idbConfigLookup;let t,s;window._idbConfigReadPromise=new Promise((o,e)=>{t=o,s=e}),window._idbConfigOpenPromise=(()=>{try{const d=o.open(\"wwConfigs\",1);return new Promise((o,c)=>{d.onerror=(()=>{c(new Error(\"Config IDB preload open failure: \"+d.error))}),d.onsuccess=(()=>{r[n+\"open-End\"]=performance.now();const c=d.result;o(c),r[n+\"getAllObjects-Start\"]=performance.now();const a=c.transaction(e,\"readonly\").objectStore(e).openCursor();a.onsuccess=function(){const o=this.result;o?(i[o.key]=o.value,o.continue()):(r[n+\"getAllObjects-End\"]=performance.now(),t())},a.onerror=(()=>{s(new Error(\"Config IDB preload read failure\"))})}),d.onupgradeneeded=(()=>{d.result.createObjectStore(e)})})}catch(o){return Promise.reject(new Error(\"Config IDB preload open failure: \"+o))}})()}preloadConfigIDB();\n        </script>\n\n\n<link crossorigin=\"anonymous\" rel=\"prefetch\" as=\"script\" href=\"//assets.msn.com/bundles/v1/homePage/latest/experiences_me-stripe-wc_dist_index_js.3c78460570d54ce4fa53.js\"><link crossorigin=\"anonymous\" rel=\"prefetch\" as=\"script\" href=\"//assets.msn.com/bundles/v1/homePage/latest/experiences_display-ads-wc_dist_index_js.8e27877371082d5c664b.js\"><meta property=\"sharing_url\" content=\"https://www.msn.com/ja-jp\"><meta property=\"og:url\" content=\"https://www.msn.com/ja-jp\"><meta property=\"og:title\" content=\"MSN | パーソナライズされたニュース、トップ ヘッドライン、ライブ アップデートなど\"><meta property=\"twitter:card\" content=\"summary_large_image\"><meta property=\"og:type\" content=\"website\"><meta property=\"og:site_name\" content=\"MSN\"><meta property=\"og:image\" content=\"https://assets.msn.com/staticsb/statics/latest/homepage/msn-logo.svg\"><meta name=\"robots\" content=\"index, follow\"><meta name=\"description\" content=\"信頼できるニュース、天気、スポーツ、金融、旅行、エンターテイメント、ゲーム、ビデオ コンテンツで最適にパーソナライズされ選別されたコレクション\"><style> @font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Light vi Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-Light-WF-subset_vi.woff2) format('woff2');font-weight:100;font-display:swap;unicode-range:U+00F0,U+00FE,U+0102,U+0103,U+0110,U+0111,U+0129,U+0169,U+01AF,U+01B0,U+1EA0-U+1EF9,U+20AB}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Semilight vi Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-Semilight-WF-subset_vi.woff2) format('woff2');font-weight:200;font-display:swap;unicode-range:U+00F0,U+00FE,U+0102,U+0103,U+0110,U+0111,U+0129,U+0169,U+01AF,U+01B0,U+1EA0-U+1EF9,U+20AB}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI vi Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-WF-subset_vi.woff2) format('woff2');font-weight:400;font-display:swap;unicode-range:U+00F0,U+00FE,U+0102,U+0103,U+0110,U+0111,U+0129,U+0169,U+01AF,U+01B0,U+1EA0-U+1EF9,U+20AB}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Semibold vi Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-Semibold-WF-subset_vi.woff2) format('woff2');font-weight:600;font-display:swap;unicode-range:U+00F0,U+00FE,U+0102,U+0103,U+0110,U+0111,U+0129,U+0169,U+01AF,U+01B0,U+1EA0-U+1EF9,U+20AB}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Bold vi Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-Bold-WF-subset_vi.woff2) format('woff2');font-weight:700;font-display:swap;unicode-range:U+00F0,U+00FE,U+0102,U+0103,U+0110,U+0111,U+0129,U+0169,U+01AF,U+01B0,U+1EA0-U+1EF9,U+20AB}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Light Eastern Europe Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-Light-WF-subset_east-europe.woff2) format('woff2');font-weight:100;font-display:swap;unicode-range:U+0104,U+0106,U+0107,U+0105,U+0118,U+0119,U+0141,U+0142,U+015A,U+015B,U+0179,U+017A,U+017B,U+017C}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Semilight Eastern Europe Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-Semilight-WF-subset_east-europe.woff2) format('woff2');font-weight:200;font-display:swap;unicode-range:U+0104,U+0106,U+0107,U+0105,U+0118,U+0119,U+0141,U+0142,U+015A,U+015B,U+0179,U+017A,U+017B,U+017C}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Eastern Europe Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-WF-subset_east-europe.woff2) format('woff2');font-weight:400;font-display:swap;unicode-range:U+0104,U+0106,U+0107,U+0105,U+0118,U+0119,U+0141,U+0142,U+015A,U+015B,U+0179,U+017A,U+017B,U+017C}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Semibold Eastern Europe Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-Semibold-WF-subset_east-europe.woff2) format('woff2');font-weight:600;font-display:swap;unicode-range:U+0104,U+0106,U+0107,U+0105,U+0118,U+0119,U+0141,U+0142,U+015A,U+015B,U+0179,U+017A,U+017B,U+017C}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Bold Eastern Europe Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-Bold-WF-subset_east-europe.woff2) format('woff2');font-weight:700;font-display:swap;unicode-range:U+0104,U+0106,U+0107,U+0105,U+0118,U+0119,U+0141,U+0142,U+015A,U+015B,U+0179,U+017A,U+017B,U+017C}@font-face{font-family:Segoe UI;font-style:normal;src:url(https://assets.msn.com/statics/fonts/SegoeUI-Roman-VF-subset_web.woff2) format('woff2');font-weight:100 900;font-display:swap}body,html{margin:0;font-family:Segoe UI,Segoe UI Midlevel,Segoe WP,Arial,sans-serif;background:#d5d4d0}body,html,fluent-design-system-provider{min-height:100vh} @media (prefers-color-scheme:dark){body,html{background:none}}</style><link rel=\"preconnect\" href=\"https://srtb.msn.com\" crossorigin=\"\"><link rel=\"preload\" as=\"image\" href=\"https://img-s-msn-com.akamaized.net/tenant/amp/entityid/AA1GHmfo.img?w=240&amp;h=129&amp;q=90&amp;m=6&amp;f=jpg&amp;x=676&amp;y=279&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AA1nCUXH?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/BB11nWhi?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AAYiJ0y?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AA17CMtD?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/BBIv7fH?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AA1y1QEc?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AAWZVgG?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AAN9UCs?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><script type=\"text/javascript\" src=\"https://assets.msn.com/staticsb/statics/latest/auth/msal-browser-2.18.0.min.js\" async=\"\" crossorigin=\"anonymous\"></script><link rel=\"preload\" as=\"image\" href=\"https://img-s-msn-com.akamaized.net/tenant/amp/entityid/AA1GGx2p.img?w=240&amp;h=129&amp;q=90&amp;m=6&amp;f=jpg&amp;x=485&amp;y=404&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/BB15T8kT?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/BBT1bNV?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AA1doaEz?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/BB3hGDK?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AA1DcdOR?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"https://img-s-msn-com.akamaized.net/tenant/amp/entityid/AA1GHueT.img?w=240&amp;h=129&amp;q=90&amp;m=6&amp;f=jpg&amp;x=466&amp;y=396&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AAMc62e?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AA125Oco?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AAYiGep?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AACEI6d?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"https://img-s-msn-com.akamaized.net/tenant/amp/entityid/AA1GHKVB.img?w=612&amp;h=328&amp;q=90&amp;m=6&amp;f=jpg&amp;x=533&amp;y=361&amp;u=t\"><script src=\"//btloader.com/tag?o=6208086025961472&amp;upapi=true\"></script><script src=\"//btloader.com/tag?o=6208086025961472&amp;upapi=true\"></script><script async=\"\" type=\"text/javascript\" charset=\"UTF-8\" src=\"https://js.monitor.azure.com/scripts/c/ms.shared.analytics.mectrl-3.gbl.min.js\" crossorigin=\"anonymous\"></script><script async=\"\" type=\"text/javascript\" charset=\"UTF-8\" src=\"https://mem.gfx.ms/scripts/me/MeControl/10.25136.1/ja-JP/meBoot.min.js\" crossorigin=\"anonymous\"></script><script async=\"\" type=\"text/javascript\" charset=\"UTF-8\" src=\"https://mem.gfx.ms/scripts/me/MeControl/10.25136.1/ja-JP/meCore.min.js\" crossorigin=\"anonymous\"></script></head>\n<body style=\"background-color: rgb(255, 255, 255);\">\n    \n\n\n\n<div id=\"root\" data-config-indexdocid=\"\">\n</div>\n\n\n\n\n\n\n\n\n    <div id=\"ssr\" data-ssr-entry=\"/bundles/v1/homePage/latest/SSR-service-entry.253e46f1e0b461971ee7.js\" hidden=\"\"></div>\n\n\n\n    <script src=\"https://assets.msn.com/bundles/v1/homePage/latest/vendors.5f65a1eeb250d53008c6.js\" type=\"text/javascript\" nonce=\"\" async=\"\" crossorigin=\"anonymous\"></script>\n    <script src=\"https://assets.msn.com/bundles/v1/homePage/latest/microsoft.670babd32e6bda4b5968.js\" type=\"text/javascript\" nonce=\"\" async=\"\" crossorigin=\"anonymous\"></script>\n    <script src=\"https://assets.msn.com/bundles/v1/homePage/latest/common.2056e58bd9efbcf6895f.js\" type=\"text/javascript\" nonce=\"\" async=\"\" crossorigin=\"anonymous\"></script>\n        <script src=\"https://assets.msn.com/bundles/v1/homePage/latest/experience.1b32e7a7d204a942f20c.js\" type=\"text/javascript\" nonce=\"\" async=\"\" crossorigin=\"anonymous\"></script>\n\n\n\n\n\n\n\n\n\n<fluent-design-system-provider fill-color=\"#F7F7F7\" direction=\"ltr\" base-layer-luminance=\"1\"><entry-point config-instance-src=\"waterfall\" instance-id=\"EntryPointHpWC\" data-t=\"{&quot;n&quot;:&quot;EntryPointHpWC&quot;,&quot;t&quot;:8}\"></entry-point></fluent-design-system-provider><script src=\"https://assets.msn.com/staticsb/statics/latest/adboxes/egb/detroitchicago/memphis.js\"></script><script src=\"https://assets.msn.com/staticsb/statics/latest/adboxes/ubo/zlk/zlk.js\"></script><script src=\"https://assets.msn.com/staticsb/statics/latest/adboxes/general.js?/nwm-pw2.min.js\"></script><div id=\"L2RldHJvaXRjaGljYWdvL21lbXBoaXMuanM=\" style=\"display: none;\"></div><div id=\"L253bS1wdzIubWluLmpz\" style=\"display: none;\"></div><div id=\"L3psay96bGsuanM=\" style=\"display: none;\"></div><img src=\"https://ad-delivery.net/px.gif?ch=2\" style=\"display: none !important; width: 1px !important; height: 1px !important;\"><img src=\"https://ad.doubleclick.net/favicon.ico?ad=300x250&amp;ad_box_=1&amp;adnet=1&amp;showad=1&amp;size=250x250\" style=\"display: none !important; width: 1px !important; height: 1px !important;\"><img src=\"https://ad-delivery.net/px.gif?ch=1&amp;e=0.8073932660220682\" style=\"display: none !important; width: 1px !important; height: 1px !important;\"><script id=\"me-control-script\" type=\"text/javascript\" src=\"https://mem.gfx.ms/meversion?partner=msnews&amp;market=ja-jp\" async=\"\" crossorigin=\"anonymous\"></script><iframe id=\"mecontrol-iframe-0\" sandbox=\"allow-forms allow-scripts allow-same-origin\" src=\"https://mem.gfx.ms/me/mecache?partner=msnews&amp;wreply=https%3A%2F%2Fwww.msn.com\" style=\"display: none;\"></iframe></body></html>\n</tool_result>", "reasoningContent": "", "id": "00bf05cd-3823-49a5-853b-ece005fc6139", "metadata": {}}, {"role": "user", "applyStatus": 0, "content": null, "promptContent": "[use_mcp_tool for 'github.com/executeautomation/mcp-playwright'] Result:\nHTML content:\n<!DOCTYPE html><html lang=\"ja-jp\" dir=\"ltr\" style=\"background-color: rgb(255, 255, 255);\"><head data-info=\"f:msnallexpusers,prg-sp-liveapi,prg-hp-fsnossr,prg-hp-nofs,prg-hp-ssr,prg-hp-stsw,ads-cfv42-c,prg-views-respad,prg-1sw-sarfmrt3,prg-adspeek,btrecrow3,prg-ad-cta-logo-va-c,1s-fcrypt,prg-upsaip-w1-t,1s-rpssecautht,traffic-p2-osc-c,prg-pr2-optreload,preprg-health-staging,cprg-ad-win-ban,chatn_v2_t1,1s-wpo-pr2-maibar-t3,prg-pr2-ntf-morebn3nf,1s-logdb-preview-mbn3nf,prg-pr2-ntf-mbn,prg-pr2-ntf-mbn3,prg-csacclink,prg-1s-acclnk,prg-accountlink,1s-shp-rc-t-gsant88,prg-sh-rmitmlnk,nopinglancecardit,prg-cg-notf-landing-t,prg-cg-notf-sub,prg-cg-notf-sub-btn,prg-ad-cms-cnt,prg-1sw-ucsem,ads-nopostsq-t,ads-nopostsq,1s-uasdisf-t,ads-creativelog-c,ads-usepme,ads-anjson-migt,sh-bdvid,prg-sh-bd-video,ads-nooutbrain,release-outlook-app,ads-prcrid-bi,prg-cg-ab-testing,prg-1sw-tvid-t20,1s-p1-vid-vs,prg-1sw-tvid-p1,prg-1sw-videopb,prg-1sw-videosxap,prg-p1-txt2,prg-p1-uc3,prg-pr1-videos,prg-tv-api,prg-tv-segcap10,prg-vid-cd,prg-vid-trdcache,prg-1sw-reclaim2,prg-1sw-reclaim,1s-p2-bg-appanon,ads-bcn-cndomain,prg-cg-int-ad-pod,prg-1sw-tvid-t20-vp,1s-p2-vid-vp,prg-1sw-tvid-p2,prg-p2-txt2,prg-p2-uc3,prg-pr2-setdur-t2,prg-pr2-videos,prg-pr2-wtab-oo,prg-tv-p2-api,prg-tv-p2-segcap10,msph-benchmark,prg-pr2-lifecyclebac,1s-routenotexpc,prg-1sw-sa-dnec,prg-1sw-crypinf,prg-cg-lock_c,prg-wx-nsever,prg-1sw-cryptren,traffic-n-tthero-t,prg-ntp-tfherott-on,msph-adsrevpd,prg-cg-cfzhcnfx,prg-cg-lstfix,prg-1sw-tbrfltr,prg-1sw-tvid-int-t1,1s-p1-vid-vs-int,prg-1sw-tvid-p1-int,prg-1sw-tvid-int-t2,1s-p2-vid-vp-int,prg-1sw-tvid-p2-int,prg-fin-rmar,1s-vid-dwt,prg-vdw-enable,prg-pr2-wwidgets-t,bing_native_chat_t,prg-1s-dwvid-wpo,1s-wpo-ntp-videos,1s-newsfeed-worknews,bing_uni_iab_t,prg-pr2-imghttd-t,prg-pr2-imghtdd-t,prg-1sw-wxaqifct,prg-wx-btide,prg-wx-ntide,prg-cg-countermoniccc,prg-1sw-safitopo,prg-1sw-sasptopo,prg-1sw-newe2e,1s-ls-uppermuid,prg-vdw-auto-cur,prg-vdw-automated,prg-1sw-wxaqepp,prg-1sw-wxaqoff,prg-wx-saqi0,prg-wx-saqi,prg-wx-nhroll,1s-wxmixcache,prg-1sw-wxncvf,prg-1sw-wxtrlog,prg-cg-rps3,prg-ad-dv-on,prg-ad-infopane-dv,msph-follv2,prg-1sw-wxtrlogp,1s-ntf2-pradasimiv2,1s-ntf1-pradasimiv2,prg-1sw-santf-vd4u-c,prg-pr2-ntf-rel,prg-1sw-pawpor1,prg-1sw-spawpor1,prg-1sw-dwvid-wpo-2,1s-wpo-ntp-videos2,prg-hp-c-lockscreen,prg-1sw-sfexpdat,1s-ntfa-fpmeta,prg-1sw-analysisdata,prg-ad-ssr-dpr-rf,1s-ads-dpr-r,prg-1sw-cs-rank,1s-cs-video,1s-xaparray-c,prg-1sw-p2-txt3,prg-fin-ruby,ads-3pdmn-filter,ads-3pctve-filter,cprg-xdr303a,cprg-xdr303,prg-1sw-wxdtdeprev,prg-1sw-otrim,prg-1sw-wxwtnyrev,prg-pw-bcn-card,prg-beacon-card,ads-3pdmn-hld-c,prg-1sw-l3v1ship,1s-wpo-pr1-layout3wv1,prg-1sw-version-fix,prg-1sw-l3v2,1s-wpo-pr1-layout3wv2;\" data-canvas-info=\"f:prg-hp-ssr,prg-accountlink,prg-ad-infopane-dv\" data-ads-numberline=\"f:moneyhz3=ads-cfv42-c,5qos=ads-nopostsq-t,5sk0=ads-creativelog-c,5sov=ads-usepme,5ui9=ads-anjson-migt,63hy=ads-nooutbrain,649x=ads-prcrid-bi,6965=ads-bcn-cndomain,7khf=ads-3pdmn-filter,7khg=ads-3pctve-filter,7n5l=ads-3pdmn-hld-c;\" data-client-settings=\"{&quot;aid&quot;:&quot;215E82E2BBE34EA190516A86CB73D223&quot;, &quot;fd_muid&quot;:&quot;3FDB89BC40236CDB115E9FB041BA6DA2&quot;, &quot;static_page&quot;:&quot;false&quot;, &quot;queryparams&quot;:&quot;&quot;, &quot;apptype&quot;:&quot;homePage&quot;, &quot;pagetype&quot;:&quot;hp&quot;, &quot;configRootUrl&quot;:&quot;https://assets.msn.com/config/v1/&quot;, &quot;feedBaseDomain&quot;:&quot;&quot;, &quot;deviceFormFactor&quot;:&quot;desktop&quot;, &quot;pageGenTime&quot;:&quot;2025-06-15T03:12:17Z&quot;, &quot;pcsInfo&quot;:{&quot;v&quot;:&quot;********.5_master&quot;, &quot;env&quot;:&quot;prod&quot;}, &quot;locale&quot;:{&quot;language&quot;:&quot;ja&quot;, &quot;market&quot;:&quot;jp&quot;, &quot;content&quot;:{&quot;language&quot;:&quot;ja&quot;, &quot;market&quot;:&quot;jp&quot;}, &quot;display&quot;:{&quot;language&quot;:&quot;ja&quot;, &quot;market&quot;:&quot;jp&quot;}}, &quot;featureFlags&quot;:{}, &quot;market&quot;:{ &quot;requestInput&quot;:{ &quot;language&quot;:&quot;ja&quot;,&quot;region&quot;:&quot;jp&quot;, &quot;geo_country&quot;:&quot;JP&quot;}, &quot;detection&quot;:{ &quot;type&quot;:&quot;url&quot;,&quot;geo_country&quot;:&quot;&quot;,&quot;geo_countrycf&quot;:&quot;&quot;, &quot;recommendLanguageCoachmark&quot;: &quot;false&quot;}}, &quot;servicesEndpoints&quot;:{&quot;staticConfig&quot;: {&quot;domain&quot;: &quot;https://assets.msn.com&quot;, &quot;path&quot;: &quot;/config/v3/&quot;, &quot;v&quot;: &quot;&quot;},&quot;crs&quot;: {&quot;domain&quot;: &quot;https://assets.msn.com&quot;, &quot;path&quot;: &quot;/resolver/api/resolve/&quot;, &quot;v&quot;: &quot;v3&quot;},&quot;feedService&quot;: {&quot;domain&quot;: &quot;https://assets.msn.com&quot;, &quot;path&quot;: &quot;&quot;, &quot;v&quot;: &quot;&quot;},&quot;enterpriseFeedService&quot;: {&quot;domain&quot;: &quot;https://ent-api.msn.com/&quot;, &quot;path&quot;: &quot;&quot;, &quot;v&quot;: &quot;&quot;}}, &quot;bundleInfo&quot;:{&quot;v&quot;:&quot;20250613.83&quot;, &quot;alias&quot;:&quot;latest&quot;}, &quot;os&quot;:&quot;windows&quot;, &quot;browser&quot;:{&quot;browserType&quot;:&quot;chrome&quot;, &quot;version&quot;:&quot;134&quot;, &quot;ismobile&quot;:&quot;false&quot;}, &quot;domain&quot;:&quot;www.msn.com&quot;, &quot;requestTrailInfo&quot;:{&quot;flightVersion&quot;:&quot;53531360&quot;}, &quot;detection&quot;:{&quot;scrubbedIp&quot;:&quot;*************&quot;, &quot;tier&quot;:&quot;b&quot;}, &quot;geo_country&quot;:&quot;JP&quot;, &quot;geo_countryname&quot;:&quot;Japan&quot;, &quot;geo_subdivision&quot;:&quot;Tokyo&quot;, &quot;geo_zip&quot;:&quot;151-0053&quot;, &quot;geo_city&quot;:&quot;Tokyo&quot;, &quot;geo_lat&quot;:&quot;35.6807&quot;, &quot;geo_long&quot;:&quot;139.6943&quot;, &quot;geo_citycf&quot;:&quot;8&quot;, &quot;geo_countrycf&quot;:&quot;8&quot;, &quot;requestMuid&quot;:&quot;3FDB89BC40236CDB115E9FB041BA6DA2&quot;}\" data-diagnostics=\"{&quot;mc&quot;:{&quot;spt&quot;:&quot;ja-jp%7c%7cjp%7cja-jp%7cja-jp%7cja%7c%7ccf%3d8%7cRefA%3dAA3942D0BC7E42E28D3224414F432CAA.RefC%3d2025-06-15T03%3a12%3a17Z&quot;}}\"><script id=\"display-sdk\" type=\"text/javascript\" src=\"https://adsdk.microsoft.com/ast/ast.js\" async=\"\"></script>\n    <meta charset=\"utf-8\">\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n\n    \n    \n\n    <title>MSN | パーソナライズされたニュース、トップ ヘッドライン、ライブ アップデートなど</title>\n\n    \n    \n\n\n    \n            <link rel=\"stylesheet\" href=\"https://assets.msn.com/bundles/v1/homePage/latest/experience.69f3918934450044f420.css\" crossorigin=\"anonymous\">\n    \n\n    \n\n    <script type=\"text/javascript\" nonce=\"\">\n        try {\n            window._pageTimings = { TTJSStart: Math.round(performance.now()) };\n            window._clientSettings = JSON.parse(document.head.dataset.clientSettings);\n        } catch (e) {\n            console.error(\"Error in adding TTJSStart marker\");\n        }\n    </script>\n\n        <script nonce=\"\">\n            function getCookieConsentRequired(){return false;}\n            window._ssrServiceEntryUrl=\"/bundles/v1/homePage/latest/SSR-service-entry.253e46f1e0b461971ee7.js\";\n        </script>\n\n\n        <script type=\"text/javascript\" id=\"inlinehead-inline-script\" nonce=\"\">\n            window[\"_webWorkerBundle\"] = \"/bundles/v1/homePage/latest/web-worker.063abe45ff34c5111974.js\";\n            window[\"_authCookieName\"] = \"ace\";\n            !function(){\"use strict\";var e={},t={};function n(o){var i=t[o];if(void 0!==i)return i.exports;var r=t[o]={exports:{}};return e[o](r,r.exports,n),r.exports}let o,i;function r(){if(i)return i;const e=document.head.getAttribute(\"data-info\");return i=((/f:\\s*([^;]+)/i.exec(e)||{})[1]||\"\").toLowerCase(),i}n.g=function(){if(\"object\"==typeof globalThis)return globalThis;try{return this||new Function(\"return this\")()}catch(e){if(\"object\"==typeof window)return window}}(),function(){if(void 0!==n){const e=n.e,t={};n.e=function(o){return e(o).catch((function(e){const i=t.hasOwnProperty(o)?t[o]:2;if(i<1)throw e;return new Promise((function(e){setTimeout((function(){t[o]=i-1,e(n.e(o))}),100)}))}))}}}();let s=new Map;const c=Object.freeze({set(e,t){return s.set(e,t),this},get:function(e,t){let n=s.get(e);return void 0===n&&t&&(n=t(),s.set(e,n)),n},clear(){s.clear()},delete:e=>s.delete(e),has:e=>s.has(e)});function a(){return!(\"undefined\"==typeof window||!window.document||!window.document.createElement||window.isRenderServiceEnv)}function l(e){let t,n=!1;const o=function(){return n||(n=!0,t=e()),t};return o.cache={clear:()=>{n=!1,t=void 0}},o}const d=\"OnOOutlookMobile\",u=\"OnOStartApp\",h=\"xmweather-\",g=\"launcherntp\",p=\"launcherInterests\",m=\"swiftKey\",w=\"superappdhp\",f=\"_ocidIsomorphicKey\",v=()=>(c.get(f)||c.set(f,W(\"ocid\").toLowerCase()),c.get(f)),b=\"_platformFlagKey\",y=\"_hostAppNameKey\",C=\"_clientTypeKey\",S=()=>(c.get(b)||c.set(b,W(\"chpltfm\")),c.get(b)),k=()=>{if(!c.get(y)){const e=S();c.set(y,e.toLowerCase().split(\"-\")[0])}return c.get(y)},I=()=>{if(!c.get(C)){const e=S();c.set(C,e.toLowerCase().split(\"-\")[1])}return c.get(C)};function A(){if(!a()||O())return!1;const e=window.sapphireWebViewBridge,t=window.webkit,n=e&&e.send,o=t&&t.messageHandlers&&t.messageHandlers.send&&t.messageHandlers.send.postMessage;return!(!n&&!o)}function q(){return!!window._isCopilotBridge||\"copilot\"===W(\"MBApp\")}function E(){const e=S()&&\"outlook\"===k()&&[\"ios\",\"android\",\"ipados\"].includes(I()),t=v()===d.toLowerCase();return e||t}function x(){return!(!a()||O())&&(A()||v()===u.toLowerCase()||v()===w.toLowerCase())}function L(){const e=W(\"edge\");return(\"emmx\"===k()||\"1\"===e)&&x()||\"mmx\"===v()}function _(){return\"3rdp\"===k()||v().startsWith(h)||O()}function T(){return[g.toLowerCase(),p.toLowerCase()].includes(v())&&x()}function O(){return v().toLowerCase()===m.toLowerCase()||\"swiftkey\"==k().toLowerCase()}const N=l((()=>A()||E()||x()||v()===\"weather-app-win\".toLowerCase()||v()===\"finance-app-win\".toLowerCase()||L()||_()||T()||O()||-1!==window.location.href.indexOf(\"metaos=true\")||q()));function W(e){try{return new URL(location.href).searchParams.get(e)||\"\"}catch(e){return\"\"}}function R(e,t,n){const o=n&&\"phone\"===n.toLowerCase(),i=t&&\"enterprise\"===t;let r=\"Edge\";O()?r=\"swiftKey\":T()?r=\"Launcher\":function(){var e;if(!a())return!1;if(\"bingHomepageMobile\"===(null===(e=window._clientSettings)||void 0===e?void 0:e.pagetype))return!0;{var t;const e=null===(t=document.getElementsByClassName(\"peregrine-widget-settings\")[0])||void 0===t?void 0:t.getAttribute(\"data-client-settings\");if(e){const t=JSON.parse(e);return t&&t.pagetype&&\"bingHomepageMobile\"===t.pagetype}return!1}}()&&(r=\"bingHomepageMobile\");return{bingHomepage:\"binghomepage\",mmx:\"emmx\",edge:\"spartan\",edgeChromium:i?\"entnews\":\"anaheim\",hybrid:\"spartan\",hub:o?\"prime_mobile\":\"prime\",microsoftNews:\"msnews\",office:\"entnews\",views:o?\"prime_mobile\":\"prime\",homePage:o?\"prime_mobile\":\"prime\",windowsShell:\"windowsshell\",edgeMobile:r}[e]}const M={homePage:{vertical:\"homepage\",category:\"\",id:\"\",domainId:\"13041\",title:\"undefined\"!=typeof document?document.title:\"\"}};function P(e){let t=e;switch(e){case\"windowsshellhp\":t=\"dhp\";break;case\"video\":t=\"watch\";break;case\"EdgeMobile\":t=L()?\"ntp\":\"dhp\"}return t}function U(){try{var e;const t={};return null===(e=Error.captureStackTrace)||void 0===e||e.call(Error,t,U),t.stack}catch(e){return(new Error).stack}}function D(e){try{return decodeURIComponent(e)}catch(e){}}let F,$;function H(e,t){if(!e)return null;if(t)return function(e){var t;const n=null===(t=document.cookie)||void 0===t?void 0:t.match(`\\\\b${e}=([^;]*)`);return n?D(n[1]):null}(e);if(!F){const e=\"undefined\"!=typeof document&&document.cookie.split(\"; \");F={};const t=e&&e.length;for(let n=0;n<t;n++){const t=e[n].indexOf(\"=\");F[e[n].slice(0,t).toLocaleLowerCase()]=D(e[n].slice(t+1))}}const n=e.toLocaleLowerCase();return F[n]||null}function B(e){const t=H(e);return t&&encodeURIComponent(t)||null}var j;!function(e){e[e.Alert=0]=\"Alert\",e[e.Deprecated=1]=\"Deprecated\",e[e.HighImpact=2]=\"HighImpact\",e[e.Critical=3]=\"Critical\"}(j||(j={}));const J=new class{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20;this.maxLength=e,this.list=[]}push(e){this.list.push(e),this.list.length>this.maxLength&&this.list.shift()}get data(){return this.list}};function V(e,t,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:j.Alert,s=arguments.length>4?arguments[4]:void 0,c=arguments.length>5?arguments[5]:void 0,a=arguments.length>6?arguments[6]:void 0;try{const l=function(){if(!o){const e=document.head.getAttribute(\"data-client-settings\");e&&(o=JSON.parse(e))}return o}(),d=r(),u=function(e){if(e){const{pcsInfo:t,pageGenTime:n}=e,o=new Date(n).getTime(),i=!t||[\"prod\",\"prod-ssr\",\"prod-ssrntp\"].includes(t.env);$=i?\"browser.events.data.msn.com\":\"events-sandbox.data.msn.com\";return{cors:\"true\",\"content-type\":\"application/x-json-stream\",\"client-id\":\"NO_AUTH\",\"client-version\":\"1DS-Web-JS-2.2.2\",apikey:i?\"0ded60c75e44443aa3484c42c1c43fe8-9fc57d3f-fdac-4bcf-b927-75eafe60192e-7279\":\"f8857dedc6f54ca8962cfb713e01e7d7-e9250191-fe0b-446f-95ae-07516262f98c-7028\",\"upload-time\":o,w:\"0\",anoncknm:\"app_anon\"}}return null}(l);let h=\"\";u&&u.apikey&&\"\"!==u.apikey&&(h=function(e){if(e){const t=e.indexOf(\"-\");if(t>0)return e.substring(0,t)}return\"\"}(u.apikey));const g=function(e,t,n,o,i,r){let s=arguments.length>6&&void 0!==arguments[6]?arguments[6]:j.Alert,c=arguments.length>7&&void 0!==arguments[7]?arguments[7]:\"\";if(n){i=i||{};const{aid:u,apptype:h,audienceMode:g,pagetype:p,pageGenTime:m,bundleInfo:w,deviceFormFactor:f=\"\",fd_muid:v,os:b,static_page:y}=n;i.pageGenTime=m,i.build=w&&w.v,i.appType=h,i.pageVisible=document.visibilityState;const C=R(h,g,f),S=C||h,k=document.getElementsByTagName(\"html\")[0].getAttribute(\"lang\");let I,A=\"\",q=\"muid\";try{if(\"edgeChromium\"===h&&\"object\"==typeof window&&window.location&&window.location.search){const e=new URLSearchParams(window.location.search);I=e.has(\"startpage\")?\"msedgdhp\":\"msedgntp\",\"enterprise\"===g?I=\"entnewsntp\":\"xbox\"===b&&(I=\"xboxntp\")}window&&window.getCookieConsentRequired&&\"function\"==typeof window.getCookieConsentRequired&&window.getCookieConsentRequired()||(A=(null===(l=window)||void 0===l||null===(l=l.__SSRUserConfigEarlyLog)||void 0===l?void 0:l.consistentMuid)||(null===(d=window)||void 0===d?void 0:d.__muid)||H(\"muid\"))}catch{}A||(A=n.aid,q=\"aid\");const E={name:\"MS.News.Web.AppError\",time:m,ver:\"4.0\",iKey:`o:${o}`,data:{baseData:{},baseType:\"MS.News.Web.Base\",page:{name:\"default\",product:S,type:P(p),content:M[h]??{category:\"standaloneError\"},ocid:I,url:location.href,isStatic:y},browser:{clientId:A,clientIdType:q},flight:{id:r,tmpl:c},request:{activityId:u,requestId:u,afdMuid:v},locale:{mkt:k},extSchema:{id:e,severity:s,pb:i,message:t}}};var a;if(A&&\"muid\"===q)E.ext={...null==E?void 0:E.ext,user:{...null==E||null===(a=E.ext)||void 0===a?void 0:a.user,localId:`t:${A}`}};return\"object\"==typeof window&&(window.isSSREnabled&&(E.data.flight.tmpl+=\";ssr-enabled:1\"),window.isSSRCompleted&&(E.data.flight.tmpl+=\";ssr-completed:1\")),E?JSON.stringify(E):null}var l,d;return null}(t,e,l,h,n,d,i,a);if(c&&!function(e){if(null==e)return!1;return e.startsWith(\"1\")||e.startsWith(\"2\")||e.startsWith(\"3\")||e.startsWith(\"4\")}(null==l?void 0:l.aid))return console.error(g),void console.error(`This App error Id: ${t} will not be sent due to app error sampling!`);if(u&&g){console.error(g),J.push(g);const e=\"https://\"+$+\"/OneCollector/1.0\"+function(e){return\"?\"+Object.keys(e).map((function(t){return t+\"=\"+encodeURIComponent(e[t])})).join(\"&\")}(u);if(s&&!performance.getEntriesByType(\"visibility-state\").some((e=>\"visible\"===e.name))){const t=()=>{\"visible\"===document.visibilityState&&navigator.sendBeacon(e,g)};document.addEventListener(\"visibilitychange\",t,{once:!0})}else navigator.sendBeacon(e,g)}}catch{}}function G(e){V(\"Error when loading bundle: \"+e,20202,{...arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}})}function K(e,t,n,o,i){const r=(i&&i.stack)??U(),s={source:t,customMessage:e??(null==i?void 0:i.message),line:n,column:o,stack:r};V(\"JS Exception\"+(s.customMessage?`- ${s.customMessage.substring(0,60)}`:\"\"),20203,s)}function X(e){const t={customMessage:e&&e.reason&&e.reason.message,stack:(e&&e.reason&&e.reason.stack)??U()};V(\"JS Exception on unhandled promise rejection\"+(t.customMessage?`- ${t.customMessage.substring(0,60)}`:\"\"),20203,t)}const z=\"experience\",Q=[\"common-monthly\"],Y=Object.create(null),Z=2;function ee(e,t){if(!(t=t||Array.from(document.scripts).find((t=>t.src.indexOf(`/${e}.`)>-1))))return;const n=function(e,t){const n=document.createElement(\"script\");return n.type=\"text/javascript\",n.crossOrigin=\"anonymous\",n.src=e,n.onerror=()=>{let e=Y[t]||0;Y[t]=++e,e<Z?ee(t,n):ne()},n.onload=()=>{delete Y[t];const e=window._pageTimings.retriedBundles;window._pageTimings.retriedBundles=e?`${e},${t}`:t,ne()},n}((o=t.src,Y[e],o),e);var o;setTimeout((()=>t.replaceWith(n)),100)}function te(){const[e={}]=performance.getEntriesByType(\"navigation\");return e.type}function ne(){if(!Object.values(Y).every((e=>e===Z)))return;let e=\"\";Object.keys(Y).forEach((t=>{e=e?`${e},${t}`:t,delete Y[t]})),e&&V(`Error when loading bundle(s): ${e}`,20202,{timeElapsed:Math.round(performance.now()),navType:te()})}function oe(e){try{if(e)return JSON.parse(e)}catch(e){}return null}\"undefined\"!=typeof window&&window.document&&window.document.createElement&&(window._pageTimings=window._pageTimings||{},window.requestIdleCallback=window.requestIdleCallback||window.setTimeout);const ie=\"Authorization\";var re,se;!function(e){e.Presentation=\"presentation\"}(re||(re={})),function(e){e.Unknown=\"Unknown\",e.Portrait=\"Portrait\",e.Landscape=\"Landscape\"}(se||(se={}));var ce,ae;!function(e){e[e.Undefined=0]=\"Undefined\",e[e.Basic=1]=\"Basic\",e[e.Advanced=2]=\"Advanced\",e[e.Premium=3]=\"Premium\"}(ce||(ce={})),function(e){e.Init=\"init\",e.Config=\"config\",e.Targeted=\"targeted\",e.Sticky=\"sticky\",e.NoSticky=\"no_sticky\",e.Admin=\"admin\",e.Forced=\"forced\",e.Manual=\"manual\"}(ae||(ae={}));const le=new Set([\"finance-app-win\",\"weather-app-win\",\"winpstoreapp\"]),de=new Set([\"hpmsn\"]);new Set([\"chromentpnews\"]);function ue(){var e;return a()?(window._clientSettings=window._clientSettings||oe(document.head.dataset.clientSettings||(null===(e=document.getElementsByClassName(\"peregrine-widget-settings\")[0])||void 0===e?void 0:e.getAttribute(\"data-client-settings\"))),window._clientSettings):null}const he=\"feed/personalize/settings\";function ge(e,t){const n=t.replace(/[[\\]]/g,\"\\\\$&\"),o=new RegExp(\"[?&]\"+n+\"(=([^&#]*)|&|#|$)\").exec(e);if(!o)return null;const i=o[2];return decodeURIComponent(i.replace(/\\+/g,\" \"))||\"\"}let pe=new class{constructor(){a()?(this.isDebugEnabled=(ge(window.location.href,\"debug\")||\"\").toLowerCase()in{1:1,true:1}||\"vp\"===(ge(window.location.href,\"reqsrc\")||\"\").toLowerCase(),this.isDebugEnabled&&!n.g.TEST_ENV&&(window.webpackRequire=n)):this.isDebugEnabled=!1}getLoggingService(){return null}isDebug(){return this.isDebugEnabled}setDebug(e){this.isDebugEnabled=e}setTags(e){}log(e,t){this.isDebug()&&console.info(e)}logError(e){console.error(e)}logCallback(e){this.isDebug()&&console.info(e())}logObjects(){this.isDebug()&&console.log(...arguments)}logSingleObject(e,t){this.isDebug()&&console.log(e)}};class me{get supported(){return!!this.storage}constructor(e){this.storage=e}getItem(e){if(this.supported)return this.storage.getItem(e)}getObject(e,t){const n=this.getItem(e);if(null!=n){const e=oe(n);if(null!=e)return e}return t}key(e){if(this.supported&&e>=0)return this.storage.key(e)}keys(){return this.supported?Object.keys(this.storage):[]}setObject(e,t){void 0!==t?this.setItem(e,JSON.stringify(t)):this.removeItem(e)}removeObject(e){const t=this.removeItem(e);if(null!=t)return oe(t)}setItem(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(this.supported)try{if(!t)throw\"Attempted to store null/undefined value: \"+t;this.storage.setItem(e,t)}catch(e){if(!n)throw e;pe.logError(e)}else if(!n)throw new Error(\"WebStorage not supported\")}get length(){if(this.supported)return this.storage.length}removeItem(e){if(this.supported){const t=this.getItem(e);return this.storage.removeItem(e),t}}clear(){this.supported&&this.storage.clear()}removeSubstringKeys(e){if(!this.supported||!e)return;const t=[];for(let n=0;n<this.storage.length;n++){const o=this.key(n);o&&o.includes(e)&&t.push(o)}for(let e=0;e<t.length;e++)this.removeItem(t[e])}}let we=new class extends me{constructor(){let e=null;if(a())try{e=localStorage}catch(e){}super(e)}};const fe=\"1s-tokens\";var ve;let be,ye;function Ce(){return be=Se(),be&&be.accessToken?be:void 0}function Se(){return be=be||void 0===be&&we.getObject(fe)||null,be}!function(e){e.NotAvailable=\"notAvailable\",e.Expired=\"expired\",e.Valid=\"valid\"}(ve||(ve={}));const ke=\"currentaccount\",Ie=l((()=>{const e=H(ke,!0),t=e&&oe(e),{login_hint:n}=t||{};return n&&t})),Ae=l((()=>!!H(ke)));const qe=\"__RequestDataInstance__\";class Ee{constructor(e,t){if(this.url=new URL(e.href),this.innerHeight=e.innerHeight,this.devicePixelRatio=e.devicePixelRatio,this.canUseCssGrid=e.canUseCssGrid,this.requestId=e.requestId,this.cookie=e.cookie,this.referer=e.referer,this.userAgent=e.userAgent,this.clientData=e.clientData,this.vpCetoToken=e.vpCetoToken,this.oneServiceHeaders=Le(e.oneServiceHeaders)||{},this.isPssrMode=t,t){const e=xe(\"OSATE\",this.cookie),t=!!e&&\"1\"===e,n=xe(\"OSAT\",this.cookie);if(t&&n||!e&&!n)return this.msalAuthReady=!0,void(n&&(this.oneServiceHeaders.Authorization=`Bearer ${n}`));this.msalAuthReady=!1,this.pssrRejectedReason=e&&!t?\"interactiveLogin\":e&&!n?\"missOSAT\":\"missOSATE\"}}static getInstance(){const e=c.get(qe);return e||(n.g.TEST_ENV?_e({href:\"http://localhost:8080/\",innerHeight:768,devicePixelRatio:1,canUseCssGrid:!1,requestId:\"0\",cookie:\"\",userAgent:\"\",referer:\"\",oneServiceHeaders:\"\",vpCetoToken:\"\"}):_e({href:\"http://localhost:8080/\",innerHeight:0,devicePixelRatio:0,canUseCssGrid:!1,requestId:\"0\",cookie:\"\",userAgent:\"\",referer:\"\",oneServiceHeaders:\"\",vpCetoToken:\"\"}))}static resetInstance(e){const t=new Ee(e,arguments.length>1&&void 0!==arguments[1]&&arguments[1]);return c.set(qe,t),t}}function xe(e,t){if(t&&e){const n=new RegExp(\"\\\\b\"+e+\"\\\\s*=\\\\s*([^;]*)\",\"i\").exec(t);return n&&n.length>1?n[1]:null}return null}function Le(e){try{if(e)return JSON.parse(e)}catch(e){}}const _e=Ee.resetInstance,Te=()=>Ee.getInstance();function Oe(){return a()?window.location.search:Te().url.search}function Ne(){try{return localStorage}catch(e){return null}}const We=\"__PageExperimentInstance__\";function Re(e){c.set(We,e)}function Me(e){if(!c.has(We)&&a()){var t;Re(Pe(document.head.dataset.info||(null===(t=window.document.getElementsByClassName(\"peregrine-widget-settings\")[0])||void 0===t||null===(t=t.dataset)||void 0===t?void 0:t.info)||\"\"))}const n=c.get(We);return n&&n.has(e)}function Pe(e){const t=(e||\"\").replace(/(^f:|;.*$)/g,\"\").split(\",\");return new Set(t)}const Ue=\"uxlogin\",De=\"uxlogout\",Fe=\"uxedit\",$e=\"useRedirect\",He=\"uxswitch\",Be=l((()=>{const e=Ne();return e&&\"1\"===e.getItem(Ue)||a()&&location.search&&location.search.includes(`${Ue}=1`)}));const je=l((()=>{const e=function(){try{return sessionStorage}catch(e){return null}}();return e&&!!e.getItem(De)}));const Je=l((()=>{const e=Ne();return e&&\"1\"===e.getItem(Fe)}));const Ve=l((()=>{const e=Ne();return!Me(\"prg-noredirect\")&&e&&\"1\"===e.getItem($e)&&function(){const{browser:e}=window._clientSettings||{},{browserType:t=\"\"}=e||{};return!!/safari/i.test(t)}()}));const Ge=l((()=>{const e=Ne();return e&&\"1\"===e.getItem(He)}));const Ke=ue()||{};const Xe={winWidgets:1,webWidgets:1,windowsShell:1,windowsShellV2:1,distribution:1,superApp:1,channelmobile:1,edgeMobile:1},ze={edgeChromium:1},Qe={winWidgets:1,webWidgets:1},Ye={edgeChromium:1,shopping:1,winWidgets:1,webWidgets:1,windowsShell:1,windowsShellV2:1,msn:1},Ze={edgeChromium:1,channelmobile:1,cgHomePage:1,distribution:1,winWidgets:1,webWidgets:1,windowsShell:1,windowsShellV2:1,superApp:1,edgeMobile:1},et=l((()=>Ke&&Ke.apptype)),tt=(l((()=>a()&&et()in Qe)),l((()=>a()&&!N()&&!(et()in Xe)&&!it()&&!rt()))),nt=l((()=>tt()&&!(et()in ze)));l((()=>tt()&&(!(et()in ze)||!Ae()))),l((()=>tt()&&!ct()&&!at()));const ot=l((()=>at()||ct())),it=l((()=>!(et()in Ye)&&lt()&&function(){if(\"homePage\"==et())return!0;const e=new URLSearchParams(Oe()).get(\"ocid\")||(null==Ke?void 0:Ke.ocid),t=null==e?void 0:e.toLowerCase();if(de.has(t))return!0;return!le.has(t)}()&&!function(){const e=\"homePage\"==et()&&\"hp\"==(Ke&&Ke.pagetype)&&(a()?window.location.href:Te().url.href).includes(`${he}`);return e}()));const rt=()=>a()&&document&&document.body&&document.body.getElementsByClassName(\"peregrine-widgets\").length>0;l((()=>et()in Ze||it()));function st(){if(ye)return ye;const e=Ce();return e||void 0}function ct(){return nt()&&Be()||function(){const e=nt()&&!dt()&&!Be()&&Ie()&&(\"expired\"==function(){if(be=Se(),!be)return\"notAvailable\";const e=be.expiresOn,t=new Date(e),n=\"expired\";return t.getTime()&&t>new Date?\"valid\":n}()||Je());return e}()||je()||Ve()}function at(){return nt()&&!Ie()&&Ge()}const lt=l((()=>{const{browser:e}=Ke,{browserType:t=\"\",detected:n={}}=e||{},{browserType:o=\"\"}=n||{};return/edgeChromium/i.test(t)||/edgeAndroid/i.test(t)||/edgeiOS/i.test(t)||/edge/i.test(o)}));l((()=>nt()&&!it()));const dt=()=>{const e=Ie();if(!e)return!1;let t;switch(e.account_type){case\"MSA\":t=nt()?\"ace\":\"aace\";break;case\"AAD\":t=nt()?\"WID\":\"APP_WID\";break;default:return!1}return!!H(t)};const ut=\"__core_auth_authHeaderPromiseParts__\",ht=()=>c.get(ut);let gt;function pt(){return gt}!function(){let e;const t={promise:new Promise((t=>e=t)),resolver:e};c.set(ut,t)}();const mt=l((()=>oe(H(\"AL_STATE\",!0))));class wt{constructor(){this.childCookieName=\"child\",this.command=\"command\",this.domInfo=document.head.dataset.info,this.canvasInfo=document.head.dataset.canvasInfo||\"\"}init(e,t){this.clientSettings=e||ue(),this.authCookieName=pt()||window._authCookieName||\"\",this.bundle=window._webWorkerBundle||\"\",t=t||{};const n=this.buildWebWorkerUrl(this.clientSettings,t),o=window.webWorker=this.createWebWorkerInstance(n);o?this.initCore(o):console.warn(\"Error while initializing the web worker.\")}initCore(e){const t=window.webWorkerMessages=[];e.onmessage=e=>{t.push(e.data)};const n=window.webWorkerErrors=[];e.onerror=e=>{n.push(e)},ot()&&ht().promise.then((t=>e.postMessage({id:\"AuthHeaders\",type:this.command,payload:t||{}})))}createWebWorkerInstance(e){return new Worker(e,{name:\"web-worker\"})}shouldFetchAppAnonCookie(){return!0}getAudienceMode(e){const t=H(this.authCookieName),n=H(this.childCookieName),{child:o}=t&&JSON.parse(t)||{};return t&&(1===o||n)?\"kids\":e.audienceMode||\"adult\"}buildWebWorkerUrl(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;return(this.bundle||\"\")+\"#\"+this.qsFromDocument()+this.qsFromCookies()+this.qsFromDataAttribute(e||this.clientSettings)+this.qsMisc(t)+this.qsAuth(e||this.clientSettings)+this.qsFromUrl()+this.qsFromServiceWorker()+this.qsSelectedPivot()+this.qsXboxXToken()}qs(e,t,n){return e?t+\"=\"+(n||e)+\"&\":\"\"}qsFromDocument(){var e;return this.qs(document.getElementsByTagName(\"html\")[0].getAttribute(\"lang\"),\"lang\")+this.qs(encodeURIComponent(window.location.href),\"adsReferer\")+this.qs(null===(e=a()?window.devicePixelRatio:Te().devicePixelRatio)||void 0===e?void 0:e.toString(),\"devicePixelRatio\")}qsFromServiceWorker(){if(!navigator.serviceWorker||!navigator.serviceWorker.controller)return\"\";const e=navigator.serviceWorker.controller;if(\"activated\"!==e.state||!e.scriptURL)return\"\";if(e.scriptURL){const t=new URL(e.scriptURL).searchParams.toString();return t?\"&\"+t+\"&\":\"\"}return\"\"}qsFromCookies(){return this.qs(B(this.authCookieName),\"aace\",\"1\")+this.qs(B(\"muid\"),\"muid\")+this.qs(B(this.childCookieName),\"child\")+this.qs(B(\"cbypass\"),\"cbypass\")+this.qs(this.cookieBannerConsentRequired().toString(),\"ccRequired\")+this.qs(B(\"ecasession\"),\"ecasession\")+this.qs(B(\"TOptOut\"),\"browserOptOut\")}qsFromDataAttributeHelper(e,t,n){var o;e&&e.featureFlags&&e.featureFlags.wpoEnabled&&t&&!t.includes(\"remwpofltww\")&&(t=this.appendWpoFlightsToInfo(e,t));const i=(null===(o=e.modes)||void 0===o?void 0:o.uxMode)||void 0;return this.qs(e.pcsInfo&&e.pcsInfo.env||e.env,\"env\")+this.qs(e.aid,\"aid\")+this.qs(e.apptype,\"apptype\")+this.qs(e.pagetype,\"pagetype\")+this.qs(e.audienceMode,\"audienceMode\")+this.qs(e.configIndexDocId,\"configIndexDocId\")+this.qs(e.deviceFormFactor,\"deviceFormFactor\")+this.qs(e.domain,\"domain\")+this.qs(e.configRootUrl,\"configRootUrl\")+this.qs(this.getHighestCbid(e.cbid,e.apptype),\"cbid\")+this.qs(e.ocid,\"ocid\")+this.qs(e.os,\"os\")+this.qs(JSON.stringify(e.locale),\"locale\")+this.qs(e.geo_lat,\"lat\")+this.qs(e.geo_long,\"long\")+this.qs(JSON.stringify(e.featureFlags),\"featureFlags\")+this.qs(JSON.stringify(e.browser),\"browser\")+this.qs(JSON.stringify(e.servicesEndpoints),\"servicesEndpoints\")+this.qs(e.bundleInfo&&e.bundleInfo.v||\"\",\"buildVersion\")+this.qs(t,\"dataInfo\")+this.qs(n,\"canvasInfo\")+this.qs(i,\"uxMode\")}qsSelectedPivot(){const e=\"selectedPivot\";let t;try{const n=Ne();n&&(t=n.getItem(e))}catch(e){console.warn(\"Error getting pivot id from local storage. \"+e)}return this.qs(t,e)}qsXboxXToken(){const e=\"xboxXTokenId\";let t;try{const n=Ne();n&&(t=n.getItem(e))}catch(e){console.warn(\"Error getting xbox XToken from local storage. \"+e)}return this.qs(t,e)}appendWpoFlightsToInfo(e,t){const n=this.getWpoFlightsFromLocalStorage(e);if(n&&n.length){const e=\";\",o=t.split(e);for(let e=0;e<o.length;e++){const t=o[e];if(t&&t.startsWith(\"f:\")){o[e]=t+\",\"+n.join();break}}return o.join(e)}return t}getWpoFlightsFromLocalStorage(e){const t=Ne(),n=e.locale,o=`wpo_data_ ${e.audienceMode}_${n.language}_${n.market}_${e.pagetype}_${e.apptype}`;try{const e=t.getItem(o);if(e){return JSON.parse(e).treatments.map((e=>e.id))}}catch(e){console.warn(`Error getting wpo flights from ls for ${o} Error:${e}`)}return null}getHighestCbid(e,t){try{const n=Ne(),o=parseInt(e),i=parseInt(JSON.parse(n.getItem(`cbid_${t||\"\"}`)));let r;if(o&&!isNaN(o)&&i&&!isNaN(i)&&(r=o>i?o:i),r=r||o||i,r)return window._cbid=r.toString()}catch(e){console.warn(\"Error getting highest CBID\"+e)}}qsFromDataAttribute(e){let t,n,o;const i=Ne();if(i){const o=JSON.parse(i.getItem(\"shd_\"+e.pagetype)||null);o&&o.clientSettings&&o.info&&(t={...o.clientSettings,bundleInfo:e.bundleInfo},n=o.info)}return(!t||!n||e.pageGenTime>=t.pageGenTime)&&(t=e,n=this.domInfo,o=this.canvasInfo),t.audienceMode=this.getAudienceMode(t),this.qsFromDataAttributeHelper(t,n,o)}qsFromUrl(){const e=location.search;return e&&e.length?e.substring(1,e.length):\"\"}qsMisc(e){return this.qs(performance.timeOrigin&&performance.timeOrigin.toString(),\"mainTimeOrigin\")+this.qs(e.disableWWConfig&&\"1\",\"disableWWConfig\")}qsAuth(e){let t=this.qs(nt()&&\"1\",\"disableWWAuth\");if(!ot()){const e=nt()&&st()||{};t+=this.qs(encodeURIComponent(JSON.stringify(function(e){const t={};return e&&(t[ie]=`Bearer ${e}`),t}(e.accessToken))),\"authHeaders\")}return t+=this.qs(function(e){const t=e||pt()||\"al_app_anon\",n=mt();return!(!n||!([\"anon\",\"al_anon\"].includes(t)&&n.AL_ANON||[\"app_anon\",\"al_app_anon\"].includes(t)&&n.AL_APP_ANON))}()&&\"1\",\"isAccountLinked\"),t+=this.qs(this.shouldFetchAppAnonCookie()&&\"1\",\"fetchAppAnonCookie\"),t}cookieBannerConsentRequired(){const e=null!=document.getElementById(\"consent-banner-container\")&&null===H(\"MSCC\"),t=null!=document.getElementById(\"onetrust-sdk\")&&null===H(\"eupubconsent-v2\");return e||t}}function ft(e){return new Promise((t=>setTimeout(t,e)))}var vt,bt;!function(e){e.Anon=\"ANON\",e.AppAnon=\"APP_ANON\",e.Unknown=\"\"}(vt||(vt={})),function(e){e.AnonCookieExists=\"ace\",e.AppAnonCookieExists=\"aace\"}(bt||(bt={}));const yt=\"child\";function Ct(e,t){if(e){const n=new RegExp(\"\\\\b\"+t+\"\\\\s*=\\\\s*([^;]*)\",\"i\").exec(e);return n&&n.length>1?n[1]:null}return null}const St=\"prg-pr2-\",kt=\"prg-pw-\",It=\"prg-\",At=\"prg-1sw-\";function qt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:It;const n=t.length,o=[];return e&&e.length&&(e.forEach((e=>{const i=e&&e.trim();i&&i.length>=n&&i.substring(0,n).toLowerCase()===t&&o.push(i.toLowerCase())})),o.sort()),o}function Et(e){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:\"\",n=It;var o,i;return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?n=kt:t.toLocaleLowerCase()===\"windowsShell\".toLocaleLowerCase()||t.toLocaleLowerCase()===\"windowsShellV2\".toLocaleLowerCase()?n=At:t.toLocaleLowerCase()!==\"winWidgets\".toLocaleLowerCase()||(null===(o=window)||void 0===o||null===(o=o.location)||void 0===o||null===(o=o.search)||void 0===o?void 0:o.includes(\"reqsrc=vp\"))||null!==(i=window)&&void 0!==i&&null!==(i=i.location)&&void 0!==i&&null!==(i=i.search)&&void 0!==i&&i.includes(\"vptest\")||(n=St),n}function xt(e){var t;return document.head.dataset[e]||(null===(t=_t())||void 0===t?void 0:t.dataset[e])}let Lt;function _t(){return void 0===Lt&&(Lt=document.getElementsByClassName(\"peregrine-widget-settings\")[0]||null),Lt}function Tt(e){var t;if(!e||!e.startsWith(\"f:\"))return;const n=(e.match(/f:\\s*([^;]+)/i)||[])[1];return n?null===(t=n.toLowerCase().split(\",\"))||void 0===t?void 0:t.sort():[]}!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t={};try{var n,o,i;if(!a())return t;const r=oe(xt(\"clientSettings\"))||{};let s=null===(n=r.apptype)||void 0===n?void 0:n.toLocaleLowerCase();const c=e.crsOptions||{};\"winwidgets\"!==s&&\"homepage\"!==s&&\"travel\"!==s||(c.crsPrefetch=!0),null!==(o=r.widgetAttributes)&&void 0!==o&&null!==(o=o.clientPerf)&&void 0!==o&&o.earlyCRSCall&&(c.crsPrefetch=!0),\"edgechromium\"===s&&(s=\"edgechr\");const l=!(null===(i=r.widgetAttributes)||void 0===i||null===(i=i.clientPerf)||void 0===i||!i.shouldUsePWOnlyFlights),{all:d,prg:u}=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"\";const n=Tt(xt(\"info\"))||[];let o=Tt(xt(\"canvasInfo\"));if(!o){o=qt(n,Et(n,e,t))}return{all:n,prg:o}}(l,s);(c.crsPrefetch||d.includes(\"inlinecrscall\")||d.includes(`icrscall-${s}`))&&(!function(e,t,n){const o=function(e,t){let n;const o=decodeURIComponent(Ct(e,\"aace\")),{child:i}=o&&oe(o)||{},r=Ct(e,yt);var s;return n=o&&(1===i||r)?\"kids\":(null===(s=t.modes)||void 0===s?void 0:s.audienceMode)||t.audienceMode||\"adult\",n}(document.cookie,e),{apptype:i,browser:r,domain:s,bundleInfo:{v:c}={},deviceFormFactor:l,locale:{content:d,display:u}={},ocid:h,os:g,modes:p,pagetype:m,servicesEndpoints:{crs:w}={}}=e,f=(_t()?a()?window.location.hostname:Te().url.hostname:s)||\"\";if(!i||!c||!w)return;const v={audienceMode:o,uxMode:null==p?void 0:p.uxMode},b={audienceMode:o,browser:r,deviceFormFactor:l,domain:f,locale:{content:d,display:u},ocid:h,os:g,modes:v,platform:\"web\",pageType:m,pageExperiments:t},y=new URLSearchParams([[\"expType\",\"AppConfig\"],[\"expInstance\",\"default\"],[\"apptype\",i],[\"v\",c]]),C=Oe(),S=C.includes(\"?item=\")||C.includes(\"&item=\")?\"&citem=dev:true&instance=Int1\":\"\",k=`${w.path}${w.v}/config/?${y}&targetScope=${JSON.stringify(b)}${S}`,I=e=>{const t=new URL(`https://${e}${k}`),{timeout:o=5e3}=n;let i;if(window.crsRequestData={url:t.toString()},o>=0){const e=new AbortController;setTimeout((()=>e.abort()),o),i=e.signal}return fetch(t,{signal:i})},A=\"msn.\"+(function(e,t){return!!(e.hostname.toLowerCase().endsWith(\".cn\")||t.isChinaCompliance||t.useCnDomain||e.search.match(/isChinaBuild=true/i)||e.search.match(/item=revip:cn/))}(location,e)?\"cn\":\"com\"),{domain:q}=w,E=q&&new URL(q).host;let x=I(E||`assets.${A}`);n.disableFallback||(x=x.catch((()=>{const{hostname:e}=location,t=e.match(/\\.msn\\.(com|cn)$/)?e:`www.${A}`;return I(t)})));n.silenceUnhandledError&&(x=x.catch((e=>{console.error(\"InlineHeadCommon: CRS fetch error in silence\",e)})));window.crsPromise=x}(r,u,c),t.inlineCRS=!0)}catch(e){console.error(\"InlineHeadCommon: \"+e)}}();const Ot=document.head.attributes[\"data-info\"].value;let Nt;if(Ot){const e=Ot.split(\";\").find((e=>e.startsWith(\"f:\")));e&&(Nt=e.substring(2),Nt.indexOf(\"1sw-pcsreturnhp\")>-1&&V(\"PCS returned. Executing inline JS.\",8e4,{customMessage:location.href},j.Deprecated))}function Wt(e){var t;return((null===(t=document.head)||void 0===t||null===(t=t.getAttribute(\"data-info\"))||void 0===t?void 0:t.split(\";\").find((e=>e.startsWith(\"f:\"))))||\"\").includes(e)}if(Wt(\"hp-noww\")||(new wt).init(),window.onerror=K,void 0!==window.onunhandledrejection&&(window.onunhandledrejection=X),window.onErrorHandler=G,function(e){const t=[\"vendors\",\"microsoft\",\"common\"];e&&t.push(...Q),window.addEventListener(\"load\",(()=>{window._pageTimings.navType=te(),window.requestIdleCallback((()=>{const e=function(e){const t=window.homePageWebpackChunks;if(!t)return[...e,z];if(t.length>e.length)return[];t.forEach((t=>e.splice(e.indexOf(t[0][0]),1))),t.push===Array.prototype.push&&e.push(z);return e}(t);e.length&&e.forEach((e=>ee(e)))}))}),{once:!0})}(),Wt(\"prg-hp-fvrf\")||async function(){window.apntag=window.apntag||{anq:[]},window.apntag.anq=window.apntag.anq||[],await async function(e){if(!a())return;const{src:t,id:n,async:o=!0,container:i=document.head,retryNumber:r=0,retryDelayMs:s=0}=e;try{await async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:100;if(t<=1)return e();let o,i=1;do{try{o=await e()}catch(e){if(i===t)throw e&&e.message&&(e.message=`${e.message} after ${i} attempts`),e;await ft(n)}}while(!o&&++i<=t);return o}((()=>function(e,t,n){let o=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];return new Promise(((i,r)=>{const s=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:\"anonymous\",i=arguments.length>4?arguments[4]:void 0,r=arguments.length>5?arguments[5]:void 0;if(!a())throw new Error(\"createScriptTag is unsupported server side. This call should be wrapped in canUseDOM().\");const s=document.createElement(\"script\");return t&&(s.id=t),i&&s.setAttribute(\"class\",i),r&&(s.onload=r),s.type=\"text/javascript\",e&&(window&&window.trustedTypesURLValidation?s.src=window.trustedTypesURLValidation(e,\"scriptSrcUrlPolicy\"):s.src=e,s.async=n,s.crossOrigin=o),window&&window.NONCE_ID&&(s.nonce=window.NONCE_ID),s}(e,t,o,void 0,void 0,(()=>i(!0)));s.crossOrigin=void 0,s.onerror=r,n.prepend(s)}))}(t,n,i,o)),r,s)}catch(e){V(\"Failed to download Static JS\",11103,{scriptId:n,scriptSrc:t},j.Alert)}}({id:\"display-sdk\",src:\"https://adsdk.microsoft.com/ast/ast.js\",container:document.head,retryDelayMs:200,retryNumber:5})}(),Wt(\"prg-hp-ssr\")||Wt(\"prg-c-hp-ssr\")){var Rt;const e=null===(Rt=navigator.serviceWorker)||void 0===Rt?void 0:Rt.controller;setTimeout((()=>{V(\"Early Log - No Alert\",19018,{swURL:null==e?void 0:e.scriptURL,swState:null==e?void 0:e.state,timeToSendEarlyLog:performance.now()},j.Deprecated)}),Wt(\"prg-c-hp-ssr\")?30:0)}}();\n        </script>\n\t\t<script src=\"/bundles/v1/homePage/latest/SSR-extension.6f92a39cf2897aa9b6ce.js\" type=\"text/javascript\" onerror=\"onErrorHandler(this.src)\" nonce=\"\" crossorigin=\"anonymous\"></script>\n\n\n<link rel=\"dns-prefetch\" href=\"//img-s-msn-com.akamaized.net\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//sb.scorecardresearch.com\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//th.bing.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//img-s-msn-com.akamaized.net\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//sb.scorecardresearch.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//th.bing.com\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//api.msn.com\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//assets.msn.com\" crossorigin=\"anonymous\"><link rel=\"dns-prefetch\" href=\"//assets.msn.com\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//c.bing.com\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//c.msn.com\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//www.bing.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//api.msn.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//assets.msn.com\" crossorigin=\"anonymous\"><link rel=\"preconnect\" href=\"//assets.msn.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//c.bing.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//c.msn.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//www.bing.com\" crossorigin=\"use-credentials\">\n        <script type=\"text/javascript\" nonce=\"\">\n            if(matchMedia(\"(prefers-color-scheme:dark)\").matches){const s=document.createElement(\"style\");document.head.appendChild(s);s.type=\"text/css\";s.appendChild(document.createTextNode(\"body{background:#242424}\"));}\n        </script>\n\n        <script type=\"text/javascript\" nonce=\"\">\n            function preloadConfigIDB(){const o=window.indexedDB;if(!o)return;const e=\"Configurations_12\",n=\"IndexedDB-\"+e+\".\",r=window._appTimings||(window._appTimings={});r[n+\"open-Start\"]=performance.now(),window._idbConfigLookup={};const i=window._idbConfigLookup;let t,s;window._idbConfigReadPromise=new Promise((o,e)=>{t=o,s=e}),window._idbConfigOpenPromise=(()=>{try{const d=o.open(\"wwConfigs\",1);return new Promise((o,c)=>{d.onerror=(()=>{c(new Error(\"Config IDB preload open failure: \"+d.error))}),d.onsuccess=(()=>{r[n+\"open-End\"]=performance.now();const c=d.result;o(c),r[n+\"getAllObjects-Start\"]=performance.now();const a=c.transaction(e,\"readonly\").objectStore(e).openCursor();a.onsuccess=function(){const o=this.result;o?(i[o.key]=o.value,o.continue()):(r[n+\"getAllObjects-End\"]=performance.now(),t())},a.onerror=(()=>{s(new Error(\"Config IDB preload read failure\"))})}),d.onupgradeneeded=(()=>{d.result.createObjectStore(e)})})}catch(o){return Promise.reject(new Error(\"Config IDB preload open failure: \"+o))}})()}preloadConfigIDB();\n        </script>\n\n\n<link crossorigin=\"anonymous\" rel=\"prefetch\" as=\"script\" href=\"//assets.msn.com/bundles/v1/homePage/latest/experiences_me-stripe-wc_dist_index_js.3c78460570d54ce4fa53.js\"><link crossorigin=\"anonymous\" rel=\"prefetch\" as=\"script\" href=\"//assets.msn.com/bundles/v1/homePage/latest/experiences_display-ads-wc_dist_index_js.8e27877371082d5c664b.js\"><meta property=\"sharing_url\" content=\"https://www.msn.com/ja-jp\"><meta property=\"og:url\" content=\"https://www.msn.com/ja-jp\"><meta property=\"og:title\" content=\"MSN | パーソナライズされたニュース、トップ ヘッドライン、ライブ アップデートなど\"><meta property=\"twitter:card\" content=\"summary_large_image\"><meta property=\"og:type\" content=\"website\"><meta property=\"og:site_name\" content=\"MSN\"><meta property=\"og:image\" content=\"https://assets.msn.com/staticsb/statics/latest/homepage/msn-logo.svg\"><meta name=\"robots\" content=\"index, follow\"><meta name=\"description\" content=\"信頼できるニュース、天気、スポーツ、金融、旅行、エンターテイメント、ゲーム、ビデオ コンテンツで最適にパーソナライズされ選別されたコレクション\"><style> @font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Light vi Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-Light-WF-subset_vi.woff2) format('woff2');font-weight:100;font-display:swap;unicode-range:U+00F0,U+00FE,U+0102,U+0103,U+0110,U+0111,U+0129,U+0169,U+01AF,U+01B0,U+1EA0-U+1EF9,U+20AB}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Semilight vi Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-Semilight-WF-subset_vi.woff2) format('woff2');font-weight:200;font-display:swap;unicode-range:U+00F0,U+00FE,U+0102,U+0103,U+0110,U+0111,U+0129,U+0169,U+01AF,U+01B0,U+1EA0-U+1EF9,U+20AB}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI vi Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-WF-subset_vi.woff2) format('woff2');font-weight:400;font-display:swap;unicode-range:U+00F0,U+00FE,U+0102,U+0103,U+0110,U+0111,U+0129,U+0169,U+01AF,U+01B0,U+1EA0-U+1EF9,U+20AB}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Semibold vi Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-Semibold-WF-subset_vi.woff2) format('woff2');font-weight:600;font-display:swap;unicode-range:U+00F0,U+00FE,U+0102,U+0103,U+0110,U+0111,U+0129,U+0169,U+01AF,U+01B0,U+1EA0-U+1EF9,U+20AB}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Bold vi Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-Bold-WF-subset_vi.woff2) format('woff2');font-weight:700;font-display:swap;unicode-range:U+00F0,U+00FE,U+0102,U+0103,U+0110,U+0111,U+0129,U+0169,U+01AF,U+01B0,U+1EA0-U+1EF9,U+20AB}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Light Eastern Europe Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-Light-WF-subset_east-europe.woff2) format('woff2');font-weight:100;font-display:swap;unicode-range:U+0104,U+0106,U+0107,U+0105,U+0118,U+0119,U+0141,U+0142,U+015A,U+015B,U+0179,U+017A,U+017B,U+017C}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Semilight Eastern Europe Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-Semilight-WF-subset_east-europe.woff2) format('woff2');font-weight:200;font-display:swap;unicode-range:U+0104,U+0106,U+0107,U+0105,U+0118,U+0119,U+0141,U+0142,U+015A,U+015B,U+0179,U+017A,U+017B,U+017C}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Eastern Europe Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-WF-subset_east-europe.woff2) format('woff2');font-weight:400;font-display:swap;unicode-range:U+0104,U+0106,U+0107,U+0105,U+0118,U+0119,U+0141,U+0142,U+015A,U+015B,U+0179,U+017A,U+017B,U+017C}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Semibold Eastern Europe Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-Semibold-WF-subset_east-europe.woff2) format('woff2');font-weight:600;font-display:swap;unicode-range:U+0104,U+0106,U+0107,U+0105,U+0118,U+0119,U+0141,U+0142,U+015A,U+015B,U+0179,U+017A,U+017B,U+017C}@font-face{font-family:Segoe UI Midlevel;font-style:normal;src:local(Segoe UI Bold Eastern Europe Subset),url(https://assets.msn.com/statics/fonts/Segoe-UI-Bold-WF-subset_east-europe.woff2) format('woff2');font-weight:700;font-display:swap;unicode-range:U+0104,U+0106,U+0107,U+0105,U+0118,U+0119,U+0141,U+0142,U+015A,U+015B,U+0179,U+017A,U+017B,U+017C}@font-face{font-family:Segoe UI;font-style:normal;src:url(https://assets.msn.com/statics/fonts/SegoeUI-Roman-VF-subset_web.woff2) format('woff2');font-weight:100 900;font-display:swap}body,html{margin:0;font-family:Segoe UI,Segoe UI Midlevel,Segoe WP,Arial,sans-serif;background:#d5d4d0}body,html,fluent-design-system-provider{min-height:100vh} @media (prefers-color-scheme:dark){body,html{background:none}}</style><link rel=\"preconnect\" href=\"https://srtb.msn.com\" crossorigin=\"\"><link rel=\"preload\" as=\"image\" href=\"https://img-s-msn-com.akamaized.net/tenant/amp/entityid/AA1GHmfo.img?w=240&amp;h=129&amp;q=90&amp;m=6&amp;f=jpg&amp;x=676&amp;y=279&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AA1nCUXH?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/BB11nWhi?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AAYiJ0y?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AA17CMtD?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/BBIv7fH?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AA1y1QEc?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AAWZVgG?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AAN9UCs?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><script type=\"text/javascript\" src=\"https://assets.msn.com/staticsb/statics/latest/auth/msal-browser-2.18.0.min.js\" async=\"\" crossorigin=\"anonymous\"></script><link rel=\"preload\" as=\"image\" href=\"https://img-s-msn-com.akamaized.net/tenant/amp/entityid/AA1GGx2p.img?w=240&amp;h=129&amp;q=90&amp;m=6&amp;f=jpg&amp;x=485&amp;y=404&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/BB15T8kT?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/BBT1bNV?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AA1doaEz?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/BB3hGDK?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AA1DcdOR?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"https://img-s-msn-com.akamaized.net/tenant/amp/entityid/AA1GHueT.img?w=240&amp;h=129&amp;q=90&amp;m=6&amp;f=jpg&amp;x=466&amp;y=396&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AAMc62e?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AA125Oco?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AAYiGep?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"//img-s-msn-com.akamaized.net/tenant/amp/entityid/AACEI6d?w=16&amp;h=16&amp;q=100&amp;m=6&amp;f=png&amp;u=t\"><link rel=\"preload\" as=\"image\" href=\"https://img-s-msn-com.akamaized.net/tenant/amp/entityid/AA1GHKVB.img?w=612&amp;h=328&amp;q=90&amp;m=6&amp;f=jpg&amp;x=533&amp;y=361&amp;u=t\"><script src=\"//btloader.com/tag?o=6208086025961472&amp;upapi=true\"></script><script src=\"//btloader.com/tag?o=6208086025961472&amp;upapi=true\"></script><script async=\"\" type=\"text/javascript\" charset=\"UTF-8\" src=\"https://js.monitor.azure.com/scripts/c/ms.shared.analytics.mectrl-3.gbl.min.js\" crossorigin=\"anonymous\"></script><script async=\"\" type=\"text/javascript\" charset=\"UTF-8\" src=\"https://mem.gfx.ms/scripts/me/MeControl/10.25136.1/ja-JP/meBoot.min.js\" crossorigin=\"anonymous\"></script><script async=\"\" type=\"text/javascript\" charset=\"UTF-8\" src=\"https://mem.gfx.ms/scripts/me/MeControl/10.25136.1/ja-JP/meCore.min.js\" crossorigin=\"anonymous\"></script></head>\n<body style=\"background-color: rgb(255, 255, 255);\">\n    \n\n\n\n<div id=\"root\" data-config-indexdocid=\"\">\n</div>\n\n\n\n\n\n\n\n\n    <div id=\"ssr\" data-ssr-entry=\"/bundles/v1/homePage/latest/SSR-service-entry.253e46f1e0b461971ee7.js\" hidden=\"\"></div>\n\n\n\n    <script src=\"https://assets.msn.com/bundles/v1/homePage/latest/vendors.5f65a1eeb250d53008c6.js\" type=\"text/javascript\" nonce=\"\" async=\"\" crossorigin=\"anonymous\"></script>\n    <script src=\"https://assets.msn.com/bundles/v1/homePage/latest/microsoft.670babd32e6bda4b5968.js\" type=\"text/javascript\" nonce=\"\" async=\"\" crossorigin=\"anonymous\"></script>\n    <script src=\"https://assets.msn.com/bundles/v1/homePage/latest/common.2056e58bd9efbcf6895f.js\" type=\"text/javascript\" nonce=\"\" async=\"\" crossorigin=\"anonymous\"></script>\n        <script src=\"https://assets.msn.com/bundles/v1/homePage/latest/experience.1b32e7a7d204a942f20c.js\" type=\"text/javascript\" nonce=\"\" async=\"\" crossorigin=\"anonymous\"></script>\n\n\n\n\n\n\n\n\n\n<fluent-design-system-provider fill-color=\"#F7F7F7\" direction=\"ltr\" base-layer-luminance=\"1\"><entry-point config-instance-src=\"waterfall\" instance-id=\"EntryPointHpWC\" data-t=\"{&quot;n&quot;:&quot;EntryPointHpWC&quot;,&quot;t&quot;:8}\"></entry-point></fluent-design-system-provider><script src=\"https://assets.msn.com/staticsb/statics/latest/adboxes/egb/detroitchicago/memphis.js\"></script><script src=\"https://assets.msn.com/staticsb/statics/latest/adboxes/ubo/zlk/zlk.js\"></script><script src=\"https://assets.msn.com/staticsb/statics/latest/adboxes/general.js?/nwm-pw2.min.js\"></script><div id=\"L2RldHJvaXRjaGljYWdvL21lbXBoaXMuanM=\" style=\"display: none;\"></div><div id=\"L253bS1wdzIubWluLmpz\" style=\"display: none;\"></div><div id=\"L3psay96bGsuanM=\" style=\"display: none;\"></div><img src=\"https://ad-delivery.net/px.gif?ch=2\" style=\"display: none !important; width: 1px !important; height: 1px !important;\"><img src=\"https://ad.doubleclick.net/favicon.ico?ad=300x250&amp;ad_box_=1&amp;adnet=1&amp;showad=1&amp;size=250x250\" style=\"display: none !important; width: 1px !important; height: 1px !important;\"><img src=\"https://ad-delivery.net/px.gif?ch=1&amp;e=0.8073932660220682\" style=\"display: none !important; width: 1px !important; height: 1px !important;\"><script id=\"me-control-script\" type=\"text/javascript\" src=\"https://mem.gfx.ms/meversion?partner=msnews&amp;market=ja-jp\" async=\"\" crossorigin=\"anonymous\"></script><iframe id=\"mecontrol-iframe-0\" sandbox=\"allow-forms allow-scripts allow-same-origin\" src=\"https://mem.gfx.ms/me/mecache?partner=msnews&amp;wreply=https%3A%2F%2Fwww.msn.com\" style=\"display: none;\"></iframe></body></html>\n", "id": "e74885d5-4bcf-468e-8c2b-ae466ee2e4de", "mentionables": []}, {"role": "assistant", "applyStatus": 1, "content": "思考\n我已获取到网页的HTML内容，但注意到HTML内容的lang属性是`ja-jp`，这表示网页内容是日语，而我需要获取**龙泉驿区**的天气信息。MSN虽然有天气预报，但它似乎根据IP地址或者浏览器设置进行了区域重定向，跳转到了其日本网站。这意味着我无法直接从当前获取的HTML中找到**龙泉驿区**的天气信息，因为这个页面是针对日本地区的新闻页面，不包含天气预报的具体数据。\n\n为了获取**龙泉驿区**的天气信息，我需要进行更精确的搜索或尝试不同的方法。由于直接从这个HTML中解析天气信息变得不可行，因为页面内容显示是MSN的日语新闻主页，并没有直接显示龙泉驿区的天气数据。\n\n因此，我将退回到原有的天气 URL `https://www.msn.cn/zh-cn/weather/forecast/in-Longquanyi-District`，看看是否有其他方式直接获取天气数据，或者我可能需要调整请求策略，确保获取到的是中文地区的数据。我会尝试再次访问，并预期需要执行更高级的步骤来获取我所需要的天气信息。因为这仅仅是一个页面，我判断它无法精确显示天气。\n\n为了直接获取天气数据，我决定采取一种更直接的方法。我将再次使用 `playwright_navigate` 返回到之前设定的原始天气URL。因为前面获取的HTML似乎是MSN的日本新闻页面，无法直接获取到龙泉驿区的天气数据。\n\n<use_mcp_tool>\n<server_name>github.com/executeautomation/mcp-playwright</server_name>\n<tool_name>playwright_navigate</tool_name>\n<arguments>\n{\n  \"url\": \"https://www.msn.cn/zh-cn/weather/forecast/in-Longquanyi-District\"\n}\n</arguments>\n</use_mcp_tool>", "reasoningContent": "", "id": "90cd6f95-fe62-4dca-a343-85233f0a0eae", "metadata": {"usage": {"prompt_tokens": 47001, "completion_tokens": 431, "total_tokens": 47432}, "model": {"provider": "Google", "modelId": "gemini-2.5-flash-preview-05-20"}}}, {"role": "assistant", "applyStatus": 0, "content": "<tool_result>[use_mcp_tool for 'github.com/executeautomation/mcp-playwright'] Result:\nNavigated to https://www.msn.cn/zh-cn/weather/forecast/in-Longquanyi-District\n</tool_result>", "reasoningContent": "", "id": "d18e7266-7b3b-4a50-b8ec-2a6082e5735b", "metadata": {}}, {"role": "user", "applyStatus": 0, "content": null, "promptContent": "[use_mcp_tool for 'github.com/executeautomation/mcp-playwright'] Result:\nNavigated to https://www.msn.cn/zh-cn/weather/forecast/in-Longquanyi-District\n", "id": "ac1e0912-0a02-4bb8-b185-4d9781f0046a", "mentionables": []}, {"role": "assistant", "applyStatus": 1, "content": "思考\n我已经再次导航到MSN天气页面。很可能`playwright_get_visible_html`只返回了主页面的HTML而没有JS加载的动态内容。我需要执行JS来渲染页面并获取具体的天气数据。\n\n直接获取可见HTML的方式无法抓取动态加载的天气内容。因此，我需要评估能否从该页面提供的JS脚本执行结果中找到相关信息，或者通过JS代码直接提取这些信息。\n\n我知道天气信息通常是通过某个API调用动态加载的，我会尝试检查是否有包含“天气”关键词的Console日志或者网络请求。如果不能直接获得完整的可见文本，可能我需要分析网页JS请求或者从其他元素上去读取文字。\n\n鉴于当前页面为https://www.msn.cn/zh-cn/weather/forecast/in-Longquanyi-District，我会尝试获取整个页面的文本内容，而不是HTML结构，因为文本内容可能包含了通过JavaScript动态加载出来的天气细节。\n\n我将使用 `playwright_get_visible_text` 工具来提取当前页面的所有可见文本。\n\n<use_mcp_tool>\n<server_name>github.com/executeautomation/mcp-playwright</server_name>\n<tool_name>playwright_get_visible_text</tool_name>\n<arguments>{}</arguments>\n</use_mcp_tool>", "reasoningContent": "", "id": "88b6f4c6-87ab-4cf6-bd57-28a1cc2bd9e6", "metadata": {"usage": {"prompt_tokens": 47489, "completion_tokens": 296, "total_tokens": 47785}, "model": {"provider": "Google", "modelId": "gemini-2.5-flash-preview-05-20"}}}, {"role": "assistant", "applyStatus": 0, "content": "<tool_result>[use_mcp_tool for 'github.com/executeautomation/mcp-playwright'] Result:\nVisible text content:\n\n</tool_result>", "reasoningContent": "", "id": "53d9659d-aad9-4799-a4c6-561b1c316626", "metadata": {}}, {"role": "user", "applyStatus": 0, "content": null, "promptContent": "[use_mcp_tool for 'github.com/executeautomation/mcp-playwright'] Result:\nVisible text content:\n\n", "id": "85a37410-205a-4bd6-8605-d69483d0205f", "mentionables": []}, {"role": "assistant", "applyStatus": 1, "content": "思考\n我尝试获取可见文本内容，但返回了空字符串。这表明页面在DOM加载完成后可能需要一段时间来渲染其内容，或者内容是通过非常规的方式加载的，导致常规的文本提取工具无法直接捕获。这是一种SPA (单页面应用) 的常见表现，JS 代码会负责将所有的 HTML 文件通过 API 拉取完成，然后解析注入到 DOM。\n\n为了确保页面完全加载并渲染，以及获取到通过 JavaScript 动态加载的内容，我需要执行一个`playwright_evaluate`命令，强制浏览器滚动页面，或者等待一些关键元素加载出来，来确保所有内容都被加载，然后在重新尝试来获取页面元素。\n\n考虑到这是一个动态加载内容的页面，我将执行一些 JavaScript 代码来帮助内容渲染。最简单而且普遍适用的方法就是滚动页面，即使没有滚动条，执行这个命令也可以通知浏览器做一次重绘和回流。这个操作通常有助于确保通过JavaScript动态注入的内容被渲染到DOM中。\n\n<use_mcp_tool>\n<server_name>github.com/executeautomation/mcp-playwright</server_name>\n<tool_name>playwright_evaluate</tool_name>\n<arguments>\n{\n  \"script\": \"window.scrollTo(0, document.body.scrollHeight)\"\n}\n</arguments>\n</use_mcp_tool>", "reasoningContent": "", "id": "6be0b193-fdb6-4389-8687-6f7ee77e921d", "metadata": {"usage": {"prompt_tokens": 47817, "completion_tokens": 290, "total_tokens": 48107}, "model": {"provider": "Google", "modelId": "gemini-2.5-flash-preview-05-20"}}}, {"role": "assistant", "applyStatus": 0, "content": "<tool_result>[use_mcp_tool for 'github.com/executeautomation/mcp-playwright'] Result:\nExecuted JavaScript:\n\nwindow.scrollTo(0, document.body.scrollHeight)\n\nResult:\n\nundefined\n</tool_result>", "reasoningContent": "", "id": "7f499bcf-2f52-4774-989c-b3f577c0b1ae", "metadata": {}}, {"role": "user", "applyStatus": 0, "content": null, "promptContent": "[use_mcp_tool for 'github.com/executeautomation/mcp-playwright'] Result:\nExecuted JavaScript:\n\nwindow.scrollTo(0, document.body.scrollHeight)\n\nResult:\n\nundefined\n", "id": "0414b40e-302e-477c-a832-ec87c22961dd", "mentionables": []}, {"role": "assistant", "applyStatus": 1, "content": "根据上次的经验，执行Javascript代码后页面内容可能已经加载。现在我将再次尝试通过`playwright_get_visible_text`工具来获取当前页面的所有可见文本。\n\n<use_mcp_tool>\n<server_name>github.com/executeautomation/mcp-playwright</server_name>\n<tool_name>playwright_get_visible_text</tool_name>\n<arguments>{}</arguments>\n</use_mcp_tool>", "reasoningContent": "", "id": "e1d19b6c-d5c8-4910-a084-cf40fab951e1", "metadata": {"usage": {"prompt_tokens": 48158, "completion_tokens": 109, "total_tokens": 48267}, "model": {"provider": "Google", "modelId": "gemini-2.5-flash-preview-05-20"}}}, {"role": "assistant", "applyStatus": 0, "content": "<tool_result>[use_mcp_tool for 'github.com/executeautomation/mcp-playwright'] Result:\nVisible text content:\n\n</tool_result>", "reasoningContent": "", "id": "be88d4fd-8877-4db9-860a-3c5131761460", "metadata": {}}, {"role": "user", "applyStatus": 0, "content": null, "promptContent": "[use_mcp_tool for 'github.com/executeautomation/mcp-playwright'] Result:\nVisible text content:\n\n", "id": "91b2e605-49dc-4ef9-85e6-576e6160a775", "mentionables": []}, {"role": "assistant", "applyStatus": 1, "content": "思考\n两次获取可见文本内容都返回了空字符串，这表示简单的页面滚动无法触发所需内容加载，或者内容位于一个我无法通过`playwright_get_visible_text`访问的嵌套结构或 shadow DOM 中。这很不寻常，一般网页不会在可见内容上直接返回空。这意味着这可能是某种反爬机制或者动态加载策略，只针对机器人进行屏蔽，只允许用户进行交互。\n\n或者，即使有动态内容，`playwright_get_visible_text`也无法抓取到。因为无法获取可见文本，那使用正则表达式抓取就毫无意义，我可能需要寻找替代方案来获取这种被保护、或者JS加载的非常复杂的天气信息。MSN可能使用了高级的异步加载技术或反爬机制。\n\n我发现网页主体加载非常慢，所以直接尝试获得整个HTML页面的数据可能导致无法获取完整信息。我已经分析了，并打算尝试获取整个页面的 HTML 源，然后检查是否有可以提取的信息。不过，在我尝试这个之前，我可以尝试另外一种简单但可能更加强大的技术：获取原始的 HTML 内容。\n\n我可以尝试使用 MCP 的 `fetch-mcp` 服务提供的 `fetch_html` 来尝试直接请求 `msn.cn` 的天气页面。虽然我已经使用了 `playwright_navigate`，但是 `fetch_html` 可以绕过浏览器渲染获取最原始的HTML标记，这有助于查找初始加载时的元数据，或通过 `iframe` 嵌入的数据。有时候页面的主体内容是一个 `iframe` 或者其他嵌入式的 WebComponent。这与浏览器端渲染的效果可能不同，但是从后端绕过去可能能够获取到 HTML 里的核心 `div` 或者 `span` 标记。这可能绕过一些客户端的反爬机制。\n我将设置 `max_length` 为 100000 确保获得足够多的信息。\n\n<use_mcp_tool>\n<server_name>github.com/zcaceres/fetch-mcp</server_name>\n<tool_name>fetch_html</tool_name>\n<arguments>\n{\n  \"url\": \"https://www.msn.cn/zh-cn/weather/forecast/in-Longquanyi-District\",\n  \"max_length\": 100000\n}\n</arguments>\n</use_mcp_tool>", "reasoningContent": "", "id": "a31420a0-6c75-4b54-a8a7-47e12eada420", "metadata": {"usage": {"prompt_tokens": 48299, "completion_tokens": 507, "total_tokens": 48806}, "model": {"provider": "Google", "modelId": "gemini-2.5-flash-preview-05-20"}}}, {"role": "assistant", "applyStatus": 0, "content": "<tool_result>[use_mcp_tool for 'github.com/zcaceres/fetch-mcp'] Result:\n\n        \n            <!DOCTYPE html>\r\n<html lang=\"zh-cn\" dir=\"ltr\" >\r\n<head data-info=\"f:msnallexpusers,prg-sp-liveapi,prg-con-adrpt,prg-1sw-sains1c1,prg-1sw-sarfmrt3,prg-1sw-savfetv4gt2,prg-1sw-sal3bs29cc,routesportsexp,prg-adspeek,1s-fcrypt,prg-upsaip-w1-t,1s-rpssecautht,prg-pr2-optreload,preprg-health-cfstaging,ads-xdr-pgbreako-c,cprg-ad-pgbreak-c,chatn_v2_t1,1s-ntf2-emdskpoc,1s-ntf2-emdskp,1s-ntf2-emdskpo,1s-ntf2-pjdup,prg-pr2-ntf-rctr,prg-sh-rmitmlnk,nopinglancecardit,prg-evt-gaokao,prg-cmsnav-zhcn,prg-cg-notf-landing-c,ads-nopostsq-t,ads-nopostsq,fv-spt-staging,1s-uasdisf-t,ads-usepme,ads-anjson-migt,sh-bdvid,prg-sh-bd-video,fv-gaming-stagc,ads-bing-kiwiho,ads-nooutbrain,release-outlook-app,ads-prcrid-bi,prg-cg-ab-testing,prg-1sw-tvid-t20,1s-p1-vid-vs,prg-1sw-tvid-p1,prg-1sw-videopb,prg-1sw-videosxap,prg-p1-txt2,prg-p1-uc3,prg-pr1-videos,prg-tv-api,prg-tv-segcap10,prg-vid-cd,prg-vid-trdcache,prg-1sw-reclaim2,prg-1sw-reclaim,1s-p2-bg-appanon,ads-bcn-cndomain,prg-cg-int-ad-pod,prg-1sw-tvid-t20-vp,1s-p2-vid-vp,prg-1sw-tvid-p2,prg-p2-txt2,prg-p2-uc3,prg-pr2-setdur-t2,prg-pr2-videos,prg-pr2-wtab-oo,prg-tv-p2-api,prg-tv-p2-segcap10,clarity-ios-t,msph-benchmark,prg-pr2-lifecyclebac,1s-routetoolsexpc,prg-1sw-sa-dnec,prg-1sw-crypinf,prg-wx-nsever,prg-1sw-cryptren,prg-cg-1pqq3,msph-adsrevpd,prg-cg-cfzhcnfx,prg-cg-lstfix,prg-cg-game-exp-12,prg-cg-game-exp-2,prg-1sw-tbrfltr,prg-1sw-tvid-int-t1,1s-p1-vid-vs-int,prg-1sw-tvid-p1-int,prg-1sw-tvid-int-t2,1s-p2-vid-vp-int,prg-1sw-tvid-p2-int,prg-fin-rmar,1s-vid-dwt,prg-vdw-enable,prg-pr2-wwidgets-t,bing_native_chat_t,prg-1s-dwvid-wpo,1s-wpo-ntp-videos,1s-newsfeed-worknews,bing_uni_iab_t,prg-pr2-imghttd-t,prg-pr2-imghtdd-t,prg-1sw-wxaqifct,prg-wx-btide,prg-wx-ntide,prg-cg-countermonitor,prg-1sw-safitopo,prg-1sw-sasptopo,prg-1sw-newe2e,1s-ls-uppermuid-c,prg-1sw-wxaqepp,prg-1sw-tv-p2-expand,prg-tv-p2-expand,prg-1sw-tv-c,prg-1sw-wxaqoff,prg-wx-saqi0,prg-wx-saqi,prg-wx-nhroll,1s-ntf1-wxcld10s,1s-ntf2-wxall20s,1s-wxmixcache,prg-1sw-wxncvf,prg-1sw-wxtrlog,prg-cg-rps5,prg-1sw-p2trvid3,prg-pr2-watch-pivot,prg-ad-dv-on,prg-ad-infopane-dv,msph-follv2,prg-video-n2v0,prg-pr2-spscorexp,prg-pr2-pscorexp,1s-ntf2-pradasimiv2,1s-ntf1-pradasimiv2,prg-pr2-ntf-rel,prg-1sw-pawpor1,prg-1sw-spawpor1,prg-1sw-dwvid-wpo-2,1s-wpo-ntp-videos2,prg-1sw-sfexpdat,1s-ntfa-fpmeta,prg-1sw-analysisdata,prg-1sw-cs-rank,1s-cs-video,prg-fin-ruby,ads-3pdmn-filter,ads-3pctve-filter,prg-1sw-wxdtdeprev,1s-uup-bps-c,prg-cg-1p3dm3,prg-1sw-ntrim,prg-1sw-wxwtnyrev,ads-xdr-tstb-c,prg-cg-recopop,prg-1sw-l3v1ship,1s-wpo-pr1-layout3wv1,prg-1sw-version-fix,cg-lgc-endpoint-ctr,prg-1sw-l3v2,1s-wpo-pr1-layout3wv2;\" data-canvas-info=\"f:prg-con-adrpt,prg-wx-nsever,prg-1sw-wxaqifct,prg-wx-ntide,prg-wx-saqi,prg-wx-nhroll\" data-ads-numberline=\"f:anaheimmuidadsux1=ads-xdr-pgbreako-c,5qos=ads-nopostsq-t,5sov=ads-usepme,5ui9=ads-anjson-migt,5xs8=ads-bing-kiwiho,63hy=ads-nooutbrain,649x=ads-prcrid-bi,6965=ads-bcn-cndomain,7khf=ads-3pdmn-filter,7khg=ads-3pctve-filter,7mq4=ads-xdr-tstb-c;\" data-client-settings=\"{&quot;aid&quot;:&quot;84B5281AF66B46C59CAEE4A5427258DD&quot;, &quot;fd_muid&quot;:&quot;0614EA13297C6EF33B0EFC1F28526F35&quot;, &quot;static_page&quot;:&quot;false&quot;, &quot;queryparams&quot;:&quot;?apiversion=v2&amp;domshim=1&amp;noservercache=1&amp;batchservertelemetry=1&amp;noservertelemetry=1&amp;reqsrc=rs&quot;, &quot;apptype&quot;:&quot;weather&quot;, &quot;pagetype&quot;:&quot;weather::weathertoday&quot;, &quot;configRootUrl&quot;:&quot;https://assets.msn.com/config/v1/&quot;, &quot;feedBaseDomain&quot;:&quot;&quot;, &quot;deviceFormFactor&quot;:&quot;desktop&quot;, &quot;pageGenTime&quot;:&quot;2025-06-15T03:12:56Z&quot;, &quot;pcsInfo&quot;:{&quot;v&quot;:&quot;********.5_master&quot;, &quot;env&quot;:&quot;prod-ssr&quot;}, &quot;locale&quot;:{&quot;language&quot;:&quot;zh&quot;, &quot;market&quot;:&quot;cn&quot;, &quot;content&quot;:{&quot;language&quot;:&quot;zh&quot;, &quot;market&quot;:&quot;cn&quot;}, &quot;display&quot;:{&quot;language&quot;:&quot;zh&quot;, &quot;market&quot;:&quot;cn&quot;}}, &quot;featureFlags&quot;:{}, &quot;market&quot;:{ &quot;requestInput&quot;:{ &quot;language&quot;:&quot;zh&quot;,&quot;region&quot;:&quot;cn&quot;, &quot;geo_country&quot;:&quot;CN&quot;}, &quot;detection&quot;:{ &quot;type&quot;:&quot;compliance&quot;,&quot;geo_country&quot;:&quot;&quot;,&quot;geo_countrycf&quot;:&quot;&quot;, &quot;recommendLanguageCoachmark&quot;: &quot;false&quot;}}, &quot;servicesEndpoints&quot;:{&quot;staticConfig&quot;: {&quot;domain&quot;: &quot;https://assets.msn.cn&quot;, &quot;path&quot;: &quot;/config/v3/&quot;, &quot;v&quot;: &quot;&quot;},&quot;crs&quot;: {&quot;domain&quot;: &quot;https://assets.msn.cn&quot;, &quot;path&quot;: &quot;/resolver/api/resolve/&quot;, &quot;v&quot;: &quot;v3&quot;},&quot;feedService&quot;: {&quot;domain&quot;: &quot;https://assets.msn.cn&quot;, &quot;path&quot;: &quot;&quot;, &quot;v&quot;: &quot;&quot;},&quot;enterpriseFeedService&quot;: {&quot;domain&quot;: &quot;https://ent-api.msn.com/&quot;, &quot;path&quot;: &quot;&quot;, &quot;v&quot;: &quot;&quot;}}, &quot;bundleInfo&quot;:{&quot;v&quot;:&quot;20250612.401&quot;, &quot;alias&quot;:&quot;latest&quot;}, &quot;os&quot;:&quot;windows&quot;, &quot;browser&quot;:{&quot;browserType&quot;:&quot;chrome&quot;, &quot;version&quot;:&quot;120&quot;, &quot;ismobile&quot;:&quot;false&quot;}, &quot;verticalKey&quot;:&quot;weather&quot;, &quot;categoryKey&quot;:&quot;weathertoday&quot;, &quot;domain&quot;:&quot;www.msn.cn&quot;, &quot;requestTrailInfo&quot;:{&quot;flightVersion&quot;:&quot;53531360&quot;}, &quot;detection&quot;:{&quot;scrubbedIp&quot;:&quot;************&quot;, &quot;tier&quot;:&quot;p&quot;}, &quot;geo_country&quot;:&quot;CN&quot;, &quot;geo_countryname&quot;:&quot;China&quot;, &quot;geo_subdivision&quot;:&quot;Sichuan&quot;, &quot;geo_zip&quot;:&quot;610100&quot;, &quot;geo_city&quot;:&quot;Longquanyi District&quot;, &quot;geo_lat&quot;:&quot;30.5694&quot;, &quot;geo_long&quot;:&quot;104.2805&quot;, &quot;geo_citycf&quot;:&quot;9&quot;, &quot;geo_countrycf&quot;:&quot;9&quot;, &quot;requestMuid&quot;:&quot;notFound&quot;}\" data-diagnostics=\"\">\r\n    <meta charset=\"utf-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n\r\n    \r\n    \r\n\r\n    \n            <title>Longquanyi-District 天气预报 | MSN 天气</title>\n            \r\n\r\n    \r\n    \r\n\r\n\r\n    \r\n    \r\n\r\n    \r\n\r\n    <script type=\"text/javascript\" nonce=\"\">\r\n        try {\r\n            window._pageTimings = { TTJSStart: Math.round(performance.now()) };\r\n            window._clientSettings = JSON.parse(document.head.dataset.clientSettings);\r\n        } catch (e) {\r\n            console.error(\"Error in adding TTJSStart marker\");\r\n        }\r\n    </script>\r\n\r\n        <script nonce=\"\">\r\n            function getCookieConsentRequired(){return true;}\r\n            window._ssrServiceEntryUrl=\"/bundles/v1/weather/latest/SSR-service-entry.9b1edb9fcd1484488d20.js\";\r\n        </script>\r\n\r\n\r\n        <script type=\"text/javascript\" id=\"inlinehead-inline-script\" nonce=\"\">\r\n            window[\"_webWorkerBundle\"] = \"/bundles/v1/weather/latest/web-worker.bd2f2a96271a41433e49.js\";\r\n            window[\"_authCookieName\"] = \"ace\";\r\n            !function(){\"use strict\";var e={},t={};function n(i){var o=t[i];if(void 0!==o)return o.exports;var s=t[i]={exports:{}};return e[i](s,s.exports,n),s.exports}function i(){return!(\"undefined\"==typeof window||!window.document||!window.document.createElement||window.isRenderServiceEnv)}n.g=function(){if(\"object\"==typeof globalThis)return globalThis;try{return this||new Function(\"return this\")()}catch(e){if(\"object\"==typeof window)return window}}();let o=new Map;const s=Object.freeze({set(e,t){return o.set(e,t),this},get:function(e,t){let n=o.get(e);return void 0===n&&t&&(n=t(),o.set(e,n)),n},clear(){o.clear()},delete:e=>o.delete(e),has:e=>o.has(e)});const r=\"__RequestDataInstance__\";class a{constructor(e,t){if(this.url=new URL(e.href),this.innerHeight=e.innerHeight,this.devicePixelRatio=e.devicePixelRatio,this.canUseCssGrid=e.canUseCssGrid,this.requestId=e.requestId,this.cookie=e.cookie,this.referer=e.referer,this.userAgent=e.userAgent,this.clientData=e.clientData,this.vpCetoToken=e.vpCetoToken,this.oneServiceHeaders=d(e.oneServiceHeaders)||{},this.isPssrMode=t,t){const e=c(\"OSATE\",this.cookie),t=!!e&&\"1\"===e,n=c(\"OSAT\",this.cookie);if(t&&n||!e&&!n)return this.msalAuthReady=!0,void(n&&(this.oneServiceHeaders.Authorization=`Bearer ${n}`));this.msalAuthReady=!1,this.pssrRejectedReason=e&&!t?\"interactiveLogin\":e&&!n?\"missOSAT\":\"missOSATE\"}}static getInstance(){const e=s.get(r);return e||(n.g.TEST_ENV?l({href:\"http://localhost:8080/\",innerHeight:768,devicePixelRatio:1,canUseCssGrid:!1,requestId:\"0\",cookie:\"\",userAgent:\"\",referer:\"\",oneServiceHeaders:\"\",vpCetoToken:\"\"}):l({href:\"http://localhost:8080/\",innerHeight:0,devicePixelRatio:0,canUseCssGrid:!1,requestId:\"0\",cookie:\"\",userAgent:\"\",referer:\"\",oneServiceHeaders:\"\",vpCetoToken:\"\"}))}static resetInstance(e){const t=new a(e,arguments.length>1&&void 0!==arguments[1]&&arguments[1]);return s.set(r,t),t}}function c(e,t){if(t&&e){const n=new RegExp(\"\\\\b\"+e+\"\\\\s*=\\\\s*([^;]*)\",\"i\").exec(t);return n&&n.length>1?n[1]:null}return null}function d(e){try{if(e)return JSON.parse(e)}catch(e){}}const l=a.resetInstance,u=()=>a.getInstance();function h(){return i()?window.location.search:u().url.search}var g,p;!function(e){e.Anon=\"ANON\",e.AppAnon=\"APP_ANON\",e.Unknown=\"\"}(g||(g={})),function(e){e.AnonCookieExists=\"ace\",e.AppAnonCookieExists=\"aace\"}(p||(p={}));const m=\"child\";function w(e,t){if(e){const n=new RegExp(\"\\\\b\"+t+\"\\\\s*=\\\\s*([^;]*)\",\"i\").exec(e);return n&&n.length>1?n[1]:null}return null}function f(e){try{if(e)return JSON.parse(e)}catch(e){}return null}const v=\"prg-pr2-\",b=\"prg-pw-\",y=\"prg-\",S=\"prg-1sw-\";function C(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:y;const n=t.length,i=[];return e&&e.length&&(e.forEach((e=>{const o=e&&e.trim();o&&o.length>=n&&o.substring(0,n).toLowerCase()===t&&i.push(o.toLowerCase())})),i.sort()),i}function k(e){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:\"\",n=y;var i,o;return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?n=b:t.toLocaleLowerCase()===\"windowsShell\".toLocaleLowerCase()||t.toLocaleLowerCase()===\"windowsShellV2\".toLocaleLowerCase()?n=S:t.toLocaleLowerCase()!==\"winWidgets\".toLocaleLowerCase()||(null===(i=window)||void 0===i||null===(i=i.location)||void 0===i||null===(i=i.search)||void 0===i?void 0:i.includes(\"reqsrc=vp\"))||null!==(o=window)&&void 0!==o&&null!==(o=o.location)&&void 0!==o&&null!==(o=o.search)&&void 0!==o&&o.includes(\"vptest\")||(n=v),n}function I(e){var t;return document.head.dataset[e]||(null===(t=q())||void 0===t?void 0:t.dataset[e])}let A;function q(){return void 0===A&&(A=document.getElementsByClassName(\"peregrine-widget-settings\")[0]||null),A}function T(e){var t;if(!e||!e.startsWith(\"f:\"))return;const n=(e.match(/f:\\s*([^;]+)/i)||[])[1];return n?null===(t=n.toLowerCase().split(\",\"))||void 0===t?void 0:t.sort():[]}const _=\"Authorization\";var E,x;!function(e){e.Presentation=\"presentation\"}(E||(E={})),function(e){e.Unknown=\"Unknown\",e.Portrait=\"Portrait\",e.Landscape=\"Landscape\"}(x||(x={}));var R,O;!function(e){e[e.Undefined=0]=\"Undefined\",e[e.Basic=1]=\"Basic\",e[e.Advanced=2]=\"Advanced\",e[e.Premium=3]=\"Premium\"}(R||(R={})),function(e){e.Init=\"init\",e.Config=\"config\",e.Targeted=\"targeted\",e.Sticky=\"sticky\",e.NoSticky=\"no_sticky\",e.Admin=\"admin\",e.Forced=\"forced\",e.Manual=\"manual\"}(O||(O={}));const L=new Set([\"finance-app-win\",\"weather-app-win\",\"winpstoreapp\"]),W=new Set([\"hpmsn\"]);new Set([\"chromentpnews\"]);function N(){var e;return i()?(window._clientSettings=window._clientSettings||f(document.head.dataset.clientSettings||(null===(e=document.getElementsByClassName(\"peregrine-widget-settings\")[0])||void 0===e?void 0:e.getAttribute(\"data-client-settings\"))),window._clientSettings):null}const P=\"feed/personalize/settings\";function F(e,t){const n=t.replace(/[[\\]]/g,\"\\\\$&\"),i=new RegExp(\"[?&]\"+n+\"(=([^&#]*)|&|#|$)\").exec(e);if(!i)return null;const o=i[2];return decodeURIComponent(o.replace(/\\+/g,\" \"))||\"\"}let D=new class{constructor(){i()?(this.isDebugEnabled=(F(window.location.href,\"debug\")||\"\").toLowerCase()in{1:1,true:1}||\"vp\"===(F(window.location.href,\"reqsrc\")||\"\").toLowerCase(),this.isDebugEnabled&&!n.g.TEST_ENV&&(window.webpackRequire=n)):this.isDebugEnabled=!1}getLoggingService(){return null}isDebug(){return this.isDebugEnabled}setDebug(e){this.isDebugEnabled=e}setTags(e){}log(e,t){this.isDebug()&&console.info(e)}logError(e){console.error(e)}logCallback(e){this.isDebug()&&console.info(e())}logObjects(){this.isDebug()&&console.log(...arguments)}logSingleObject(e,t){this.isDebug()&&console.log(e)}};class M{get supported(){return!!this.storage}constructor(e){this.storage=e}getItem(e){if(this.supported)return this.storage.getItem(e)}getObject(e,t){const n=this.getItem(e);if(null!=n){const e=f(n);if(null!=e)return e}return t}key(e){if(this.supported&&e>=0)return this.storage.key(e)}keys(){return this.supported?Object.keys(this.storage):[]}setObject(e,t){void 0!==t?this.setItem(e,JSON.stringify(t)):this.removeItem(e)}removeObject(e){const t=this.removeItem(e);if(null!=t)return f(t)}setItem(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(this.supported)try{if(!t)throw\"Attempted to store null/undefined value: \"+t;this.storage.setItem(e,t)}catch(e){if(!n)throw e;D.logError(e)}else if(!n)throw new Error(\"WebStorage not supported\")}get length(){if(this.supported)return this.storage.length}removeItem(e){if(this.supported){const t=this.getItem(e);return this.storage.removeItem(e),t}}clear(){this.supported&&this.storage.clear()}removeSubstringKeys(e){if(!this.supported||!e)return;const t=[];for(let n=0;n<this.storage.length;n++){const i=this.key(n);i&&i.includes(e)&&t.push(i)}for(let e=0;e<t.length;e++)this.removeItem(t[e])}}let H=new class extends M{constructor(){let e=null;if(i())try{e=localStorage}catch(e){}super(e)}};const U=\"1s-tokens\";var $;let j,B,J;function V(){return j=G(),j&&j.accessToken?j:void 0}function G(){return j=j||void 0===j&&H.getObject(U)||null,j}function z(e){try{return decodeURIComponent(e)}catch(e){}}function K(e,t){if(!e)return null;if(t)return function(e){var t;const n=null===(t=document.cookie)||void 0===t?void 0:t.match(`\\\\b${e}=([^;]*)`);return n?z(n[1]):null}(e);if(!J){const e=\"undefined\"!=typeof document&&document.cookie.split(\"; \");J={};const t=e&&e.length;for(let n=0;n<t;n++){const t=e[n].indexOf(\"=\");J[e[n].slice(0,t).toLocaleLowerCase()]=z(e[n].slice(t+1))}}const n=e.toLocaleLowerCase();return J[n]||null}function X(e){const t=K(e);return t&&encodeURIComponent(t)||null}function Q(e){let t,n=!1;const i=function(){return n||(n=!0,t=e()),t};return i.cache={clear:()=>{n=!1,t=void 0}},i}!function(e){e.NotAvailable=\"notAvailable\",e.Expired=\"expired\",e.Valid=\"valid\"}($||($={}));const Y=\"currentaccount\",Z=Q((()=>{const e=K(Y,!0),t=e&&f(e),{login_hint:n}=t||{};return n&&t})),ee=Q((()=>!!K(Y)));function te(){try{return localStorage}catch(e){return null}}const ne=\"__PageExperimentInstance__\";function ie(e){s.set(ne,e)}function oe(e){if(!s.has(ne)&&i()){var t;ie(se(document.head.dataset.info||(null===(t=window.document.getElementsByClassName(\"peregrine-widget-settings\")[0])||void 0===t||null===(t=t.dataset)||void 0===t?void 0:t.info)||\"\"))}const n=s.get(ne);return n&&n.has(e)}function se(e){const t=(e||\"\").replace(/(^f:|;.*$)/g,\"\").split(\",\");return new Set(t)}const re=\"uxlogin\",ae=\"uxlogout\",ce=\"uxedit\",de=\"useRedirect\",le=\"uxswitch\",ue=Q((()=>{const e=te();return e&&\"1\"===e.getItem(re)||i()&&location.search&&location.search.includes(`${re}=1`)}));const he=Q((()=>{const e=function(){try{return sessionStorage}catch(e){return null}}();return e&&!!e.getItem(ae)}));const ge=Q((()=>{const e=te();return e&&\"1\"===e.getItem(ce)}));const pe=Q((()=>{const e=te();return!oe(\"prg-noredirect\")&&e&&\"1\"===e.getItem(de)&&function(){const{browser:e}=window._clientSettings||{},{browserType:t=\"\"}=e||{};return!!/safari/i.test(t)}()}));const me=Q((()=>{const e=te();return e&&\"1\"===e.getItem(le)}));const we=\"OnOOutlookMobile\",fe=\"OnOStartApp\",ve=\"xmweather-\",be=\"launcherntp\",ye=\"launcherInterests\",Se=\"swiftKey\",Ce=\"superappdhp\",ke=\"_ocidIsomorphicKey\",Ie=()=>(s.get(ke)||s.set(ke,He(\"ocid\").toLowerCase()),s.get(ke)),Ae=\"_platformFlagKey\",qe=\"_hostAppNameKey\",Te=\"_clientTypeKey\",_e=()=>(s.get(Ae)||s.set(Ae,He(\"chpltfm\")),s.get(Ae)),Ee=()=>{if(!s.get(qe)){const e=_e();s.set(qe,e.toLowerCase().split(\"-\")[0])}return s.get(qe)},xe=()=>{if(!s.get(Te)){const e=_e();s.set(Te,e.toLowerCase().split(\"-\")[1])}return s.get(Te)};function Re(){if(!i()||De())return!1;const e=window.sapphireWebViewBridge,t=window.webkit,n=e&&e.send,o=t&&t.messageHandlers&&t.messageHandlers.send&&t.messageHandlers.send.postMessage;return!(!n&&!o)}function Oe(){return!!window._isCopilotBridge||\"copilot\"===He(\"MBApp\")}function Le(){const e=_e()&&\"outlook\"===Ee()&&[\"ios\",\"android\",\"ipados\"].includes(xe()),t=Ie()===we.toLowerCase();return e||t}function We(){return!(!i()||De())&&(Re()||Ie()===fe.toLowerCase()||Ie()===Ce.toLowerCase())}function Ne(){const e=He(\"edge\");return(\"emmx\"===Ee()||\"1\"===e)&&We()||\"mmx\"===Ie()}function Pe(){return\"3rdp\"===Ee()||Ie().startsWith(ve)||De()}function Fe(){return[be.toLowerCase(),ye.toLowerCase()].includes(Ie())&&We()}function De(){return Ie().toLowerCase()===Se.toLowerCase()||\"swiftkey\"==Ee().toLowerCase()}const Me=Q((()=>Re()||Le()||We()||Ie()===\"weather-app-win\".toLowerCase()||Ie()===\"finance-app-win\".toLowerCase()||Ne()||Pe()||Fe()||De()||-1!==window.location.href.indexOf(\"metaos=true\")||Oe()));function He(e){try{return new URL(location.href).searchParams.get(e)||\"\"}catch(e){return\"\"}}const Ue=N()||{};const $e={winWidgets:1,webWidgets:1,windowsShell:1,windowsShellV2:1,distribution:1,superApp:1,channelmobile:1,edgeMobile:1},je={edgeChromium:1},Be={winWidgets:1,webWidgets:1},Je={edgeChromium:1,shopping:1,winWidgets:1,webWidgets:1,windowsShell:1,windowsShellV2:1,msn:1},Ve={edgeChromium:1,channelmobile:1,cgHomePage:1,distribution:1,winWidgets:1,webWidgets:1,windowsShell:1,windowsShellV2:1,superApp:1,edgeMobile:1},Ge=Q((()=>Ue&&Ue.apptype)),ze=(Q((()=>i()&&Ge()in Be)),Q((()=>i()&&!Me()&&!(Ge()in $e)&&!Qe()&&!Ye()))),Ke=Q((()=>ze()&&!(Ge()in je)));Q((()=>ze()&&(!(Ge()in je)||!ee()))),Q((()=>ze()&&!et()&&!tt()));const Xe=Q((()=>tt()||et())),Qe=Q((()=>!(Ge()in Je)&&nt()&&function(){if(\"homePage\"==Ge())return!0;const e=new URLSearchParams(h()).get(\"ocid\")||(null==Ue?void 0:Ue.ocid),t=null==e?void 0:e.toLowerCase();if(W.has(t))return!0;return!L.has(t)}()&&!function(){const e=\"homePage\"==Ge()&&\"hp\"==(Ue&&Ue.pagetype)&&(i()?window.location.href:u().url.href).includes(`${P}`);return e}()));const Ye=()=>i()&&document&&document.body&&document.body.getElementsByClassName(\"peregrine-widgets\").length>0;Q((()=>Ge()in Ve||Qe()));function Ze(){if(B)return B;const e=V();return e||void 0}function et(){return Ke()&&ue()||function(){const e=Ke()&&!it()&&!ue()&&Z()&&(\"expired\"==function(){if(j=G(),!j)return\"notAvailable\";const e=j.expiresOn,t=new Date(e),n=\"expired\";return t.getTime()&&t>new Date?\"valid\":n}()||ge());return e}()||he()||pe()}function tt(){return Ke()&&!Z()&&me()}const nt=Q((()=>{const{browser:e}=Ue,{browserType:t=\"\",detected:n={}}=e||{},{browserType:i=\"\"}=n||{};return/edgeChromium/i.test(t)||/edgeAndroid/i.test(t)||/edgeiOS/i.test(t)||/edge/i.test(i)}));Q((()=>Ke()&&!Qe()));const it=()=>{const e=Z();if(!e)return!1;let t;switch(e.account_type){case\"MSA\":t=Ke()?\"ace\":\"aace\";break;case\"AAD\":t=Ke()?\"WID\":\"APP_WID\";break;default:return!1}return!!K(t)};const ot=\"__core_auth_authHeaderPromiseParts__\",st=()=>s.get(ot);let rt;function at(){return rt}!function(){let e;const t={promise:new Promise((t=>e=t)),resolver:e};s.set(ot,t)}();const ct=Q((()=>f(K(\"AL_STATE\",!0))));class dt{constructor(){this.childCookieName=\"child\",this.command=\"command\",this.domInfo=document.head.dataset.info,this.canvasInfo=document.head.dataset.canvasInfo||\"\"}init(e,t){this.clientSettings=e||N(),this.authCookieName=at()||window._authCookieName||\"\",this.bundle=window._webWorkerBundle||\"\",t=t||{};const n=this.buildWebWorkerUrl(this.clientSettings,t),i=window.webWorker=this.createWebWorkerInstance(n);i?this.initCore(i):console.warn(\"Error while initializing the web worker.\")}initCore(e){const t=window.webWorkerMessages=[];e.onmessage=e=>{t.push(e.data)};const n=window.webWorkerErrors=[];e.onerror=e=>{n.push(e)},Xe()&&st().promise.then((t=>e.postMessage({id:\"AuthHeaders\",type:this.command,payload:t||{}})))}createWebWorkerInstance(e){return new Worker(e,{name:\"web-worker\"})}shouldFetchAppAnonCookie(){return!0}getAudienceMode(e){const t=K(this.authCookieName),n=K(this.childCookieName),{child:i}=t&&JSON.parse(t)||{};return t&&(1===i||n)?\"kids\":e.audienceMode||\"adult\"}buildWebWorkerUrl(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;return(this.bundle||\"\")+\"#\"+this.qsFromDocument()+this.qsFromCookies()+this.qsFromDataAttribute(e||this.clientSettings)+this.qsMisc(t)+this.qsAuth(e||this.clientSettings)+this.qsFromUrl()+this.qsFromServiceWorker()+this.qsSelectedPivot()+this.qsXboxXToken()}qs(e,t,n){return e?t+\"=\"+(n||e)+\"&\":\"\"}qsFromDocument(){var e;return this.qs(document.getElementsByTagName(\"html\")[0].getAttribute(\"lang\"),\"lang\")+this.qs(encodeURIComponent(window.location.href),\"adsReferer\")+this.qs(null===(e=i()?window.devicePixelRatio:u().devicePixelRatio)||void 0===e?void 0:e.toString(),\"devicePixelRatio\")}qsFromServiceWorker(){if(!navigator.serviceWorker||!navigator.serviceWorker.controller)return\"\";const e=navigator.serviceWorker.controller;if(\"activated\"!==e.state||!e.scriptURL)return\"\";if(e.scriptURL){const t=new URL(e.scriptURL).searchParams.toString();return t?\"&\"+t+\"&\":\"\"}return\"\"}qsFromCookies(){return this.qs(X(this.authCookieName),\"aace\",\"1\")+this.qs(X(\"muid\"),\"muid\")+this.qs(X(this.childCookieName),\"child\")+this.qs(X(\"cbypass\"),\"cbypass\")+this.qs(this.cookieBannerConsentRequired().toString(),\"ccRequired\")+this.qs(X(\"ecasession\"),\"ecasession\")+this.qs(X(\"TOptOut\"),\"browserOptOut\")}qsFromDataAttributeHelper(e,t,n){var i;e&&e.featureFlags&&e.featureFlags.wpoEnabled&&t&&!t.includes(\"remwpofltww\")&&(t=this.appendWpoFlightsToInfo(e,t));const o=(null===(i=e.modes)||void 0===i?void 0:i.uxMode)||void 0;return this.qs(e.pcsInfo&&e.pcsInfo.env||e.env,\"env\")+this.qs(e.aid,\"aid\")+this.qs(e.apptype,\"apptype\")+this.qs(e.pagetype,\"pagetype\")+this.qs(e.audienceMode,\"audienceMode\")+this.qs(e.configIndexDocId,\"configIndexDocId\")+this.qs(e.deviceFormFactor,\"deviceFormFactor\")+this.qs(e.domain,\"domain\")+this.qs(e.configRootUrl,\"configRootUrl\")+this.qs(this.getHighestCbid(e.cbid,e.apptype),\"cbid\")+this.qs(e.ocid,\"ocid\")+this.qs(e.os,\"os\")+this.qs(JSON.stringify(e.locale),\"locale\")+this.qs(e.geo_lat,\"lat\")+this.qs(e.geo_long,\"long\")+this.qs(JSON.stringify(e.featureFlags),\"featureFlags\")+this.qs(JSON.stringify(e.browser),\"browser\")+this.qs(JSON.stringify(e.servicesEndpoints),\"servicesEndpoints\")+this.qs(e.bundleInfo&&e.bundleInfo.v||\"\",\"buildVersion\")+this.qs(t,\"dataInfo\")+this.qs(n,\"canvasInfo\")+this.qs(o,\"uxMode\")}qsSelectedPivot(){const e=\"selectedPivot\";let t;try{const n=te();n&&(t=n.getItem(e))}catch(e){console.warn(\"Error getting pivot id from local storage. \"+e)}return this.qs(t,e)}qsXboxXToken(){const e=\"xboxXTokenId\";let t;try{const n=te();n&&(t=n.getItem(e))}catch(e){console.warn(\"Error getting xbox XToken from local storage. \"+e)}return this.qs(t,e)}appendWpoFlightsToInfo(e,t){const n=this.getWpoFlightsFromLocalStorage(e);if(n&&n.length){const e=\";\",i=t.split(e);for(let e=0;e<i.length;e++){const t=i[e];if(t&&t.startsWith(\"f:\")){i[e]=t+\",\"+n.join();break}}return i.join(e)}return t}getWpoFlightsFromLocalStorage(e){const t=te(),n=e.locale,i=`wpo_data_ ${e.audienceMode}_${n.language}_${n.market}_${e.pagetype}_${e.apptype}`;try{const e=t.getItem(i);if(e){return JSON.parse(e).treatments.map((e=>e.id))}}catch(e){console.warn(`Error getting wpo flights from ls for ${i} Error:${e}`)}return null}getHighestCbid(e,t){try{const n=te(),i=parseInt(e),o=parseInt(JSON.parse(n.getItem(`cbid_${t||\"\"}`)));let s;if(i&&!isNaN(i)&&o&&!isNaN(o)&&(s=i>o?i:o),s=s||i||o,s)return window._cbid=s.toString()}catch(e){console.warn(\"Error getting highest CBID\"+e)}}qsFromDataAttribute(e){let t,n,i;const o=te();if(o){const i=JSON.parse(o.getItem(\"shd_\"+e.pagetype)||null);i&&i.clientSettings&&i.info&&(t={...i.clientSettings,bundleInfo:e.bundleInfo},n=i.info)}return(!t||!n||e.pageGenTime>=t.pageGenTime)&&(t=e,n=this.domInfo,i=this.canvasInfo),t.audienceMode=this.getAudienceMode(t),this.qsFromDataAttributeHelper(t,n,i)}qsFromUrl(){const e=location.search;return e&&e.length?e.substring(1,e.length):\"\"}qsMisc(e){return this.qs(performance.timeOrigin&&performance.timeOrigin.toString(),\"mainTimeOrigin\")+this.qs(e.disableWWConfig&&\"1\",\"disableWWConfig\")}qsAuth(e){let t=this.qs(Ke()&&\"1\",\"disableWWAuth\");if(!Xe()){const e=Ke()&&Ze()||{};t+=this.qs(encodeURIComponent(JSON.stringify(function(e){const t={};return e&&(t[_]=`Bearer ${e}`),t}(e.accessToken))),\"authHeaders\")}return t+=this.qs(function(e){const t=e||at()||\"al_app_anon\",n=ct();return!(!n||!([\"anon\",\"al_anon\"].includes(t)&&n.AL_ANON||[\"app_anon\",\"al_app_anon\"].includes(t)&&n.AL_APP_ANON))}()&&\"1\",\"isAccountLinked\"),t+=this.qs(this.shouldFetchAppAnonCookie()&&\"1\",\"fetchAppAnonCookie\"),t}cookieBannerConsentRequired(){const e=null!=document.getElementById(\"consent-banner-container\")&&null===K(\"MSCC\"),t=null!=document.getElementById(\"onetrust-sdk\")&&null===K(\"eupubconsent-v2\");return e||t}}const lt=Object.freeze({AIDRequestHeaderName:\"pcs-aid\",Product:Object.freeze({edge:\"spartan\",edgeChromium:\"anaheim\",hybrid:\"spartan\",hub:\"prime\",microsoftNews:\"msnews\",office:\"entnews\",weather:\"prime\",views:\"prime\"}),PageTimings:Object.freeze({inlineStart:\"page.inlineStart\",fragmentFetchStart:\"ssr.fragmentFetching\",fragmentFetchEnd:\"ssr.fragmentFetched\",fragmentFetchStatusCode:\"ssr.fragmentFetchStatusCode\",fragmentBrowserCached:\"ssr.fragmentBrowserCached\",fragmentCDNCached:\"ssr.fragmentCDNCached\",injectingDOM:\"ssr.injectingDOM\",awaitingRAF:\"ssr.awaitingRAF\",markersSet:\"ssr.markersSet\",completed:\"ssr.completed\",CSRTimeout:\"ssr.CSRTimeout\",CSROnly:\"ssr.csrOnly\",didSSR:\"ssr.isSSRPagePresent\",riverEnabled:\"ssr.riverEnabled\",ssrState:\"ssr.state\",pageTTVR:\"TTVR\",fetchDelayReason:\"ssr.fetchDelayReason\",awaitingInnerWidthTime:\"ssr.awaitingInnerWidthTime\",skipDueToHiddenReason:\"ssr.skipDueToHiddenReason\",pssrRejectedReason:\"ssr.pssrRejected\",ssrResponseSize:\"ssr.responseSize\",inlineBodyStart:\"ssr.inlineBodyStart\",pcsEnv:\"pcsEnv\"}),pauseThresholdMS:1200,oneServiceHeaders:\"1s-headers\",edgeNTPHeader:\"sec-edge-ntp\",defaultSSRFeedLayout:\"multi\"});var ut,ht,gt;!function(e){e[e.Hub=0]=\"Hub\",e[e.Homepage=1]=\"Homepage\",e[e.Edge=2]=\"Edge\",e[e.Weather=3]=\"Weather\",e[e.Finance=4]=\"Finance\",e[e.Community=5]=\"Community\",e[e.Views=6]=\"Views\",e[e.Gaming=7]=\"Gaming\",e[e.Sports=8]=\"Sports\",e[e.Health=9]=\"Health\",e[e.Shopping=10]=\"Shopping\",e[e.CgHomePage=11]=\"CgHomePage\"}(ut||(ut={})),function(e){e[e.documentHidden=0]=\"documentHidden\",e[e.hasPaused=1]=\"hasPaused\",e[e.invalidInnerWidth=2]=\"invalidInnerWidth\",e[e.slowFetchStart=3]=\"slowFetchStart\"}(ht||(ht={})),function(e){e.JSON=\"application/json;charset=UTF-8\",e.HTML=\"text/html;charset=UTF-8\"}(gt=gt||(gt={}));function pt(){return i()?window._pageTimings||(window._pageTimings={}):s.get(\"__diagnostics_pageTimings\",(()=>({})))}function mt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:performance.now(),n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];const i=pt();e in i||(i[e]=n?Math.round(t):t)}function wt(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];window.performance.mark(e),t&&mt(e)}let ft,vt;function bt(){if(vt)return vt;const e=document.head.getAttribute(\"data-info\");return vt=((/f:\\s*([^;]+)/i.exec(e)||{})[1]||\"\").toLowerCase(),vt}function yt(e,t,n){const o=n&&\"phone\"===n.toLowerCase(),s=t&&\"enterprise\"===t;let r=\"Edge\";De()?r=\"swiftKey\":Fe()?r=\"Launcher\":function(){var e;if(!i())return!1;if(\"bingHomepageMobile\"===(null===(e=window._clientSettings)||void 0===e?void 0:e.pagetype))return!0;{var t;const e=null===(t=document.getElementsByClassName(\"peregrine-widget-settings\")[0])||void 0===t?void 0:t.getAttribute(\"data-client-settings\");if(e){const t=JSON.parse(e);return t&&t.pagetype&&\"bingHomepageMobile\"===t.pagetype}return!1}}()&&(r=\"bingHomepageMobile\");return{bingHomepage:\"binghomepage\",mmx:\"emmx\",edge:\"spartan\",edgeChromium:s?\"entnews\":\"anaheim\",hybrid:\"spartan\",hub:o?\"prime_mobile\":\"prime\",microsoftNews:\"msnews\",office:\"entnews\",views:o?\"prime_mobile\":\"prime\",homePage:o?\"prime_mobile\":\"prime\",windowsShell:\"windowsshell\",edgeMobile:r}[e]}const St={homePage:{vertical:\"homepage\",category:\"\",id:\"\",domainId:\"13041\",title:\"undefined\"!=typeof document?document.title:\"\"}};function Ct(e){let t=e;switch(e){case\"windowsshellhp\":t=\"dhp\";break;case\"video\":t=\"watch\";break;case\"EdgeMobile\":t=Ne()?\"ntp\":\"dhp\"}return t}let kt;var It;!function(e){e[e.Alert=0]=\"Alert\",e[e.Deprecated=1]=\"Deprecated\",e[e.HighImpact=2]=\"HighImpact\",e[e.Critical=3]=\"Critical\"}(It||(It={}));const At=new class{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20;this.maxLength=e,this.list=[]}push(e){this.list.push(e),this.list.length>this.maxLength&&this.list.shift()}get data(){return this.list}};function qt(e,t,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:It.Alert,o=arguments.length>4?arguments[4]:void 0,s=arguments.length>5?arguments[5]:void 0,r=arguments.length>6?arguments[6]:void 0;try{const a=function(){if(!ft){const e=document.head.getAttribute(\"data-client-settings\");e&&(ft=JSON.parse(e))}return ft}(),c=bt(),d=function(e){if(e){const{pcsInfo:t,pageGenTime:n}=e,i=new Date(n).getTime(),o=!t||[\"prod\",\"prod-ssr\",\"prod-ssrntp\"].includes(t.env);kt=o?\"browser.events.data.msn.com\":\"events-sandbox.data.msn.com\";return{cors:\"true\",\"content-type\":\"application/x-json-stream\",\"client-id\":\"NO_AUTH\",\"client-version\":\"1DS-Web-JS-2.2.2\",apikey:o?\"0ded60c75e44443aa3484c42c1c43fe8-9fc57d3f-fdac-4bcf-b927-75eafe60192e-7279\":\"f8857dedc6f54ca8962cfb713e01e7d7-e9250191-fe0b-446f-95ae-07516262f98c-7028\",\"upload-time\":i,w:\"0\",anoncknm:\"app_anon\"}}return null}(a);let l=\"\";d&&d.apikey&&\"\"!==d.apikey&&(l=function(e){if(e){const t=e.indexOf(\"-\");if(t>0)return e.substring(0,t)}return\"\"}(d.apikey));const u=function(e,t,n,i,o,s){let r=arguments.length>6&&void 0!==arguments[6]?arguments[6]:It.Alert,a=arguments.length>7&&void 0!==arguments[7]?arguments[7]:\"\";if(n){o=o||{};const{aid:u,apptype:h,audienceMode:g,pagetype:p,pageGenTime:m,bundleInfo:w,deviceFormFactor:f=\"\",fd_muid:v,os:b,static_page:y}=n;o.pageGenTime=m,o.build=w&&w.v,o.appType=h,o.pageVisible=document.visibilityState;const S=yt(h,g,f),C=S||h,k=document.getElementsByTagName(\"html\")[0].getAttribute(\"lang\");let I,A=\"\",q=\"muid\";try{if(\"edgeChromium\"===h&&\"object\"==typeof window&&window.location&&window.location.search){const e=new URLSearchParams(window.location.search);I=e.has(\"startpage\")?\"msedgdhp\":\"msedgntp\",\"enterprise\"===g?I=\"entnewsntp\":\"xbox\"===b&&(I=\"xboxntp\")}window&&window.getCookieConsentRequired&&\"function\"==typeof window.getCookieConsentRequired&&window.getCookieConsentRequired()||(A=(null===(d=window)||void 0===d||null===(d=d.__SSRUserConfigEarlyLog)||void 0===d?void 0:d.consistentMuid)||(null===(l=window)||void 0===l?void 0:l.__muid)||K(\"muid\"))}catch{}A||(A=n.aid,q=\"aid\");const T={name:\"MS.News.Web.AppError\",time:m,ver:\"4.0\",iKey:`o:${i}`,data:{baseData:{},baseType:\"MS.News.Web.Base\",page:{name:\"default\",product:C,type:Ct(p),content:St[h]??{category:\"standaloneError\"},ocid:I,url:location.href,isStatic:y},browser:{clientId:A,clientIdType:q},flight:{id:s,tmpl:a},request:{activityId:u,requestId:u,afdMuid:v},locale:{mkt:k},extSchema:{id:e,severity:r,pb:o,message:t}}};var c;if(A&&\"muid\"===q)T.ext={...null==T?void 0:T.ext,user:{...null==T||null===(c=T.ext)||void 0===c?void 0:c.user,localId:`t:${A}`}};return\"object\"==typeof window&&(window.isSSREnabled&&(T.data.flight.tmpl+=\";ssr-enabled:1\"),window.isSSRCompleted&&(T.data.flight.tmpl+=\";ssr-completed:1\")),T?JSON.stringify(T):null}var d,l;return null}(t,e,a,l,n,c,i,r);if(s&&!function(e){if(null==e)return!1;return e.startsWith(\"1\")||e.startsWith(\"2\")||e.startsWith(\"3\")||e.startsWith(\"4\")}(null==a?void 0:a.aid))return console.error(u),void console.error(`This App error Id: ${t} will not be sent due to app error sampling!`);if(d&&u){console.error(u),At.push(u);const e=\"https://\"+kt+\"/OneCollector/1.0\"+function(e){return\"?\"+Object.keys(e).map((function(t){return t+\"=\"+encodeURIComponent(e[t])})).join(\"&\")}(d);if(o&&!performance.getEntriesByType(\"visibility-state\").some((e=>\"visible\"===e.name))){const t=()=>{\"visible\"===document.visibilityState&&navigator.sendBeacon(e,u)};document.addEventListener(\"visibilitychange\",t,{once:!0})}else navigator.sendBeacon(e,u)}}catch{}}const Tt=\"experience\",_t=[\"common-monthly\"],Et=Object.create(null),xt=2;function Rt(e,t){if(!(t=t||Array.from(document.scripts).find((t=>t.src.indexOf(`/${e}.`)>-1))))return;const n=function(e,t){const n=document.createElement(\"script\");return n.type=\"text/javascript\",n.crossOrigin=\"anonymous\",n.src=e,n.onerror=()=>{let e=Et[t]||0;Et[t]=++e,e<xt?Rt(t,n):Lt()},n.onload=()=>{delete Et[t];const e=window._pageTimings.retriedBundles;window._pageTimings.retriedBundles=e?`${e},${t}`:t,Lt()},n}((i=t.src,Et[e],i),e);var i;setTimeout((()=>t.replaceWith(n)),100)}function Ot(){const[e={}]=performance.getEntriesByType(\"navigation\");return e.type}function Lt(){if(!Object.values(Et).every((e=>e===xt)))return;let e=\"\";Object.keys(Et).forEach((t=>{e=e?`${e},${t}`:t,delete Et[t]})),e&&qt(`Error when loading bundle(s): ${e}`,20202,{timeElapsed:Math.round(performance.now()),navType:Ot()})}function Wt(e){return new Promise((t=>setTimeout(t,e)))}\"undefined\"!=typeof window&&window.document&&window.document.createElement&&(window._pageTimings=window._pageTimings||{},window.requestIdleCallback=window.requestIdleCallback||window.setTimeout);wt(lt.PageTimings.inlineStart,!0);const{inlineCRS:Nt}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t={};try{var n,o,s;if(!i())return t;const r=f(I(\"clientSettings\"))||{};let a=null===(n=r.apptype)||void 0===n?void 0:n.toLocaleLowerCase();const c=e.crsOptions||{};\"winwidgets\"!==a&&\"homepage\"!==a&&\"travel\"!==a||(c.crsPrefetch=!0),null!==(o=r.widgetAttributes)&&void 0!==o&&null!==(o=o.clientPerf)&&void 0!==o&&o.earlyCRSCall&&(c.crsPrefetch=!0),\"edgechromium\"===a&&(a=\"edgechr\");const d=!(null===(s=r.widgetAttributes)||void 0===s||null===(s=s.clientPerf)||void 0===s||!s.shouldUsePWOnlyFlights),{all:l,prg:g}=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"\";const n=T(I(\"info\"))||[];let i=T(I(\"canvasInfo\"));if(!i){i=C(n,k(n,e,t))}return{all:n,prg:i}}(d,a);(c.crsPrefetch||l.includes(\"inlinecrscall\")||l.includes(`icrscall-${a}`))&&(!function(e,t,n){const o=function(e,t){let n;const i=decodeURIComponent(w(e,\"aace\")),{child:o}=i&&f(i)||{},s=w(e,m);var r;return n=i&&(1===o||s)?\"kids\":(null===(r=t.modes)||void 0===r?void 0:r.audienceMode)||t.audienceMode||\"adult\",n}(document.cookie,e),{apptype:s,browser:r,domain:a,bundleInfo:{v:c}={},deviceFormFactor:d,locale:{content:l,display:g}={},ocid:p,os:v,modes:b,pagetype:y,servicesEndpoints:{crs:S}={}}=e,C=(q()?i()?window.location.hostname:u().url.hostname:a)||\"\";if(!s||!c||!S)return;const k={audienceMode:o,uxMode:null==b?void 0:b.uxMode},I={audienceMode:o,browser:r,deviceFormFactor:d,domain:C,locale:{content:l,display:g},ocid:p,os:v,modes:k,platform:\"web\",pageType:y,pageExperiments:t},A=new URLSearchParams([[\"expType\",\"AppConfig\"],[\"expInstance\",\"default\"],[\"apptype\",s],[\"v\",c]]),T=h(),_=T.includes(\"?item=\")||T.includes(\"&item=\")?\"&citem=dev:true&instance=Int1\":\"\",E=`${S.path}${S.v}/config/?${A}&targetScope=${JSON.stringify(I)}${_}`,x=e=>{const t=new URL(`https://${e}${E}`),{timeout:i=5e3}=n;let o;if(window.crsRequestData={url:t.toString()},i>=0){const e=new AbortController;setTimeout((()=>e.abort()),i),o=e.signal}return fetch(t,{signal:o})},R=\"msn.\"+(function(e,t){return!!(e.hostname.toLowerCase().endsWith(\".cn\")||t.isChinaCompliance||t.useCnDomain||e.search.match(/isChinaBuild=true/i)||e.search.match(/item=revip:cn/))}(location,e)?\"cn\":\"com\"),{domain:O}=S,L=O&&new URL(O).host;let W=x(L||`assets.${R}`);n.disableFallback||(W=W.catch((()=>{const{hostname:e}=location,t=e.match(/\\.msn\\.(com|cn)$/)?e:`www.${R}`;return x(t)})));n.silenceUnhandledError&&(W=W.catch((e=>{console.error(\"InlineHeadCommon: CRS fetch error in silence\",e)})));window.crsPromise=W}(r,g,c),t.inlineCRS=!0)}catch(e){console.error(\"InlineHeadCommon: \"+e)}return t}(),Pt=N();function Ft(e){var t;return((null===(t=document)||void 0===t||null===(t=t.head)||void 0===t||null===(t=t.getAttribute(\"data-info\"))||void 0===t?void 0:t.split(\";\").find((e=>e.startsWith(\"f:\"))))||\"\").includes(e)}window._clientSettings=Pt,Ft(\"prg-wx-nowebworker\")||(new dt).init(Pt,{disableWWConfig:Nt}),function(e){const t=[\"vendors\",\"microsoft\",\"common\"];e&&t.push(..._t),window.addEventListener(\"load\",(()=>{window._pageTimings.navType=Ot(),window.requestIdleCallback((()=>{const e=function(e){const t=window.weatherWebpackChunks;if(!t)return[...e,Tt];if(t.length>e.length)return[];t.forEach((t=>e.splice(e.indexOf(t[0][0]),1))),t.push===Array.prototype.push&&e.push(Tt);return e}(t);e.length&&e.forEach((e=>Rt(e)))}))}),{once:!0})}(),Ft(\"prg-wx-psdawc\")&&async function(){window.apntag=window.apntag||{anq:[]},window.apntag.anq=window.apntag.anq||[],await async function(e){if(!i())return;const{src:t,id:n,async:o=!0,container:s=document.head,retryNumber:r=0,retryDelayMs:a=0}=e;try{await async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:100;if(t<=1)return e();let i,o=1;do{try{i=await e()}catch(e){if(o===t)throw e&&e.message&&(e.message=`${e.message} after ${o} attempts`),e;await Wt(n)}}while(!i&&++o<=t);return i}((()=>function(e,t,n){let o=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];return new Promise(((s,r)=>{const a=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:\"anonymous\",s=arguments.length>4?arguments[4]:void 0,r=arguments.length>5?arguments[5]:void 0;if(!i())throw new Error(\"createScriptTag is unsupported server side. This call should be wrapped in canUseDOM().\");const a=document.createElement(\"script\");return t&&(a.id=t),s&&a.setAttribute(\"class\",s),r&&(a.onload=r),a.type=\"text/javascript\",e&&(window&&window.trustedTypesURLValidation?a.src=window.trustedTypesURLValidation(e,\"scriptSrcUrlPolicy\"):a.src=e,a.async=n,a.crossOrigin=o),window&&window.NONCE_ID&&(a.nonce=window.NONCE_ID),a}(e,t,o,void 0,void 0,(()=>s(!0)));a.crossOrigin=void 0,a.onerror=r,n.prepend(a)}))}(t,n,s,o)),r,a)}catch(e){qt(\"Failed to download Static JS\",11103,{scriptId:n,scriptSrc:t},It.Alert)}}({id:\"display-sdk\",src:\"https://adsdk.microsoft.com/ast/ast.js\",container:document.head,retryDelayMs:200,retryNumber:5})}()}();\r\n        </script>\r\n         <script nonce=\"\" src=\"https://assets.msn.cn/staticsb/statics/latest/oneTrustCN/1.1/scripttemplates/otSDKStub.js\" data-document-language=\"true\" type=\"text/javascript\" charset=\"UTF-8\" data-domain-script=\"42e7c639-42ed-4e0f-936c-e7f2a5fcb01e\" id=\"onetrust-sdk\"></script>\r\n\r\n\r\n<link rel=\"dns-prefetch\" href=\"//img-s-msn-com.akamaized.net\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//sb.scorecardresearch.com\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//th.bing.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//img-s-msn-com.akamaized.net\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//sb.scorecardresearch.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//th.bing.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//ecn.api.tiles.virtualearth.net\" crossorigin=\"anonymous\"><link rel=\"preconnect\" href=\"//ecn.api.tiles.virtualearth.net\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//t.ssl.ak.dynamic.tiles.virtualearth.net\" crossorigin=\"anonymous\"><link rel=\"preconnect\" href=\"//t.ssl.ak.dynamic.tiles.virtualearth.net\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//t.ssl.ak.tiles.virtualearth.net\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//api.msn.cn\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//assets.msn.cn\" crossorigin=\"anonymous\"><link rel=\"dns-prefetch\" href=\"//assets.msn.cn\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//c.msn.cn\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//api.msn.cn\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//assets.msn.cn\" crossorigin=\"anonymous\"><link rel=\"preconnect\" href=\"//assets.msn.cn\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//c.msn.cn\" crossorigin=\"use-credentials\">\r\n        <script type=\"text/javascript\" nonce=\"\">\r\n            if(matchMedia(\"(prefers-color-scheme:dark)\").matches){const s=document.createElement(\"style\");document.head.appendChild(s);s.type=\"text/css\";s.appendChild(document.createTextNode(\"body{background:#242424}\"));}\r\n        </script>\r\n\r\n\r\n\r\n\n        <meta name=\"description\" content=\"使用 MSN 天气 获取 Longquanyi-District 今天、晚上和明天的每小时准确预测，以及 10 天每日预测和天气雷达。随时了解降水、严重天气警告、空气质量和野火警报。\">\n<link rel=\"canonical\" href=\"https://www.msn.cn/zh-cn/weather/forecast/in-Longquanyi-District\"/>\n<meta property=og:url content=\"https://www.msn.cn/zh-cn/weather/forecast/in-Longquanyi-District\">\n        \n        </head>\r\n<body>\r\n    \r\n\r\n\r\n    <link rel=\"stylesheet\" href=\"/bundles/v1/weather/latest/SSR-service-entry.6d86847df0dd6cebc71b.css\" />\r\n\r\n<div id=\"root\" data-config-indexdocid=\"\">\r\n\n        \n        <script nonce>\n            \n        </script>\n        <style id=\"ssr-styles\">.svgRadarContainer-E1_1 {\n  position: absolute;\n  display: flex;\n  flex-wrap: wrap;\n  flex-direction: row;\n  align-content: flex-start;\n  opacity: 0;\n  pointer-events: none;\n  top: 0;\n  left: 0;\n}\n.svgRadarContainerVisible-E1_1 {\n  opacity: 0.7;\n}\n.svgRadarContainerVisibleAtBackground-E1_1 {\n  opacity: 1;\n}\n.svgRadarTile-E1_1 {\n  padding: 0;\n  margin: 0;\n  margin-top: -1px !important;\n  margin-left: -1px !important;\n}\n.svgRadarTileNoMargin-E1_1 {\n  padding: 0;\n  margin: 0;\n}\n.pane-E1_1 {\n  position: absolute;\n  display: block;\n  top: 0;\n  left: 0;\n  overflow: hidden;\n  width: 100%;\n  height: 100%;\n}\n.tileContainer-E1_1 {\n  position: absolute;\n  display: flex;\n  flex-wrap: wrap;\n  pointer-events: none;\n  direction: ltr;\n}\n.tile-E1_1 {\n  padding: 0;\n  margin: 0;\n  position: relative;\n}\n.tile-E1_1:focus {\n  outline: none;\n}\n.hideImageAltTagInFF:-moz-loading {\n  visibility: hidden;\n}\n.miniMapContainer-E1_1 {\n  position: relative;\n  width: 100%;\n  height: 210px;\n  border: 0px solid #003fee;\n  overflow: hidden;\n  cursor: pointer;\n  border-radius: 6px;\n  transform: translateZ(0);\n  box-sizing: border-box;\n}\n.miniMapContainer-E1_1 svg {\n}\n.miniMapContainer-E1_1:focus-within {\n  outline: 1px solid #2B2B2B;\n}\n@media (-ms-high-contrast:active) {\n  .miniMapContainer-E1_1 {\n    border: 1px solid highlight;\n  }\n}\n.miniMapContainer-E1_1.round {\n  border-radius: 16px;\n}\n.miniMapContainer-E1_1 [data-type='maskElement'] {\n  display: block;\n  position: absolute;\n  background: none;\n  border: none;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 5;\n  text-decoration: none;\n  curson: pointer;\n  pointer-events: all;\n}\n@media (-ms-high-contrast:active) {\n  .miniMapContainer-E1_1 svg {\n    opacity: 1;\n    fill: ButtonText !important;\n  }\n}\n.miniMapContainerLink-E1_1 {\n  position: relative;\n  display: block;\n  height: 100%;\n  width: 100%;\n  z-index: 1;\n}\n.miniMapButtonGroupContainer-E1_1 {\n  position: absolute;\n  width: 40px;\n  height: 180px;\n  border: 0px solid #003fee;\n  top: 12px;\n  left: 12px;\n  z-index: 5;\n}\n.miniMapStaticMap-E1_1 {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  height: 100%;\n  width: 100%;\n  object-fit: none;\n  object-position: center center;\n  transform: translate(-50%, -50%);\n  overflow: hidden;\n  pointer-events: none;\n  border-radius: initial;\n}\n.miniMapExpandContainer-E1_1 {\n  position: absolute;\n  display: inline-flex;\n  width: -webkit-fit-content;\n  height: 32px;\n  right: 16px;\n  bottom: 18px;\n  border-radius: 4px;\n  background: #FFFFFF;\n  cursor: pointer;\n  text-decoration: none;\n}\n.miniMapExpandContainer-E1_1:focus {\n  outline: none;\n}\nbody:not(.js-focus-visible) .miniMapExpandContainer-E1_1:focus, .js-focus-visible .miniMapExpandContainer-E1_1.focus-visible, .js-focus-visible [data-focus-visible-added].miniMapExpandContainer-E1_1 {\n  border: 1px solid;\n  border-color: rgb(16, 16, 16);\n}\n.miniMapExpandContainer-E1_1> span:first-child {\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  outline: none;\n}\n@media (-ms-high-contrast:active) {\n  .miniMapExpandContainer-E1_1 {\n    border: 1px solid highlight;\n  }\n  .miniMapExpandContainer-E1_1:hover, :hover > .miniMapExpandContainer-E1_1, .miniMapExpandContainer-E1_1:focus {\n    text-decoration: underline;\n  }\n}\n.miniMapExpandContainer-E1_1> span:first-child> span {\n}\n.miniMapExpandContainer-E1_1> span:first-child> span > svg {\n  margin: 8px;\n}\n.miniMapExpandTopRightContainer-E1_1 {\n  position: absolute;\n  cursor: pointer;\n  z-index: 3;\n  animation: delayFadeIn ease 0.8s;\n  text-decoration: none;\n  top: 12px;\n  right: 8px;\n  border-radius: 8px;\n  background: white;\n  display: flex;\n  flex-direction: row;\n  align-content: center;\n  justify-content: center;\n  align-items: center;\n  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);\n  width: 40px;\n  height: 40px;\n}\n.miniMapExpandTopRightContainer-E1_1:hover {\n  background: rgba(0, 0, 0, 0.3);\n}\n.miniMapExpandTopRightContainer-E1_1:focus {\n  outline: none;\n}\nbody:not(.js-focus-visible) .miniMapExpandTopRightContainer-E1_1:focus, .js-focus-visible .miniMapExpandTopRightContainer-E1_1.focus-visible, .js-focus-visible [data-focus-visible-added].miniMapExpandTopRightContainer-E1_1 {\n  border: 1px solid;\n  border-color: rgb(16, 16, 16);\n}\n@media (-ms-high-contrast:active) {\n  .miniMapExpandTopRightContainer-E1_1 {\n    border: 1px solid highlight;\n  }\n  .miniMapExpandTopRightContainer-E1_1:hover, :hover > .miniMapExpandTopRightContainer-E1_1, .miniMapExpandTopRightContainer-E1_1:focus {\n    text-decoration: underline;\n  }\n}\n.miniMapExpandTopRightContainer-E1_1> span {\n}\n.miniMapExpandTopRightContainer-E1_1> span > svg {\n  width: 14px;\n  height: 14px;\n  margin: 0px;\n  fill: black;\n}\n.miniMapExpandTopRightL1-E1_1 {\n  background: white !important;\n  right: unset;\n  left: 12px;\n  top: unset;\n  bottom: 57px;\n  backdrop-filter: blur(4px);\n}\n.miniMapExpandTopRightL1Precip-E1_1 {\n  bottom: 113px !important;\n}\n.miniMapExpandButton-E1_1 {\n  font-style: normal;\n  font-weight: 500;\n  font-size: 14px;\n  line-height: 32px;\n  text-align: center;\n  width: -webkit-fit-content;\n  color: #666666;\n  z-index: 1;\n  border-right: 1px solid #ECECEC;\n  padding-left: 10px;\n  padding-right: 10px;\n}\n.miniMapExpandButton-E1_1:hover {\n  color: #444444;\n}\n.miniMapMarker-E1_1 {\n  position: absolute;\n  cursor: pointer;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  pointer-events: none;\n  object-fit: cover;\n  box-sizing: border-box;\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  overflow: visible;\n  background: #ffd02e;\n  border: 3px solid #FFF;\n  box-shadow: 0px 0px 10px rgb(0 0 0 / 40%);\n  display: flex !important;\n  align-items: center;\n  justify-content: center;\n  z-index: 4;\n}\n.miniMapMarker-E1_1:before {\n  content: ' ';\n  display: block;\n  width: 12px;\n  height: 12px;\n  min-width: 12px;\n  border-radius: 50%;\n  box-shadow: 0px 0px 3px 3px #FFFC;\n  opacity: 0;\n}\n@keyframes locationPinBorder {\n  0% {\n    opacity: 0;\n  }\n  50% {\n    opacity: 0.8;\n  }\n  100% {\n    opacity: 0;\n  }\n}\n.timestampMask-E1_1 {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 20%;\n  pointer-events: none;\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 21.57%, rgba(196, 196, 196, 0) 88.24%);\n  z-index: 3;\n}\n.timestamp-E1_1 {\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  color: #FFFFFF;\n  opacity: 1;\n  line-height: 14px;\n  text-shadow: -1px -1px 0 rgb(0 0 0 / 45%), 1px -1px 0 rgb(0 0 0 / 45%), -1px 1px 0 rgb(0 0 0 / 45%), 1px 1px 0 rgb(0 0 0 / 45%);\n  z-index: 3;\n}\n.weatherFullMapButton-E1_1 {\n  z-index: 6;\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n  width: 30px;\n  height: 30px;\n  position: absolute;\n  top: 16px;\n  right: 16px;\n  border-radius: 8.92px;\n  backdrop-filter: blur(30px);\n  box-shadow: 0px 0.5px 3px 0px rgba(0, 0, 0, 0.2);\n  border: 1.12px solid rgba(255, 255, 255, 1);\n  background: rgba(248, 244, 242, 0.6);\n}\n.weatherFullMapButton-E1_1 > span {\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n}\n.weatherFullMapButton-E1_1 > span > svg {\n  fill: #3D3D3D;\n}\n.fallbackImage-E1_1 {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  position: absolute;\n}\n.simpleInfoPane-E1_1 {\n  position: absolute;\n  left: 16px;\n  bottom: 16px;\n  right: 16px;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 600;\n  padding: 10px 16px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  z-index: 5;\n  backdrop-filter: blur(30px);\n  white-space: nowrap;\n  word-break: keep-all;\n  color: #2B2B2B;\n}\n.simpleInfoPane-E1_1:before {\n  content: ' ';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: -1;\n  opacity: 0.9;\n  background-color: #FFFFFF;\n}\n.weatherMapMini_root-E1_1 {\n  margin: 0 auto;\n  position: relative;\n  color: #2B2B2B;\n}\n@media (max-width: 979px) {\n  .weatherMapMini_root-E1_1 {\n    width: 624px;\n  }\n}\n@media (min-width: 980px) and (max-width: 1303px) {\n  .weatherMapMini_root-E1_1 {\n    width: 948px;\n  }\n}\n@media (min-width: 1304px) and (max-width: 1627px) {\n  .weatherMapMini_root-E1_1 {\n    width: 1272px;\n  }\n}\n.weatherMapMini_root_mobile-E1_1 {\n  width: 100vw;\n  box-sizing: border-box;\n  padding: 0 16px;\n  margin-bottom: 16px;\n}\n@media (min-width: 656px) {\n  .weatherMapMini_root_mobile-E1_1 {\n  }\n  .weatherMapMini_root_mobile-E1_1 img[class*=miniMapStaticMap] {\n    object-fit: cover;\n  }\n}\n.marginBottom-E1_1 {\n  margin-bottom: -24px;\n}\n.marginNormal-E1_1 {\n  margin-top: -24px;\n}\n.mini_root_normal-E1_1 {\n  padding: 0px;\n  background: none;\n  box-shadow: none;\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n.mapCopilotTitle-E1_1 {\n  font-size: 20px;\n  line-height: 26px;\n  font-weight: 600;\n  color: var(--copilot-primary-font);\n  margin: 0 0 16px 0;\n}\n.weatherMapMiniDescLayout-E1_1 {\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(60px);\n  -webkit-backdrop-filter: blur(60px);\n  border-radius: 4px;\n  padding: 16px;\n  margin: 0 16px 16px;\n  width: calc(100vw - 32px);\n}\n.weatherMapMini_title-E1_1 {\n  color: #FFFFFF;\n  font-size: 14px;\n  line-height: 22px;\n  font-weight: 600;\n  text-transform: uppercase;\n  height: 22px;\n  margin-bottom: 12px;\n  display: none;\n}\n@media (max-width: 979px) {\n  .weatherMapMini_title-E1_1 {\n    display: block;\n  }\n}\n.weatherMapMini_common_title-E1_1 {\n  color: #FFFFFF;\n  font-size: 14px;\n  line-height: 22px;\n  font-weight: 600;\n  text-transform: uppercase;\n  height: 22px;\n  margin-bottom: 12px;\n  display: none;\n}\n@media (min-width: 980px) and (max-width: 1303px) {\n  .weatherMapMini_common_title-E1_1 {\n    display: block;\n  }\n}\n.bottomCard-E1_1 {\n  margin-top: 20px;\n  height: 168px;\n}\n.feedbackButtonWrapper-E1_1 {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  padding: 10px 12px;\n  backdrop-filter: blur(15px);\n  border: none;\n  cursor: pointer;\n  font-weight: 400;\n  font-size: 14px;\n  line-height: 14px;\n  font-family: inherit;\n  border-radius: 12px;\n  background: var(--feedback_button_background);\n  color: var(--feedback_label_color);\n}\n.feedbackButtonWrapper1U-E1_1 {\n  font-size: 12px;\n  line-height: 14px;\n  padding: 8px 6px 8px 9px;\n}\n.feedbackButtonLabel-E1_1 {\n  pointer-events: none;\n}\n.feedbackButtonIcon-E1_1 {\n  display: flex;\n  height: 16px;\n  width: 16px;\n  margin-right: 7px;\n  pointer-events: none;\n}\n.feedbackButtonIcon-E1_1 svg {\n}\n.feedbackButtonIcon-E1_1 svg path {\n  fill: var(--feedback_label_color);\n}\n.overallContainer-E1_1 {\n  padding-top: 16px;\n}\n.overallContainer-E1_1.showing1uBanner {\n  max-height: 254px;\n  overflow: hidden;\n}\n.labelContainerCompact-E1_1 {\n  margin-bottom: 8px;\n  width: 100%;\n}\n.size1uCard .labelContainerCompact-E1_1 {\n  margin-bottom: 0;\n  width: 50%;\n}\n.labelContainerMobile-E1_1 {\n  padding-left: 16px;\n  padding-right: 16px;\n  display: flex;\n  justify-content: space-between;\n}\n.labelWeather-E1_1 {\n  height: 22px;\n  font-size: 14px;\n  line-height: 22px;\n  font-weight: 600;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.labelUpdatetime-E1_1 {\n  height: 16px;\n  font-size: 12px;\n  line-height: 14px;\n  color: var(--label_color_2);\n  margin: 0;\n}\n.summaryContainerCompact-E1_1 {\n  padding-left: 16px;\n}\n.size1uCard .summaryContainerCompact-E1_1 {\n  height: 86px;\n  transition: margin 0.5s;\n}\n.summaryCardShowAnimation-E1_1 {\n  margin-top: 105px;\n}\n.summaryContainerMobile-E1_1 {\n  padding-left: 16px;\n  margin-bottom: 16px;\n}\n.summaryLineGroupCompact-E1_1 {\n  display: inline-flex;\n}\n.summaryIconAndTemperatureWrapper-E1_1 {\n  overflow-y: hidden;\n  position: relative;\n  overflow-x: visible;\n  overflow-y: clip;\n  height: 72px;\n  display: flex;\n  flex-direction: column;\n}\n.summaryIconAndTemperature-E1_1 {\n  display: inline-flex;\n}\n.summaryIconAndTemperature-E1_1[data-anim=scroll-in] {\n  animation: current-condition-scroll-in 0.5s linear forwards;\n}\n.summaryIconAndTemperature-E1_1[data-anim=scroll-out] {\n  animation: current-condition-scroll-out 0.5s linear forwards;\n}\n@keyframes current-condition-scroll-in {\n  0% {\n    opacity: 0;\n    transform: translate3d(0, 0%, 0);\n  }\n  100% {\n    opacity: 1;\n    transform: translate3d(0, -100%, 0);\n  }\n}\n@keyframes current-condition-scroll-out {\n  0% {\n    opacity: 1;\n    transform: translate3d(0, 0%, 0);\n  }\n  100% {\n    opacity: 0;\n    transform: translate3d(0, -100%, 0);\n  }\n}\n.summaryLineGroupMobile-E1_1 {\n  display: inline-flex;\n}\n.summaryLineGroupMobile-E1_1 .summaryIconAndTemperature-E1_1 {\n  align-items: center;\n}\n.summaryTemperatureCompact-E1_1 {\n  margin-left: 8px;\n  font-size: 64px;\n  line-height: 62px;\n  align-items: center;\n  direction: ltr;\n  display: flex;\n}\n.summaryTemperatureCompact-E1_1 > span {\n  direction: unset;\n}\n.summaryTemperatureIcon-E1_1 {\n}\n.summaryTemperatureHover-E1_1 {\n  text-decoration: none;\n  color: inherit;\n}\n.summaryTemperatureUnit-E1_1 {\n  height: 100%;\n  font-size: 34px;\n}\n.newSummaryLabel-E1_1 {\n  box-sizing: border-box;\n  text-transform: uppercase;\n  padding-right: 10px;\n}\n.newSummaryValue-E1_1 {\n  font-weight: bold;\n  margin-right: 5px;\n  box-sizing: border-box;\n}\n.newSummaryContainer-E1_1 {\n  max-width: 200px;\n  font-size: 14px;\n  position: absolute;\n  right: 10px;\n}\n.summaryCaptionAndFeelLikeContainerCompact-E1_1 {\n  margin-left: 32px;\n}\n.summaryCaptionAndFeelLikeContainerMobile-E1_1 {\n  margin-left: 20px;\n  display: flex;\n  flex-direction: column-reverse;\n  justify-content: center;\n  align-items: flex-start;\n  font-size: 12px;\n}\n.summaryFeelLikeContainerCompact-E1_1 {\n  display: flex;\n  height: 22px;\n  color: var(--label_color);\n  font-weight: normal;\n  align-items: center;\n  padding-top: 2px;\n}\n.summaryFeelLikeContainerCompact-E1_1>*:first-child {\n  font-size: 14px;\n  line-height: 16px;\n  opacity: 0.8;\n  margin-right: 12px;\n}\n.summaryFeelLikeContainerCompact-E1_1>*:last-child {\n  font-size: 14px;\n  line-height: 22px;\n}\n.summaryTemperatureMobile-E1_1 {\n  font-size: 56px;\n  margin-left: 20px;\n}\n.summaryTemperatureMobile-E1_1>span {\n  font-size: 30px;\n  position: relative;\n  bottom: 18px;\n}\n.summaryCaptionCompact-E1_1 {\n  display: inline-block;\n  font-size: 18px;\n  line-height: 24px;\n  font-weight: 600;\n  white-space: nowrap;\n  padding-top: 17px;\n}\n.summaryCaptionMobile-E1_1 {\n  padding-bottom: 4px;\n  font-weight: 600;\n  font-size: 16px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width:  150px;\n  white-space: nowrap;\n}\n.summaryDescContainer-E1_1 {\n  display: flex;\n  height: 65px;\n  align-items: center;\n  margin-bottom: 9px;\n}\n.weatherFeedbackEntryNewButton-E1_1 {\n  position: relative;\n  display: inline-block;\n  margin: 0 14px 0 8px;\n  vertical-align: middle;\n  z-index: 100;\n}\n.size1uCard .weatherFeedbackEntryNewButton-E1_1 {\n  margin: 0;\n}\n.summaryDescCompact-E1_1 {\n  font-size: 16px;\n  line-height: 22px;\n  font-weight: 350px;\n  margin: 0;\n}\n.summaryCurrentInfoSection-E1_1 {\n  display: flex;\n  box-sizing: border-box;\n  height: 100px;\n  margin-inline-start: -10px;\n  margin-bottom: 20px;\n}\n.summaryTempIconK-E1_1 {\n  display: block;\n  padding: 10px;\n  width: 80px;\n  height: 80px;\n  box-sizing: content-box;\n}\n.summaryCurCondition-E1_1 {\n  display: flex;\n  box-sizing: border-box;\n  flex-direction: column;\n  margin-inline-start: 12px;\n  padding: 6px 0;\n  height: 100px;\n}\n.summaryCurTempText-E1_1 {\n  display: flex;\n}\n.summaryCurTemperature-E1_1 {\n  font-size: 64px;\n  font-style: normal;\n  font-weight: 600;\n  line-height: 64px;\n  text-decoration: none;\n  color: inherit;\n}\n.summaryCurCondText-E1_1 {\n  margin-inline-start: 10px;\n  font-size: 32px;\n  font-style: normal;\n  font-weight: 550;\n  line-height: 48px;\n  max-width: 378px;\n  overflow: hidden;\n  white-space: pre;\n  text-overflow: ellipsis;\n}\n.summaryHlTempFeels-E1_1 {\n  display: flex;\n  font-size: 14px;\n  font-style: normal;\n  font-weight: 400;\n  line-height: 20px;\n  margin-top: 4px;\n}\n.summaryHlTempSection-E1_1 {\n  display: flex;\n}\n.summaryHighTemp-E1_1 {\n  display: flex;\n}\n.summaryHighTempLabel-E1_1 {\n  display: block;\n}\n.summaryHighTempText-E1_1 {\n  margin-inline-start: 4px;\n}\n.summaryLowTemp-E1_1 {\n  display: flex;\n  margin-inline-start: 8px;\n}\n.summaryLowTempLabel-E1_1 {\n  display: block;\n}\n.summaryLowTempText-E1_1 {\n  margin-inline-start: 4px;\n}\n.summaryFeelsLike-E1_1 {\n  margin-inline-start: 14px;\n  display: flex;\n  text-decoration: none;\n  color: inherit;\n}\n.summaryFeelsLike-E1_1:hover {\n  text-decoration: underline;\n}\n.summaryFeelsLabel-E1_1 {\n}\n.summaryFeelsValue-E1_1 {\n  margin-inline-start: 4px;\n}\n.detailContainerCompact-E1_1 {\n  padding-left: 16px;\n  margin-bottom: 16px;\n  padding-right: 16px;\n}\n.size1uCard .detailContainerCompact-E1_1 {\n  margin-top: 24px;\n}\n.detailItemLine-E1_1 {\n  display: grid;\n  grid-template-columns: repeat(3, 2fr);\n  grid-column-gap: 110px;\n  margin-bottom: 32px;\n}\n.detailItemLineCompact-E1_1 {\n  display: flex;\n  justify-content: space-between;\n  flex-wrap: wrap;\n}\n.size1uCard .detailItemLineCompact-E1_1 {\n  display: grid;\n  grid-template-columns: 74px 58px 58px 58px;\n  gap: 14px 8px;\n}\n.size1uCard .detailItemLineCompact-E1_1[data-windposition='1'] {\n  grid-template-columns: 58px 74px 58px 58px;\n}\n.size1uCard .detailItemLineCompact-E1_1[data-windposition='2'] {\n  grid-template-columns: 58px 58px 74px 58px;\n}\n.detailItemLineCompactMobile-E1_1 {\n  display: flex;\n  justify-content: space-between;\n  flex-wrap: wrap;\n  height: 48px;\n  max-width: 100%;\n  width: auto;\n  overflow: hidden;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='detailItemGridCompact'] {\n  margin-right: 10px;\n  height: 48px;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='aqiLabelHint'] {\n  position: static;\n  overflow: visible;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='detailLabelHint'] {\n  position: static;\n  overflow: visible;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='detailLabelHint'] div[class*='detailHintTips'] {\n  visibility: visible;\n  opacity: 1;\n  top: 20px;\n  transform: translateX(-50%);\n  left: 50%;\n  z-index: 901;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='aqiLabelHint'] p {\n  display: none;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='aqiLabelHint']:hover {\n}\n.detailItemLineCompactMobile-E1_1 div[class*='aqiLabelHint']:hover p {\n  top: 20px;\n  transform: translateX(-50%);\n  left: 50%;\n  display: block;\n  z-index: 20;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='detailItemGridCompact']>*:first-child {\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='detailItemGridCompact']>*:first-child>*:first-child {\n  line-height: 12px;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='detailItemGridCompact']>*:first-child>*:last-child {\n  line-height: 16px;\n  margin-bottom: 10px;\n}\n.modalMask-E1_1 {\n  position: fixed;\n  top: 0;\n  left: 0;\n  height: 100%;\n  width: 100%;\n  z-index: 900;\n}\n.detailItemGridCompact-E1_1 {\n  height: 38px;\n  display: inline-flex;\n}\n.detailItemGridCompact-E1_1 > span {\n  align-self: center;\n  margin: 7px 16px 7px 0;\n  height: 24px;\n}\n:lang(hu-hu) .detailItemGridCompact-E1_1:nth-child(n+6) {\n  display: none;\n}\n.detailItemGridCompact-E1_1:nth-child(n+9) {\n  display: none;\n}\n.size1uCard :lang(hu-hu) .detailItemGridCompact-E1_1:nth-child(n+6) {\n  display: inline-flex;\n  width: 100%;\n}\n.size1uCard :lang(hu-hu) .detailItemGridCompact-E1_1:nth-child(n+6) > a {\n  width: 100%;\n}\n.detailItemGroup-E1_1 {\n  white-space: nowrap;\n}\n.size1uCard .detailItemGroup-E1_1 {\n  width: 100%;\n}\n.detailItemGroup-E1_1>*:first-child {\n  font-size: 12px;\n  font-weight: normal;\n  line-height: 16px;\n  opacity: 0.8;\n}\n.detailItemGroup-E1_1>*:last-child {\n  font-size: 16px;\n  line-height: 22px;\n  font-weight: normal;\n}\n.detailItemGroup-E1_1>*:last-child > span {\n  direction: ltr;\n  display: inline-block;\n}\n.size1uCard .detailItemGroup-E1_1>*:last-child {\n  font-size: 14px;\n  line-height: 18px;\n  font-weight: 600;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.size1uCard .detailItemGroup-E1_1>*:last-child * {\n  display: inline;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.icon_indicator-E1_1 {\n  padding-left: 5px;\n}\n.icon_indicator-E1_1 > svg {\n  opacity: 1 !important;\n}\n.icon_indicator-E1_1 svg {\n}\n.icon_indicator-E1_1 svg path {\n  fill: var(--label_color);\n}\n.aqiDetailItemGroupCompact-E1_1 {\n}\n.size1uCard .aqiDetailItemGroupCompact-E1_1 {\n  width: 100%;\n}\n.aqiDetailItemGroupCompact-E1_1>*:first-child {\n  font-size: 12px;\n  font-weight: normal;\n  line-height: 16px;\n  opacity: 1;\n}\n.aqiDetailItemGroupCompact-E1_1>*:last-child {\n  font-size: 16px;\n  line-height: 22px;\n  font-weight: normal;\n}\n.size1uCard .aqiDetailItemGroupCompact-E1_1>*:last-child {\n  font-size: 14px;\n  line-height: 18px;\n  font-weight: 600;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.aqiDetailItemGroup-E1_1 {\n}\n.aqiDetailItemGroup-E1_1>*:first-child {\n  text-transform: uppercase;\n  font-size: 13px;\n  font-weight: normal;\n  line-height: 16px;\n  opacity: 1;\n}\n.aqiDetailItemGroup-E1_1>*:last-child {\n  font-size: 16px;\n  line-height: 20px;\n  font-weight: normal;\n}\n.aqiLabelHint-E1_1 {\n  position: relative;\n  display: block;\n  overflow: hidden;\n}\n.aqiLabelHint-E1_1:hover {\n  overflow: visible;\n}\n.aqiLabelHint-E1_1>span {\n  white-space: nowrap;\n  opacity: 0.8;\n}\n.aqiLabelHint-E1_1 p {\n  color: var(--hover_label_color);\n  background-color: var(--hover_background);\n  box-shadow: var(--hover_shadow_color);\n  pointer-events: none;\n  visibility: hidden;\n  width: max-content;\n  text-align: left;\n  border-radius: 6px;\n  padding: 8px;\n  position: absolute;\n  z-index: 9999;\n  top: 5px;\n  left: 80%;\n  opacity: 0;\n  transition: opacity 0.3s;\n  display: block;\n  overflow: hidden;\n  text-transform: none;\n  font-size: 12px;\n  line-height: 20px;\n}\n.aqiLabelHint-E1_1:after {\n}\n.aqiLabelHint-E1_1:after p {\n  content: ;\n  position: absolute;\n  top: 100%;\n  left: 50%;\n  margin-left: -5px;\n  border-width: 5px;\n  border-style: solid;\n  border-color: #555 transparent transparent transparent;\n}\n.aqiLabelHint-E1_1 p>span {\n  font-weight: 600;\n}\n.size1uCard .aqiLabelHint-E1_1>span {\n  display: block;\n}\n.size1uCard .aqiLabelHint-E1_1>span:first-child {\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.aqiLabelHint-E1_1:hover p {\n  visibility: visible;\n  opacity: 1;\n}\n.pollenValueContent-E1_1 {\n  display: block;\n  max-width: 160px;\n}\n.size1uCard .pollenValueContent-E1_1 {\n  display: flex;\n  align-items: center;\n}\n.size1uCard .pollenValueContent-E1_1>*:first-child {\n  display: block;\n}\n.size1uCard .pollenValueContent-E1_1>*:last-child {\n  display: block;\n  width: 44px;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n}\n.detailLabelHint-E1_1 {\n  position: relative;\n  display: block;;\n  opacity: 1 !important;\n  overflow: hidden;\n}\n.detailLabelHint-E1_1:hover {\n  overflow: visible;\n}\n.detailLabelHint-E1_1:hover .detailHintTips-E1_1 {\n  visibility: visible;\n  opacity: 1;\n}\n.detailLabelHint-E1_1>span {\n  opacity: 0.8;\n}\n.detailLabelHint-E1_1:after .detailHintTips-E1_1 {\n  content: ;\n  position: absolute;\n  top: 100%;\n  left: 50%;\n  margin-left: -5px;\n  border-width: 5px;\n  border-style: solid;\n  border-color: #555 transparent transparent transparent;\n}\n.size1uCard .detailLabelHint-E1_1>span {\n  display: block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.detailHintTips-E1_1 {\n  pointer-events: none;\n  visibility: hidden;\n  width: max-content;\n  height: auto;\n  background-color: var(--hover_background);\n  color: var(--hover_label_color);\n  text-align: left;\n  border-radius: 6px;\n  padding: 8px;\n  position: absolute;\n  z-index: 20;\n  top: 5px;\n  left: 80%;\n  opacity: 0;\n  transition: opacity 0.3s;\n  display: block;\n  overflow: hidden;\n  text-transform: none;\n  font-size: 12px;\n  line-height: 20px;\n  white-space: normal;\n  box-shadow: var(--hover_shadow_color);\n}\n.detailHintTips-E1_1 >span {\n  font-weight: 600;\n}\n.detailHintTips-E1_1 >div {\n  height: auto;\n  max-width: 200px;\n}\n.slugInfoSpace-E1_1 {\n  margin-left: 3px;\n  cursor: inherit;\n  display: inline-block;\n  width: 12px;\n  height: 12px;\n}\n.size1uCard .slugInfoSpace-E1_1 {\n  display: none !important;\n}\n.slugInfoSpace-E1_1 svg {\n}\n.slugInfoSpace-E1_1 svg path {\n  fill: var(--label_color);\n}\n@media (-ms-high-contrast:active) {\n  .slugInfoSpace-E1_1 svg path {\n    fill: ButtonText;\n  }\n}\n.aqiColorCycle-E1_1 {\n  opacity: 1 !important;\n}\n.aqiDetailItemGroupHoverCompact-E1_1 {\n  text-decoration: none;\n  color: var(--label_color);\n}\n.aqiDetailItemGroupHoverCompact-E1_1:hover {\n}\n.aqiDetailItemGroupHoverCompact-E1_1:hover>*:first-child {\n}\n.aqiDetailItemGroupHoverCompact-E1_1:hover>*:first-child> span {\n  text-decoration: underline;\n}\n.detailItemGroupHover-E1_1 {\n  text-decoration: none;\n  color: var(--label_color);\n}\n.detailItemGroupHover-E1_1:hover {\n}\n.detailItemGroupHover-E1_1:hover>*:first-child {\n  text-decoration: underline;\n}\n.weather_feedback_root-E1_1 {\n  position: relative;\n  display: block;\n}\n.linkMoreArrow-E1_1 {\n}\n.linkMoreArrow-E1_1:after {\n  content: '';\n  width: 5px;\n  height: 5px;\n  margin: 0 1px 1px;\n  display: inline-block;\n  border: rgba(255,255,255,0.8) solid;\n  border-width: 1px 1px 0 0;\n  transform: rotate(45deg);\n}\n.feedbackRow-E1_1 {\n  display: flex;\n  height: 36px;\n  width: 100%;\n  justify-content: flex-end;\n}\n.headerRow-E1_1 {\n  display: flex;\n  padding: 0 16px;\n  justify-content: space-between;\n}\n.weatherWatcherBannerWrapper-E1_1 {\n  display: flex;\n  box-sizing: border-box;\n  height: 44px;\n  margin: -4px 0 6px 0;\n  padding: 0 12px 0 12px;\n}\n.u1SummaryContainer-E1_1 {\n  display: flex;\n  align-items: center;\n}\n.u1SummaryTextContainer-E1_1 {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  margin-inline-start: 6px;\n}\n.u1TempUnitContainer-E1_1 {\n  display: flex;\n  align-items: flex-start;\n}\n.u1SummaryTemperatureCompact-E1_1 {\n  font-size: 48px;\n  line-height: 64px;\n  font-weight: 600;\n  direction: ltr;\n  text-decoration: none;\n  color: inherit;\n  display: flex;\n  align-items: flex-start;\n}\n.u1SummaryTemperatureCompact-E1_1 > span {\n  direction: unset;\n}\n.u1SummaryTemperatureUnit-E1_1 {\n  font-weight: 500;\n  font-size: 15px;\n  line-height: 20px;\n  padding-top: 14px;\n}\n.u1SummaryCaptionCompact-E1_1 {\n  display: block;\n  font-size: 18px;\n  line-height: 22px;\n  font-weight: 600;\n  white-space: nowrap;\n}\n.size1uCard .u1SummaryCaptionCompact-E1_1 {\n  max-width: 200px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.icon-E1_1 {\n  max-height: 100%;\n  max-width: 100%;\n}\n.inline-E1_1 {\n}\n.inline-E1_1.inline-E1_1 {\n  width: auto;\n  height: auto;\n}\n.weather_alerts_root-E1_1 {\n  display: flex;\n  align-items: center;\n  color: #FFFFFF;\n  font-size: 14px;\n  position: relative;\n  left: 369px;\n  margin-left: 35px;\n  flex: 1;\n  white-space: nowrap;\n}\n.weather_alerts_level-E1_1 {\n  display: flex;\n  align-items: center;\n  font-weight: 500;\n  line-height: 20px;\n  font-size: 12px;\n  padding: 5px 12px 5px 12px;\n  border-radius: 16px;\n  cursor: pointer;\n}\n.weather_alerts_level-E1_1 :first-child {\n  height: 16px;\n}\n.weather_alerts_default-E1_1 {\n  padding: 6px 12px;\n  border-radius: 16px;\n  display: flex;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.05);\n  backdrop-filter: blur(60px);\n  font-weight: 600;\n  line-height: 17px;\n  font-size: 14px;\n  color: #ffffff;\n}\n.weather_alerts_default-E1_1 path {\n  fill: #ffffff;\n}\n.weather_alerts_color_y-E1_1 {\n  background-color: #FFD02C;\n  color: #795700;\n}\n.weather_alerts_color_y-E1_1 svg {\n  fill: #795700;\n}\n.weather_alerts_color_o-E1_1 {\n  background-color: #E67E22;\n}\n.weather_alerts_color_o-E1_1 svg {\n  fill: #FFFFFF;\n}\n.weather_alerts_color_r-E1_1 {\n  background-color: #E74C3C;\n}\n.weather_alerts_color_r-E1_1 svg {\n  fill: #FFFFFF;\n}\n.weather_alerts_color_b-E1_1 {\n  background-color: #3967E4;\n}\n.weather_alerts_color_b-E1_1 svg {\n  fill: #FFFFFF;\n}\n.weather_alerts_logo-E1_1 {\n  margin-right: 5px;\n}\n.weather_alerts_title-E1_1 {\n  color: #FFFFFF;\n  margin: 0 12px 0 12px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.weather_alerts_link-E1_1 {\n  position: relative;\n  text-decoration: none;\n  height: 16px;\n  margin-bottom: 2px;\n  padding-bottom: 1px;\n}\n.weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1 {\n  color: #FFFFFF;\n}\n.weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1:hover, .weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1:link, .weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1:visited {\n  color: #FFFFFF;\n  border-bottom: 1px solid white;\n}\n.weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1:focus {\n  color: #FFFFFF;\n  outline: rgb(16, 16, 16) auto 1px;\n}\n.weather_alerts_anchor-E1_1 {\n  text-decoration: none;\n  color: inherit;\n}\n.location_name_set_home_icon-E1_1 {\n  width: 26px;\n  height: 26px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  padding: 0px;\n  margin: 0;\n  position: relative;\n  margin-left: 16px;\n}\n.location_name_set_home_icon-E1_1>*:first-child {\n  position: relative;\n  top: -0.5px;\n}\n.location_name_set_home_icon-E1_1>*:nth-child(2) {\n  display: none;\n}\n.location_name_set_home_icon-E1_1:focus {\n  outline: none;\n}\n.location_name_set_home_icon-E1_1:focus-visible {\n  outline: rgb(16, 16, 16) auto 1px;\n}\n.location_name_set_home_button-E1_1 {\n  position: relative;\n  display: flex;\n  flex-shrink: 0;\n  align-items: center;\n  justify-content: center;\n  background: transparent;\n  border: var(--button_border);\n  outline: none;\n  padding: 0;\n  width: 24px;\n  height: 24px;\n  cursor: pointer;\n  margin-inline-start: 16px;\n  border-radius: 50%;\n}\n.location_name_set_home_button-E1_1:hover {\n  background: rgba(255, 255, 255, 0.1);\n}\n.location_name_set_home_button-E1_1 svg > path {\n  fill: var(--button_color);\n}\n.location_icon-E1_1 {\n  margin-right: 11px;\n}\n@media screen and (forced-colors: active) and (prefers-color-scheme: dark) {\n  .location_icon-E1_1 {\n    fill: #FFF !important;\n  }\n}\n@media screen and (forced-colors: active) and (prefers-color-scheme: light) {\n  .location_icon-E1_1 {\n    fill: #000 !important;\n  }\n}\n.location_icon-E1_1 svg > path {\n  fill: var(--button_color);\n}\n.location_name_label-E1_1 {\n  color: var(--label_color);\n  line-height: 24px;\n  text-decoration: none;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.location_name_link-E1_1 {\n  cursor: inherit;\n}\n.location_name_link-E1_1:hover, .location_name_link-E1_1:active {\n  outline: none;\n}\n.location_name_link-E1_1:hover, .location_name_link-E1_1:focus, .location_name_link-E1_1:active {\n  text-decoration: underline;\n  text-underline-position: under;\n}\n.plain_text-E1_1 {\n  cursor: text !important;\n  text-decoration: none !important;\n}\n.location_name_main_button-E1_1 {\n  border: none;\n  cursor: pointer;\n  background: inherit;\n  color: unset;\n  padding: 0;\n}\n.location_name_detect_icon-E1_1 {\n  position: relative;\n  top: 1px;\n}\n.location_name_detect_icon-E1_1 svg > path {\n  fill: var(--label_color);\n}\n.location_name_container-E1_1 {\n  display: flex;\n  height: 24px;\n  position: relative;\n  align-items: center;\n  max-width: 612px;\n  cursor: pointer;\n  overflow: hidden;\n}\n.location_name_container-E1_1 path {\n}\n.location_name_container-E1_1 rect {\n}\n.location_name_container-E1_1[data-theme='dark'] {\n  --label_color: #fff;\n  --button_border: 1px solid rgba(255, 255, 255, 0.8);\n  --button_border_2: #fff solid;\n  --button_color: rgba(255, 255, 255, 0.8);\n}\n.location_name_container-E1_1[data-theme='light'] {\n  --label_color: #000;\n  --button_border: 1px solid rgba(0, 0, 0, 0.8);\n  --button_border_2: #000 solid;\n  --button_color: rgba(0, 0, 0, 0.8);\n}\n@media(prefers-color-scheme: dark) {\n  .location_name_container-E1_1 {\n  }\n  .location_name_container-E1_1[data-theme='light'] {\n    --label_color: #fff;\n    --button_border: 1px solid rgba(255, 255, 255, 0.8);\n    --button_border_2: #fff solid;\n    --button_color: rgba(255, 255, 255, 0.8);\n  }\n}\n@media (-ms-high-contrast:active) {\n  .location_name_container-E1_1 rect {\n    stroke: ButtonText;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .location_name_container-E1_1 path {\n    fill: ButtonText !important;\n  }\n}\n.location_change_button-E1_1 {\n  width: 9px;\n  height: 9px;\n  display: block;\n  margin-top: -4px;\n  border: var(--button_border_2);\n  border-width: 1px 1px 0 0;\n  transform: rotate(135deg);\n}\n.location_change_button_container-E1_1 {\n  margin-left: 8px;\n  width: 15px;\n}\n.weather_alerts_root-E1_2 {\n  left: unset;\n  right: unset;\n}\n:focus-visible {\n  outline: rgb(16, 16, 16) auto 1px;\n}\n#ForecastDays {\n  z-index: 1;\n}\n.weatherOverview_root-E1_1 {\n  border-radius: 6px;\n  position: relative;\n  width: 620px;\n  height: 310px;\n  color: var(--label_color);\n}\n.weatherOverview_root-E1_1.size1uCard {\n  width: 300px;\n}\n.weatherOverview_root-E1_1[data-theme='dark'] {\n  --container_background: rgba(255, 255, 255, 0.05);\n  --label_color: #FFF;\n  --label_color_2: rgba(255,255,255,0.7);\n  --feedback_button_background: rgba(255, 255, 255, 0.09);\n  --feedback_label_color: rgba(184, 209, 255, 1);\n  --hover_label_color: rgba(255, 255, 255, 1);\n  --hover_background: rgba(41, 41, 41, 1);\n  --hover_shadow_color: 0px 0px 2px 0px rgba(0, 0, 0, 0.12), 0px 8px 16px 0px rgba(0, 0, 0, 0.14);\n  --card_border_color_k: rgba(255, 255, 255, 0.05);\n}\n.weatherOverview_root-E1_1[data-theme='light'] {\n  --container_background: rgba(255, 255, 255, 0.8);\n  --label_color: #000;\n  --label_color_2: rgba(0, 0, 0, 0.7);\n  --feedback_button_background: rgba(0, 0, 0, 0.03);\n  --feedback_label_color: rgba(43, 80, 243, 1);\n  --hover_label_color: rgba(68, 68, 68, 1);\n  --hover_background: rgba(255, 255, 255, 1);\n  --hover_shadow_color: 0px 0px 2px 0px rgba(0, 0, 0, 0.12), 0px 8px 16px 0px rgba(0, 0, 0, 0.14);\n  --card_border_color_k: rgba(0, 0, 0, 0.1);\n}\n@media(prefers-color-scheme: dark) {\n  .weatherOverview_root-E1_1 {\n  }\n  .weatherOverview_root-E1_1[data-theme='light'] {\n    --container_background: rgba(255, 255, 255, 0.05);\n    --label_color: #FFF;\n    --label_color_2: rgba(255,255,255,0.7);\n    --feedback_button_background: rgba(255, 255, 255, 0.09);\n    --feedback_label_color: rgba(184, 209, 255, 1);\n    --hover_label_color: rgba(255, 255, 255, 1);\n    --hover_background: rgba(41, 41, 41, 1);\n    --hover_shadow_color: 0px 0px 2px 0px rgba(0, 0, 0, 0.12), 0px 8px 16px 0px rgba(0, 0, 0, 0.14);\n    --card_border_color_k: rgba(255, 255, 255, 0.05);\n  }\n}\n.locationNameContainer-E1_1 {\n  display: flex;\n  height: 24px;\n  position: relative;\n  width: var(--weather-container-width);\n  margin-bottom: 16px;\n}\n.backgroundContainerCompact-E1_1 {\n  height: 270px;\n  border-radius: 6px;\n  background-color: var(--container_background);\n}\n.backgroundContainerFull-E1_1 {\n  height: 620px;\n  border-radius: 6px;\n}\n.weatherSsrLayoutContainer-E1_1 {\n  display: grid;\n  gap: 20px;\n  align-items: end;\n}\n@media (max-width: 979px) {\n  .weatherSsrLayoutContainer-E1_1 {\n    grid-template-columns: 1fr;\n    grid-template-rows: 310px 270px;\n  }\n}\n@media (min-width: 980px) and (max-width: 1303px) {\n  .weatherSsrLayoutContainer-E1_1 {\n    grid-template-columns: 1fr 1fr 1fr;\n    grid-template-rows: auto;\n  }\n}\n@media (min-width: 1304px) and (max-width: 1627px) {\n  .weatherSsrLayoutContainer-E1_1 {\n    grid-template-columns: 1fr 1fr;\n    grid-template-rows: auto;\n  }\n}\n@media (min-width: 1628px) {\n  .weatherSsrLayoutContainer-E1_1 {\n    grid-template-columns: 1fr 1fr;\n    grid-template-rows: auto;\n  }\n}\n.weatherOverviewV2Container-E1_1 {\n  grid-area: 1 / 1 / 2 / 2;\n}\n.weatherMapMiniContainer-E1_1 {\n}\n@media (max-width: 979px) {\n  .weatherMapMiniContainer-E1_1 {\n    grid-area: 2 / 1 / 3 / 2;\n  }\n}\n@media (min-width: 980px) and (max-width: 1303px) {\n  .weatherMapMiniContainer-E1_1 {\n    grid-area: 1 / 2 / 2 / 3;\n  }\n}\n@media (min-width: 1304px) and (max-width: 1627px) {\n  .weatherMapMiniContainer-E1_1 {\n    grid-area: 1 / 2 / 2 / 3;\n  }\n}\n@media (min-width: 1628px) {\n  .weatherMapMiniContainer-E1_1 {\n    grid-area: 1 / 2 / 2 / 3;\n  }\n}\n.aboveFolderContainer-E1_1 {\n  position: relative;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.00);\n}\n#weatherhomepage .aboveFolderContainer-E1_1 {\n  --customized-native-ad-card-fill-color: #FFFFFF;\n  --customized-native-ad-card-secondary-color: #FFFFFF;\n  --customized-native-ad-card-text-color: #000000;\n  --customized-native-ad-card-bg: #FFFFFF;\n}\n#weatherhomepage [class*='newsTitleNightMode'] .aboveFolderContainer-E1_1 {\n  --customized-native-ad-card-fill-color: #565656;\n  --customized-native-ad-card-secondary-color: #565656;\n  --customized-native-ad-card-text-color: #FFFFFF;\n  --customized-native-ad-card-bg: rgba(255, 255, 255, 0.08);\n}\n.scrollForFourColsForwards-E1_1 {\n  -webkit-animation: scroll 1s ease forwards 1;\n  animation: scrollForwards 1s ease forwards 1;\n}\n@keyframes scrollForwards {\n  0% {\n    -webkit-transform: translateX(0);\n    transform: translateX(0);\n  }\n  100% {\n    -webkit-transform: translateX(-300px);\n    transform: translateX(-300px);\n  }\n}\n.scrollForFourColsBackwards-E1_1 {\n  -webkit-animation: scrollBackWards 1s ease forwards 1;\n  animation: scrollBackWards 1s ease forwards 1;\n}\n@keyframes scrollBackWards {\n  0% {\n    -webkit-transform: translateX(-300px);\n    transform: translateX(-300px);\n  }\n  100% {\n    -webkit-transform: translateX(0);\n    transform: translateX(0);\n  }\n}\n.chevronBtn-E1_1 {\n  position: absolute;\n  z-index: 1;\n  padding: 12px;\n  top: 292px;\n  right: -23px;\n  cursor: pointer;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  display: inline-flex;\n  justify-content: flex-end;\n  align-items: center;\n  height: 48px;\n  width: 48px;\n  border-radius: 24px;\n  border: 0.5px solid var(--Stroke-Neutral-Grey, rgba(0, 0, 0, 0.10));\n  background: var(--Background-Special-Button, #FFF);\n  box-shadow:  0px 0px 2px 0px rgba(0, 0, 0, 0.12), 0px 2px 4px 0px rgba(0, 0, 0, 0.14);\n}\n.chevronBtn-E1_1:focus-visible {\n  outline: -webkit-focus-ring-color auto 1px;\n}\n@media (-ms-high-contrast:active) {\n  .chevronBtn-E1_1 {\n    border: 1px solid Highlight;\n  }\n}\n.bannerAdsContainer-E1_1 {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  margin-top: 32px;\n  height: 104px !important;\n}\n.weatherTeaserLayoutContainer-E1_1 {\n  display: grid;\n  gap: 72px;\n  align-items: end;\n  grid-template-columns: 1fr;\n  grid-template-rows: auto auto;\n}\n.unitContainerSSR-E1_1 {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n  font-size: 20px;\n  line-height: 23px;\n  color: #FFFFFF;\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n  cursor: pointer;\n  position: relative;\n}\n.unitTextSSR-E1_1 {\n  background: none;\n  border: none;\n  padding: 0;\n  font-size: 18px;\n  line-height: 21px;\n  color: #FFFFFF;\n  cursor: pointer;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  border-radius: 4px;\n  justify-content: space-evenly;\n  align-items: center;\n}\n.triangleSSR-E1_1 {\n  width: 7px;\n  display: flex;\n}\n.textField-E1_1 {\n  font-family: inherit;\n  box-sizing: border-box;\n  margin: 0;\n  transition: all 0.2s ease-in-out;\n  font-size: 14px;\n  line-height: 20px;\n  font-weight: 400;\n  background: #FFFFFF;\n  border: 1px solid #BEBEBE;\n  color: #2B2B2B;\n  padding: 0 11px;\n  border-radius: 2px;\n  height: 32px;\n}\n.textField-E1_1:hover:enabled {\n  background: #FFFFFF;\n  border-color: #979797;\n}\n.textField-E1_1:active:enabled {\n  background: #FFFFFF;\n  border-color: #D6D6D6;\n}\n.textField-E1_1:focus:enabled {\n  outline: none;\n  box-shadow: 0 0 0 1px #888888 inset;\n  border-color: #888888;\n}\n.textField-E1_1:disabled {\n  cursor: not-allowed !important;\n  opacity: 0.3;\n}\n.textField-E1_1::placeholder {\n  color: #767676;\n}\n@media (-ms-high-contrast:active) {\n  .textField-E1_1 {\n    -ms-high-contrast-adjust: none;\n    background: ButtonFace;\n    border-color: ButtonText;\n    color: ButtonText;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .textField-E1_1::placeholder {\n    color: GrayText;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .textField-E1_1:disabled {\n    opacity: 1;\n    background: ButtonFace !important;\n    color: GrayText !important;\n    fill: GrayText !important;\n    border-color: GrayText !important;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .textField-E1_1:focus:enabled {\n    border-color: ButtonText;\n    box-shadow: 0 0 0 1px inset ButtonText;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .textField-E1_1:hover:enabled {\n    background: ButtonFace;\n    border: 1px solid Highlight;\n  }\n}\n.textField__filled-E1_1 {\n  font-family: inherit;\n  box-sizing: border-box;\n  margin: 0;\n  transition: all 0.2s ease-in-out;\n  font-size: 14px;\n  line-height: 20px;\n  font-weight: 400;\n  background: #EDEDED;\n  border: 1px solid transparent;\n  color: #2B2B2B;\n  padding: 0 11px;\n  border-radius: 2px;\n}\n.textField__filled-E1_1:hover:enabled {\n  border-color: transparent;\n  background: #E5E5E5;\n}\n.textField__filled-E1_1:active:enabled {\n  border-color: transparent;\n}\n.textField__filled-E1_1:focus:enabled {\n  border-color: #888888;\n}\n.textField__filled-E1_1:disabled {\n  cursor: not-allowed !important;\n  opacity: 0.3;\n}\n.textField__filled-E1_1::placeholder {\n  color: #6B6B6B;\n}\n@media (-ms-high-contrast:active) {\n  .textField__filled-E1_1 {\n    -ms-high-contrast-adjust: none;\n    background: ButtonFace;\n    border-color: ButtonText;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .textField__filled-E1_1::placeholder {\n    color: GrayText;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .textField__filled-E1_1:disabled {\n    opacity: 1;\n    background: ButtonFace !important;\n    color: GrayText !important;\n    fill: GrayText !important;\n    border-color: GrayText !important;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .textField__filled-E1_1:hover:enabled {\n    background: ButtonFace;\n    border: 1px solid Highlight;\n  }\n}\n.suggestionContainer-E1_1 {\n  display: flex;\n  flex-direction: row;\n  margin-left: 5px;\n}\n.suggestions-E1_1 {\n  position: absolute;\n  left: 3px;\n  z-index: 100;\n  background: #FFFFFF;\n  border: 1px solid #E5E5E5;\n  width: 200px;\n  display: block;\n  height: auto;\n  overflow-y: auto;\n  margin-top: 32px;\n  border-bottom-left-radius: 16px;\n  border-bottom-right-radius: 16px;\n  padding-left: 4px;\n}\n.suggestions-E1_1 > li {\n  padding: 2px;\n  list-style: none;\n  display: block;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  overflow: hidden;\n  text-align: left;\n  font-size: 14px;\n  line-height: 20px;\n}\n.suggestions-E1_1 > li:hover, .suggestions-E1_1 > li:focus {\n  cursor: pointer;\n  background: #f1f1f1;\n}\n.suggestions-E1_1 > li > button {\n  text-align: left;\n  height: auto!important;\n  background: transparent;\n  color: unset;\n  width: 100%;\n  margin: 0;\n  padding: 0;\n  cursor: pointer;\n  display: flex;\n  justify-content: space-between;\n}\n.suggestionActive-E1_1 {\n  background-color: #f1f1f1;\n}\n@media (-ms-high-contrast:active) {\n  .suggestionActive-E1_1 {\n    outline: 1px solid;\n  }\n}\n.toolTipContainer-E1_1 {\n  align-items: center;\n  padding: 6px 8px 8px 8px;\n  position: absolute;\n  width: 100%;\n  top: 100%;\n  margin-top: 4px;\n  background: #E0E0E0;\n  box-sizing: border-box;\n  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);\n  border-radius: 6px;\n}\n.firstLineToolTip-E1_1 {\n  font-weight: 500;\n  font-size: 12px;\n  word-wrap: break-word;\n  line-height: 16px;\n}\n.secondLineToolTip-E1_1 {\n  font-size: 12px;\n  word-wrap: break-word;\n  line-height: 14px;\n}\n.suggestion-E1_1 {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n.suggestionText-E1_1 {\n  display: flex;\n  flex-direction: column;\n  width: calc(100% - 30px);\n}\n.firstLine-E1_1 {\n  font-size: 16px;\n  line-height: 22px;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  overflow: hidden;\n  font-weight: 500;\n  display: block;\n}\n.secondLine-E1_1 {\n  font-size: 14px;\n  font-weight: 400;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n  line-height: 22px;\n  display: block;\n}\n.searchIcon-E1_1 {\n  border: none;\n  outline: none;\n  border-radius: 4px !important;\n  padding: 0px !important;\n  background: inherit;\n  min-width: 16px;\n  width: 16px !important;\n  margin-right: 20px !important;\n}\n.searchIcon-E1_1> span:nth-child(1) {\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  outline: none;\n}\n.searchIcon-E1_1:focus {\n  outline: none;\n}\n.searchIcon-E1_1:focus-visible {\n  outline: rgb(16, 16, 16) auto 1px;\n}\n.clearAllIconContainer-E1_1 {\n  border: none;\n  outline: none;\n  cursor: pointer;\n  padding: 0px !important;\n  background: none;\n  height: 12px;\n  width: 15px;\n  margin-right: 10px !important;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n.clearAllIconContainer-E1_1:focus {\n  outline: none;\n}\n.clearAllIconContainer-E1_1:focus-visible {\n  border-radius: 4px !important;\n  outline: rgb(16, 16, 16) auto 1px;\n}\n.clearAllIconSvg-E1_1 {\n  height: 12px;\n  width: 15px;\n}\n.clearAllIconSvg-E1_1 path {\n  border: none;\n  fill: black;\n  display: inline-block;\n  height: 12px;\n  width: 15px;\n  font-size: 12px;\n}\n@media (forced-colors: active) and (prefers-color-scheme: dark) {\n  .clearAllIconSvg-E1_1 {\n  }\n  .clearAllIconSvg-E1_1 path {\n    fill: #FFF;\n  }\n}\n@media (forced-colors: active) and (prefers-color-scheme: light) {\n  .clearAllIconSvg-E1_1 {\n  }\n  .clearAllIconSvg-E1_1 path {\n    fill: #000;\n  }\n}\n.textField-E1_2 {\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  border: 0px;\n  background: #FFFFFF;\n  width: 100%;\n  height: 40px;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n  border-radius: 0px;\n  border-top: solid 1px #9F9F9F;\n  border-bottom: solid 1px #9F9F9F;\n}\n.textField-E1_2:hover:enabled {\n  border: 0px;\n  border-top: solid 1px #9F9F9F;\n  border-bottom: solid 1px #9F9F9F;\n  box-shadow: none;\n  webkit-box-shadow: none;\n  moz-box-shadow: none;\n}\n.textField-E1_2:active:enabled {\n  border: 0px;\n  border-top: solid 1px #9F9F9F;\n  border-bottom: solid 1px #9F9F9F;\n  background: #FFFFFF;\n  box-shadow: none;\n  webkit-box-shadow: none;\n  moz-box-shadow: none;\n}\n.textField-E1_2:focus:enabled {\n  box-shadow: none;\n  border: 0px;\n  border-top: solid 1px #9F9F9F;\n  border-bottom: solid 1px #9F9F9F;\n  background: #FFFFFF;\n  webkit-box-shadow: none;\n  moz-box-shadow: none;\n}\n.textField-E1_2::placeholder {\n  color: black;\n}\n.wls_root-E1_1 {\n  position: relative;\n}\n.searchRoot-E1_1 {\n  border-radius: 6px;\n  padding: 15px;\n  border: 0px solid;\n  font-size: 14px;\n  line-height: 20px;\n  position: relative;\n  color: #2B2B2B;\n}\n.searchRootV2-E1_1 {\n  font-size: 14px;\n  line-height: 20px;\n  position: relative;\n  overflow: hidden;\n  color: #2B2B2B;\n}\n.searchRoot_onlybox-E1_1 {\n  border-radius: 6px;\n  padding: 0;\n  border: 0px solid;\n  font-size: 14px;\n  line-height: 20px;\n  position: relative;\n  color: #2B2B2B;\n}\n.inputBox-E1_1 {\n  position: relative;\n  display: flex;\n}\n.suggestionContainer-E1_2 {\n  right: 0px;\n  padding-right: 10px;\n  border: 1px solid var(--card_border);\n  border-radius: 12px;\n  box-sizing: border-box;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n  width: 281px;\n  box-shadow: none;\n  color: unset;\n  backdrop-filter: none;\n  background-color: var(--background_color);\n  border-color: var(--card_border);\n  margin-left: 0px !important;\n  flex-wrap: wrap;\n  padding: 0;\n}\n.suggestionContainer-E1_2 input, .suggestionContainer-E1_2 input:focus:enabled, .suggestionContainer-E1_2 input:hover:enabled, .suggestionContainer-E1_2 input:active:enabled {\n  border-top: none;\n  border-bottom: none;\n  width: calc(100% - 69px);\n  height: 40px;\n  margin-left: 6px;\n}\n.suggestionContainer-E1_2 img {\n  border-top: none;\n  border-bottom: none;\n  height: 16px;\n  margin: 7px 0px 7px 0px;\n}\n.suggestionContainer-E1_2 button {\n  height: 32px;\n  margin-right: 20px;\n  border: none;\n}\n.weather-L1 .suggestionContainer-E1_2 input {\n  background: none !important;\n  padding: 0 10px;\n  font-size: 14px;\n}\n.suggestionContainer-E1_2 li[role='option']:hover {\n}\n.suggestionContainer-E1_2 div[class*='searchIcon'] svg>path {\n  fill: #919191;\n}\n.suggestionContainer-E1_2 > div {\n  width: 100%;\n}\n.suggestionContainer-E1_2 input {\n  background: none !important;\n  padding: 0 10px;\n  color: var(--location_color)!important;\n}\n@media (-ms-high-contrast:active) {\n  .suggestionContainer-E1_2 input {\n    -ms-high-contrast-adjust: auto;\n  }\n}\n.suggestionContainer-E1_2 input::placeholder {\n  color: var(--location_color);\n}\n.suggestionContainer-E1_2 input::-ms-input-placeholder {\n  color: var(--location_color);\n}\n@media (-ms-high-contrast:active) {\n  .suggestionContainer-E1_2 li[role='option']:hover {\n    outline: #FFFFFF auto 1px;\n  }\n}\n.suggestions-E1_2 {\n  background-color: var(--popup_background);\n  width: 281px;\n  left: -17px;\n}\n.suggestions-E1_2 {\n  position: relative;\n  margin: 0;\n  border: none;\n  border-top: 1px solid var(--card_border);\n  padding: 10px 0px;\n  width: 100%;\n  border-bottom-left-radius: 12px;\n  border-bottom-right-radius: 12px;\n  left: 0px;\n}\n.suggestions-E1_2 div[class*='noResultContainer'] div {\n  color: var(--popup_location_color);\n}\n.suggestions-E1_2 li {\n  padding: 6px 16px;\n}\n.suggestions-E1_2 > li:hover, .suggestions-E1_2 > li:focus {\n  cursor: pointer;\n  background: var(--popup_item_hover);\n}\n.suggestions-E1_2 li button {\n  color: var(--popup_location_color);\n}\n.suggestions-E1_2 li path {\n  fill: var(--popup_location_color);\n}\n.suggestions-E1_2 li rect {\n}\n@media (-ms-high-contrast:active) {\n  .suggestions-E1_2 li rect {\n    stroke: #919191;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .suggestions-E1_2 li path {\n    fill: #919191;\n  }\n}\n.suggestionActive-E1_2 {\n  background: var(--popup_item_hover);\n}\n@media (-ms-high-contrast:active) {\n  .suggestionActive-E1_2 {\n    border: 1px solid;\n  }\n}\n.background-E1_1 {\n  background: rgba(255, 255, 255, 0.08);\n}\n.backgroundHP20-E1_1 {\n  display: flex;\n  align-items: center;\n  height: 72px;\n}\n.backgroundHP20-E1_1[data-theme='dark'] {\n  --container_background_active: rgba(255, 255, 255, 0.15);\n  --container_background: rgba(255, 255, 255, 0.05);\n  --theme_base_10: rgba(255, 255, 255, 1);\n  --theme_base_05: rgba(255, 255, 255, 0.1);\n  --location_color: rgba(255, 255, 255, 0.9);\n  --card_border: 1px solid rgba(250, 249, 248, 0.15);\n  --popup_background: #292929;\n  --popup_item_hover: rgba(255, 255, 255, 0.15);\n}\n.backgroundHP20-E1_1[data-theme='light'] {\n  --theme_base_10: rgba(0, 0, 0, 1);\n  --theme_base_05: rgba(0, 0, 0, 0.05);\n  --container_background: rgba(255, 255, 255, 0.6);\n  --container_background_active: rgba(255, 255, 255, 1);\n  --location_color: #323130;\n  --card_border: none;\n  --popup_background: #fff;\n  --popup_item_hover: #EFEFEF;\n}\n@media(prefers-color-scheme: dark) {\n  .backgroundHP20-E1_1 {\n    background: rgba(0, 0, 0, 0.36);\n  }\n  .backgroundHP20-E1_1[data-theme='light'] {\n    --container_background_active: rgba(255, 255, 255, 0.15);\n    --container_background: rgba(255, 255, 255, 0.05);\n    --theme_base_10: rgba(255, 255, 255, 1);\n    --theme_base_05: rgba(255, 255, 255, 0.1);\n    --location_color: rgba(255, 255, 255, 0.9);\n    --card_border: 1px solid rgba(250, 249, 248, 0.15);\n    --popup_background: #292929;\n    --popup_item_hover: rgba(255, 255, 255, 0.15);\n  }\n}\n.backgroundFilter-E1_1 {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  backdrop-filter: blur(12px);\n  -webkit-backdrop-filter: blur(12px);\n}\n.scrollContainer-E1_1 {\n  display: flex;\n  flex: 1 1 auto;\n  align-items: stretch;\n  width: 100%;\n}\n.scrollContainer-E1_1 button[class*='temperatureButton'] {\n  forced-color-adjust: none;\n}\ndiv[class^='locationSearchContainer'] + .scrollContainer-E1_1 {\n  width: 924px;\n}\n@media (min-width: 656px) and (max-width: 979px) {\n  div[class^='locationSearchContainer'] + .scrollContainer-E1_1 {\n    width: 300px;\n  }\n}\n@media (min-width: 980px) and (max-width: 1303px) {\n  div[class^='locationSearchContainer'] + .scrollContainer-E1_1 {\n    width: 612px;\n  }\n}\n.scrollContainerHP20-E1_1 {\n  height: 40px;\n}\n.scrollContainerHP20-E1_1 div[class*='unitContainerSSR'] {\n  background: var(--container_background);\n  border-radius: 12px;\n  font-size: 20px;\n  line-height: 23px;\n  width: 36px;\n  height: 36px;\n}\n.scrollContainerHP20-E1_1 div[class*='unitContainerSSR'] button[class*='unitTextSSR'] {\n  color: var(--theme_base_10);\n}\n.scrollContainerHP20-E1_1 div[class*='unitContainerSSR'] path {\n  fill: var(--theme_base_10);\n}\n.cardContainer-E1_1 {\n  display: flex;\n  list-style: none;\n  white-space: nowrap;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  height: 48px;\n}\n.carouselmaskContainer-E1_1 {\n  position: absolute;\n  display: none;\n  width: 100px;\n  height: 48px;\n  top: 0;\n  bottom: 0;\n  right: 104px;\n  pointer-events: none;\n  transform: rotate(0);\n}\n.carouselmaskContainer-E1_1.notToScrollEnd {\n  display: block;\n}\n.carouselmaskContent-E1_1 {\n  position: relative;\n  display: block;\n  width: 100px;\n  height: 48px;\n  background: linear-gradient(270deg, rgba(255, 255, 255, 0.08) 0%, transparent 100%);\n}\n.chevron-E1_1 {\n  bottom: 50%;\n  fill: white;\n  z-index: 100;\n  background: transparent;\n  border: none;\n  padding: 0;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n}\n.chevron-E1_1> span:nth-child(1) {\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n  outline: none;\n}\n.chevron-E1_1 span[class^='chevronIcon'] {\n  width: 24px;\n  height: 24px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 50%;\n  padding: 0;\n}\n.chevron-E1_1:focus {\n  outline: none;\n}\n.chevron-E1_1:focus-visible {\n  outline: rgb(16, 16, 16) auto 1px;\n}\n.chevron-E1_1 path {\n  fill: #fff;\n}\n@media (-ms-high-contrast:active) {\n  .chevron-E1_1 path {\n    fill: ButtonText !important;\n  }\n}\n.chevron-E1_1 span[class^='chevronIcon'] svg {\n  width: 8px;\n  position: relative;\n}\n.chevronHP20-E1_1 {\n  top: 0px;\n}\n.chevronHP20-E1_1 span[class^='chevronIcon'] {\n  background: none;\n}\n.chevronHP20-E1_1 path {\n  fill: var(--theme_base_10);\n}\n@media (-ms-high-contrast:active) {\n  .chevronHP20-E1_1 path {\n    fill: ButtonText !important;\n  }\n}\n.chevronHP20-E1_1 span[class^='chevronIcon']:hover {\n  background: rgba(255, 255, 255, 0.1);\n}\n.chevron__left-E1_1 {\n  position: relative;\n}\n.chevron__left-E1_1 svg {\n  transform: rotate(180deg) scale(1.5);\n  right: 0px;\n}\n@media (max-width: 655px) {\n  .chevron__left-E1_1 {\n    display: none;\n  }\n}\n.chevronLeftCover-E1_1 {\n  position: absolute;\n  width: 31px;\n  height: 100%;\n  left: 0;\n  background: linear-gradient(to right, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0));\n  top: 0;\n}\n.chevron__right-E1_1 {\n  position: relative;\n  margin-left: 5px;\n}\n.chevron__right-E1_1 svg {\n  transform: scale(1.5);\n  right: -1px;\n}\n@media (max-width: 655px) {\n  .chevron__right-E1_1 {\n    display: none;\n  }\n}\n.chevronRightCover-E1_1 {\n  position: absolute;\n  width: 31px;\n  height: 100%;\n  right: 0;\n  background: linear-gradient(to left, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0));\n  top: 0;\n}\n.chevronIcon-E1_1 {\n  padding: 2px;\n  cursor: pointer;\n}\n.chevronIcon-E1_1:hover {\n  background: rgba(255,255,255,.4);\n}\n.chevronIcon-E1_1:focus {\n  outline: none;\n}\n.disabledIcon-E1_1 {\n  cursor: default;\n}\n.disabledIcon-E1_1:hover {\n  background: none !important;\n}\n.cdBorder-E1_1 {\n  border-left: 1px solid rgba(255, 255, 255, 0.2);\n}\n.cdBorder-E1_1 li[class^=popular] {\n  padding-left: 8px;\n}\n.cardItem-E1_1 {\n  border-right: 1px solid rgba(255, 255, 255, 0.2);\n  box-sizing: border-box;\n  flex-shrink: 0;\n}\n.cardItemHP20-E1_1 {\n  margin-left: 8px;\n  box-sizing: border-box;\n  flex-shrink: 0;\n  height: 40px;\n}\n.disabled-E1_1 {\n  opacity: 0.5;\n  outline: none !important;\n}\n.ulContainer-E1_1 {\n  width: 0;\n  flex: 1 1 auto;\n  position: relative;\n}\n.contentContainer-E1_1 {\n  margin: auto;\n  position: relative;\n  height: 48px;\n  display: flex;\n  align-items: center;\n}\n@media (max-width: 655px) {\n  .contentContainer-E1_1 {\n    width: 300px;\n  }\n}\n@media (min-width: 656px) and (max-width: 979px) {\n  .contentContainer-E1_1 {\n    width: 620px;\n  }\n}\n@media (min-width: 980px) and (max-width: 1303px) {\n  .contentContainer-E1_1 {\n    width: 940px;\n  }\n}\n@media (min-width: 1304px) and (max-width: 1627px) {\n  .contentContainer-E1_1 {\n    width: 1260px;\n  }\n}\n@media (min-width: 1628px) {\n  .contentContainer-E1_1 {\n    width: 1580px;\n  }\n}\n.contentContainerHP20-E1_1 {\n  height: 40px;\n}\n.locationSearchContainer-E1_1 {\n  width: 300px;\n  height: 32px;\n}\n.locationSearchContainer-E1_1 div[class^='searchRootV2'] {\n  background: none;\n  overflow: visible;\n}\n.locationSearchContainer-E1_1 #m-locs:not(.white) div[class^='suggestionContainer'] {\n  box-shadow:  0px 1px 3px rgb(0 0 0 / 14%);\n  background-color: rgba(255, 255, 255, 0.15);\n}\n.locationSearchContainer-E1_1 #m-locs:not(.white) div[class^='suggestionContainer'] > input {\n  color: #fff;\n}\n.locationSearchContainer-E1_1 #m-locs:not(.white) div[class^='suggestionContainer'] input::-webkit-input-placeholder {\n  color: #fff;\n  font-size: 14px;\n}\n.locationSearchContainer-E1_1 #m-locs:not(.white) div[class^='suggestionContainer'] input::-ms-input-placeholder {\n  color: #fff;\n  font-size: 14px;\n}\n.locationSearchContainer-E1_1 #m-locs:not(.white) div[class^='suggestionContainer'] input::-moz-placeholder {\n  color: #fff;\n  font-size: 14px;\n}\n.locationSearchContainer-E1_1 #m-locs:not(.white) div[class^='suggestionContainer'] input:-moz-placeholder {\n  color: #fff;\n  font-size: 14px;\n}\n.locationSearchContainer-E1_1 #m-locs:not(.white) div[class^='suggestionContainer'] button[class^='searchIcon'] {\n  background: none;\n}\n.locationSearchContainer-E1_1 #m-locs:not(.white) div[class^='suggestionContainer'] svg[class^='clearAllIconSvg'] path {\n  fill: rgb(245,245,245);\n}\n.locationSearchContainer-E1_1 \n</tool_result>", "reasoningContent": "", "id": "b9be901c-f8ea-4ed5-8e9e-180f63945dcd", "metadata": {}}, {"role": "user", "applyStatus": 0, "content": null, "promptContent": "[use_mcp_tool for 'github.com/zcaceres/fetch-mcp'] Result:\n\n        \n            <!DOCTYPE html>\r\n<html lang=\"zh-cn\" dir=\"ltr\" >\r\n<head data-info=\"f:msnallexpusers,prg-sp-liveapi,prg-con-adrpt,prg-1sw-sains1c1,prg-1sw-sarfmrt3,prg-1sw-savfetv4gt2,prg-1sw-sal3bs29cc,routesportsexp,prg-adspeek,1s-fcrypt,prg-upsaip-w1-t,1s-rpssecautht,prg-pr2-optreload,preprg-health-cfstaging,ads-xdr-pgbreako-c,cprg-ad-pgbreak-c,chatn_v2_t1,1s-ntf2-emdskpoc,1s-ntf2-emdskp,1s-ntf2-emdskpo,1s-ntf2-pjdup,prg-pr2-ntf-rctr,prg-sh-rmitmlnk,nopinglancecardit,prg-evt-gaokao,prg-cmsnav-zhcn,prg-cg-notf-landing-c,ads-nopostsq-t,ads-nopostsq,fv-spt-staging,1s-uasdisf-t,ads-usepme,ads-anjson-migt,sh-bdvid,prg-sh-bd-video,fv-gaming-stagc,ads-bing-kiwiho,ads-nooutbrain,release-outlook-app,ads-prcrid-bi,prg-cg-ab-testing,prg-1sw-tvid-t20,1s-p1-vid-vs,prg-1sw-tvid-p1,prg-1sw-videopb,prg-1sw-videosxap,prg-p1-txt2,prg-p1-uc3,prg-pr1-videos,prg-tv-api,prg-tv-segcap10,prg-vid-cd,prg-vid-trdcache,prg-1sw-reclaim2,prg-1sw-reclaim,1s-p2-bg-appanon,ads-bcn-cndomain,prg-cg-int-ad-pod,prg-1sw-tvid-t20-vp,1s-p2-vid-vp,prg-1sw-tvid-p2,prg-p2-txt2,prg-p2-uc3,prg-pr2-setdur-t2,prg-pr2-videos,prg-pr2-wtab-oo,prg-tv-p2-api,prg-tv-p2-segcap10,clarity-ios-t,msph-benchmark,prg-pr2-lifecyclebac,1s-routetoolsexpc,prg-1sw-sa-dnec,prg-1sw-crypinf,prg-wx-nsever,prg-1sw-cryptren,prg-cg-1pqq3,msph-adsrevpd,prg-cg-cfzhcnfx,prg-cg-lstfix,prg-cg-game-exp-12,prg-cg-game-exp-2,prg-1sw-tbrfltr,prg-1sw-tvid-int-t1,1s-p1-vid-vs-int,prg-1sw-tvid-p1-int,prg-1sw-tvid-int-t2,1s-p2-vid-vp-int,prg-1sw-tvid-p2-int,prg-fin-rmar,1s-vid-dwt,prg-vdw-enable,prg-pr2-wwidgets-t,bing_native_chat_t,prg-1s-dwvid-wpo,1s-wpo-ntp-videos,1s-newsfeed-worknews,bing_uni_iab_t,prg-pr2-imghttd-t,prg-pr2-imghtdd-t,prg-1sw-wxaqifct,prg-wx-btide,prg-wx-ntide,prg-cg-countermonitor,prg-1sw-safitopo,prg-1sw-sasptopo,prg-1sw-newe2e,1s-ls-uppermuid-c,prg-1sw-wxaqepp,prg-1sw-tv-p2-expand,prg-tv-p2-expand,prg-1sw-tv-c,prg-1sw-wxaqoff,prg-wx-saqi0,prg-wx-saqi,prg-wx-nhroll,1s-ntf1-wxcld10s,1s-ntf2-wxall20s,1s-wxmixcache,prg-1sw-wxncvf,prg-1sw-wxtrlog,prg-cg-rps5,prg-1sw-p2trvid3,prg-pr2-watch-pivot,prg-ad-dv-on,prg-ad-infopane-dv,msph-follv2,prg-video-n2v0,prg-pr2-spscorexp,prg-pr2-pscorexp,1s-ntf2-pradasimiv2,1s-ntf1-pradasimiv2,prg-pr2-ntf-rel,prg-1sw-pawpor1,prg-1sw-spawpor1,prg-1sw-dwvid-wpo-2,1s-wpo-ntp-videos2,prg-1sw-sfexpdat,1s-ntfa-fpmeta,prg-1sw-analysisdata,prg-1sw-cs-rank,1s-cs-video,prg-fin-ruby,ads-3pdmn-filter,ads-3pctve-filter,prg-1sw-wxdtdeprev,1s-uup-bps-c,prg-cg-1p3dm3,prg-1sw-ntrim,prg-1sw-wxwtnyrev,ads-xdr-tstb-c,prg-cg-recopop,prg-1sw-l3v1ship,1s-wpo-pr1-layout3wv1,prg-1sw-version-fix,cg-lgc-endpoint-ctr,prg-1sw-l3v2,1s-wpo-pr1-layout3wv2;\" data-canvas-info=\"f:prg-con-adrpt,prg-wx-nsever,prg-1sw-wxaqifct,prg-wx-ntide,prg-wx-saqi,prg-wx-nhroll\" data-ads-numberline=\"f:anaheimmuidadsux1=ads-xdr-pgbreako-c,5qos=ads-nopostsq-t,5sov=ads-usepme,5ui9=ads-anjson-migt,5xs8=ads-bing-kiwiho,63hy=ads-nooutbrain,649x=ads-prcrid-bi,6965=ads-bcn-cndomain,7khf=ads-3pdmn-filter,7khg=ads-3pctve-filter,7mq4=ads-xdr-tstb-c;\" data-client-settings=\"{&quot;aid&quot;:&quot;84B5281AF66B46C59CAEE4A5427258DD&quot;, &quot;fd_muid&quot;:&quot;0614EA13297C6EF33B0EFC1F28526F35&quot;, &quot;static_page&quot;:&quot;false&quot;, &quot;queryparams&quot;:&quot;?apiversion=v2&amp;domshim=1&amp;noservercache=1&amp;batchservertelemetry=1&amp;noservertelemetry=1&amp;reqsrc=rs&quot;, &quot;apptype&quot;:&quot;weather&quot;, &quot;pagetype&quot;:&quot;weather::weathertoday&quot;, &quot;configRootUrl&quot;:&quot;https://assets.msn.com/config/v1/&quot;, &quot;feedBaseDomain&quot;:&quot;&quot;, &quot;deviceFormFactor&quot;:&quot;desktop&quot;, &quot;pageGenTime&quot;:&quot;2025-06-15T03:12:56Z&quot;, &quot;pcsInfo&quot;:{&quot;v&quot;:&quot;********.5_master&quot;, &quot;env&quot;:&quot;prod-ssr&quot;}, &quot;locale&quot;:{&quot;language&quot;:&quot;zh&quot;, &quot;market&quot;:&quot;cn&quot;, &quot;content&quot;:{&quot;language&quot;:&quot;zh&quot;, &quot;market&quot;:&quot;cn&quot;}, &quot;display&quot;:{&quot;language&quot;:&quot;zh&quot;, &quot;market&quot;:&quot;cn&quot;}}, &quot;featureFlags&quot;:{}, &quot;market&quot;:{ &quot;requestInput&quot;:{ &quot;language&quot;:&quot;zh&quot;,&quot;region&quot;:&quot;cn&quot;, &quot;geo_country&quot;:&quot;CN&quot;}, &quot;detection&quot;:{ &quot;type&quot;:&quot;compliance&quot;,&quot;geo_country&quot;:&quot;&quot;,&quot;geo_countrycf&quot;:&quot;&quot;, &quot;recommendLanguageCoachmark&quot;: &quot;false&quot;}}, &quot;servicesEndpoints&quot;:{&quot;staticConfig&quot;: {&quot;domain&quot;: &quot;https://assets.msn.cn&quot;, &quot;path&quot;: &quot;/config/v3/&quot;, &quot;v&quot;: &quot;&quot;},&quot;crs&quot;: {&quot;domain&quot;: &quot;https://assets.msn.cn&quot;, &quot;path&quot;: &quot;/resolver/api/resolve/&quot;, &quot;v&quot;: &quot;v3&quot;},&quot;feedService&quot;: {&quot;domain&quot;: &quot;https://assets.msn.cn&quot;, &quot;path&quot;: &quot;&quot;, &quot;v&quot;: &quot;&quot;},&quot;enterpriseFeedService&quot;: {&quot;domain&quot;: &quot;https://ent-api.msn.com/&quot;, &quot;path&quot;: &quot;&quot;, &quot;v&quot;: &quot;&quot;}}, &quot;bundleInfo&quot;:{&quot;v&quot;:&quot;20250612.401&quot;, &quot;alias&quot;:&quot;latest&quot;}, &quot;os&quot;:&quot;windows&quot;, &quot;browser&quot;:{&quot;browserType&quot;:&quot;chrome&quot;, &quot;version&quot;:&quot;120&quot;, &quot;ismobile&quot;:&quot;false&quot;}, &quot;verticalKey&quot;:&quot;weather&quot;, &quot;categoryKey&quot;:&quot;weathertoday&quot;, &quot;domain&quot;:&quot;www.msn.cn&quot;, &quot;requestTrailInfo&quot;:{&quot;flightVersion&quot;:&quot;53531360&quot;}, &quot;detection&quot;:{&quot;scrubbedIp&quot;:&quot;************&quot;, &quot;tier&quot;:&quot;p&quot;}, &quot;geo_country&quot;:&quot;CN&quot;, &quot;geo_countryname&quot;:&quot;China&quot;, &quot;geo_subdivision&quot;:&quot;Sichuan&quot;, &quot;geo_zip&quot;:&quot;610100&quot;, &quot;geo_city&quot;:&quot;Longquanyi District&quot;, &quot;geo_lat&quot;:&quot;30.5694&quot;, &quot;geo_long&quot;:&quot;104.2805&quot;, &quot;geo_citycf&quot;:&quot;9&quot;, &quot;geo_countrycf&quot;:&quot;9&quot;, &quot;requestMuid&quot;:&quot;notFound&quot;}\" data-diagnostics=\"\">\r\n    <meta charset=\"utf-8\" />\r\n    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\r\n\r\n    \r\n    \r\n\r\n    \n            <title>Longquanyi-District 天气预报 | MSN 天气</title>\n            \r\n\r\n    \r\n    \r\n\r\n\r\n    \r\n    \r\n\r\n    \r\n\r\n    <script type=\"text/javascript\" nonce=\"\">\r\n        try {\r\n            window._pageTimings = { TTJSStart: Math.round(performance.now()) };\r\n            window._clientSettings = JSON.parse(document.head.dataset.clientSettings);\r\n        } catch (e) {\r\n            console.error(\"Error in adding TTJSStart marker\");\r\n        }\r\n    </script>\r\n\r\n        <script nonce=\"\">\r\n            function getCookieConsentRequired(){return true;}\r\n            window._ssrServiceEntryUrl=\"/bundles/v1/weather/latest/SSR-service-entry.9b1edb9fcd1484488d20.js\";\r\n        </script>\r\n\r\n\r\n        <script type=\"text/javascript\" id=\"inlinehead-inline-script\" nonce=\"\">\r\n            window[\"_webWorkerBundle\"] = \"/bundles/v1/weather/latest/web-worker.bd2f2a96271a41433e49.js\";\r\n            window[\"_authCookieName\"] = \"ace\";\r\n            !function(){\"use strict\";var e={},t={};function n(i){var o=t[i];if(void 0!==o)return o.exports;var s=t[i]={exports:{}};return e[i](s,s.exports,n),s.exports}function i(){return!(\"undefined\"==typeof window||!window.document||!window.document.createElement||window.isRenderServiceEnv)}n.g=function(){if(\"object\"==typeof globalThis)return globalThis;try{return this||new Function(\"return this\")()}catch(e){if(\"object\"==typeof window)return window}}();let o=new Map;const s=Object.freeze({set(e,t){return o.set(e,t),this},get:function(e,t){let n=o.get(e);return void 0===n&&t&&(n=t(),o.set(e,n)),n},clear(){o.clear()},delete:e=>o.delete(e),has:e=>o.has(e)});const r=\"__RequestDataInstance__\";class a{constructor(e,t){if(this.url=new URL(e.href),this.innerHeight=e.innerHeight,this.devicePixelRatio=e.devicePixelRatio,this.canUseCssGrid=e.canUseCssGrid,this.requestId=e.requestId,this.cookie=e.cookie,this.referer=e.referer,this.userAgent=e.userAgent,this.clientData=e.clientData,this.vpCetoToken=e.vpCetoToken,this.oneServiceHeaders=d(e.oneServiceHeaders)||{},this.isPssrMode=t,t){const e=c(\"OSATE\",this.cookie),t=!!e&&\"1\"===e,n=c(\"OSAT\",this.cookie);if(t&&n||!e&&!n)return this.msalAuthReady=!0,void(n&&(this.oneServiceHeaders.Authorization=`Bearer ${n}`));this.msalAuthReady=!1,this.pssrRejectedReason=e&&!t?\"interactiveLogin\":e&&!n?\"missOSAT\":\"missOSATE\"}}static getInstance(){const e=s.get(r);return e||(n.g.TEST_ENV?l({href:\"http://localhost:8080/\",innerHeight:768,devicePixelRatio:1,canUseCssGrid:!1,requestId:\"0\",cookie:\"\",userAgent:\"\",referer:\"\",oneServiceHeaders:\"\",vpCetoToken:\"\"}):l({href:\"http://localhost:8080/\",innerHeight:0,devicePixelRatio:0,canUseCssGrid:!1,requestId:\"0\",cookie:\"\",userAgent:\"\",referer:\"\",oneServiceHeaders:\"\",vpCetoToken:\"\"}))}static resetInstance(e){const t=new a(e,arguments.length>1&&void 0!==arguments[1]&&arguments[1]);return s.set(r,t),t}}function c(e,t){if(t&&e){const n=new RegExp(\"\\\\b\"+e+\"\\\\s*=\\\\s*([^;]*)\",\"i\").exec(t);return n&&n.length>1?n[1]:null}return null}function d(e){try{if(e)return JSON.parse(e)}catch(e){}}const l=a.resetInstance,u=()=>a.getInstance();function h(){return i()?window.location.search:u().url.search}var g,p;!function(e){e.Anon=\"ANON\",e.AppAnon=\"APP_ANON\",e.Unknown=\"\"}(g||(g={})),function(e){e.AnonCookieExists=\"ace\",e.AppAnonCookieExists=\"aace\"}(p||(p={}));const m=\"child\";function w(e,t){if(e){const n=new RegExp(\"\\\\b\"+t+\"\\\\s*=\\\\s*([^;]*)\",\"i\").exec(e);return n&&n.length>1?n[1]:null}return null}function f(e){try{if(e)return JSON.parse(e)}catch(e){}return null}const v=\"prg-pr2-\",b=\"prg-pw-\",y=\"prg-\",S=\"prg-1sw-\";function C(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:y;const n=t.length,i=[];return e&&e.length&&(e.forEach((e=>{const o=e&&e.trim();o&&o.length>=n&&o.substring(0,n).toLowerCase()===t&&i.push(o.toLowerCase())})),i.sort()),i}function k(e){let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:\"\",n=y;var i,o;return arguments.length>1&&void 0!==arguments[1]&&arguments[1]?n=b:t.toLocaleLowerCase()===\"windowsShell\".toLocaleLowerCase()||t.toLocaleLowerCase()===\"windowsShellV2\".toLocaleLowerCase()?n=S:t.toLocaleLowerCase()!==\"winWidgets\".toLocaleLowerCase()||(null===(i=window)||void 0===i||null===(i=i.location)||void 0===i||null===(i=i.search)||void 0===i?void 0:i.includes(\"reqsrc=vp\"))||null!==(o=window)&&void 0!==o&&null!==(o=o.location)&&void 0!==o&&null!==(o=o.search)&&void 0!==o&&o.includes(\"vptest\")||(n=v),n}function I(e){var t;return document.head.dataset[e]||(null===(t=q())||void 0===t?void 0:t.dataset[e])}let A;function q(){return void 0===A&&(A=document.getElementsByClassName(\"peregrine-widget-settings\")[0]||null),A}function T(e){var t;if(!e||!e.startsWith(\"f:\"))return;const n=(e.match(/f:\\s*([^;]+)/i)||[])[1];return n?null===(t=n.toLowerCase().split(\",\"))||void 0===t?void 0:t.sort():[]}const _=\"Authorization\";var E,x;!function(e){e.Presentation=\"presentation\"}(E||(E={})),function(e){e.Unknown=\"Unknown\",e.Portrait=\"Portrait\",e.Landscape=\"Landscape\"}(x||(x={}));var R,O;!function(e){e[e.Undefined=0]=\"Undefined\",e[e.Basic=1]=\"Basic\",e[e.Advanced=2]=\"Advanced\",e[e.Premium=3]=\"Premium\"}(R||(R={})),function(e){e.Init=\"init\",e.Config=\"config\",e.Targeted=\"targeted\",e.Sticky=\"sticky\",e.NoSticky=\"no_sticky\",e.Admin=\"admin\",e.Forced=\"forced\",e.Manual=\"manual\"}(O||(O={}));const L=new Set([\"finance-app-win\",\"weather-app-win\",\"winpstoreapp\"]),W=new Set([\"hpmsn\"]);new Set([\"chromentpnews\"]);function N(){var e;return i()?(window._clientSettings=window._clientSettings||f(document.head.dataset.clientSettings||(null===(e=document.getElementsByClassName(\"peregrine-widget-settings\")[0])||void 0===e?void 0:e.getAttribute(\"data-client-settings\"))),window._clientSettings):null}const P=\"feed/personalize/settings\";function F(e,t){const n=t.replace(/[[\\]]/g,\"\\\\$&\"),i=new RegExp(\"[?&]\"+n+\"(=([^&#]*)|&|#|$)\").exec(e);if(!i)return null;const o=i[2];return decodeURIComponent(o.replace(/\\+/g,\" \"))||\"\"}let D=new class{constructor(){i()?(this.isDebugEnabled=(F(window.location.href,\"debug\")||\"\").toLowerCase()in{1:1,true:1}||\"vp\"===(F(window.location.href,\"reqsrc\")||\"\").toLowerCase(),this.isDebugEnabled&&!n.g.TEST_ENV&&(window.webpackRequire=n)):this.isDebugEnabled=!1}getLoggingService(){return null}isDebug(){return this.isDebugEnabled}setDebug(e){this.isDebugEnabled=e}setTags(e){}log(e,t){this.isDebug()&&console.info(e)}logError(e){console.error(e)}logCallback(e){this.isDebug()&&console.info(e())}logObjects(){this.isDebug()&&console.log(...arguments)}logSingleObject(e,t){this.isDebug()&&console.log(e)}};class M{get supported(){return!!this.storage}constructor(e){this.storage=e}getItem(e){if(this.supported)return this.storage.getItem(e)}getObject(e,t){const n=this.getItem(e);if(null!=n){const e=f(n);if(null!=e)return e}return t}key(e){if(this.supported&&e>=0)return this.storage.key(e)}keys(){return this.supported?Object.keys(this.storage):[]}setObject(e,t){void 0!==t?this.setItem(e,JSON.stringify(t)):this.removeItem(e)}removeObject(e){const t=this.removeItem(e);if(null!=t)return f(t)}setItem(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(this.supported)try{if(!t)throw\"Attempted to store null/undefined value: \"+t;this.storage.setItem(e,t)}catch(e){if(!n)throw e;D.logError(e)}else if(!n)throw new Error(\"WebStorage not supported\")}get length(){if(this.supported)return this.storage.length}removeItem(e){if(this.supported){const t=this.getItem(e);return this.storage.removeItem(e),t}}clear(){this.supported&&this.storage.clear()}removeSubstringKeys(e){if(!this.supported||!e)return;const t=[];for(let n=0;n<this.storage.length;n++){const i=this.key(n);i&&i.includes(e)&&t.push(i)}for(let e=0;e<t.length;e++)this.removeItem(t[e])}}let H=new class extends M{constructor(){let e=null;if(i())try{e=localStorage}catch(e){}super(e)}};const U=\"1s-tokens\";var $;let j,B,J;function V(){return j=G(),j&&j.accessToken?j:void 0}function G(){return j=j||void 0===j&&H.getObject(U)||null,j}function z(e){try{return decodeURIComponent(e)}catch(e){}}function K(e,t){if(!e)return null;if(t)return function(e){var t;const n=null===(t=document.cookie)||void 0===t?void 0:t.match(`\\\\b${e}=([^;]*)`);return n?z(n[1]):null}(e);if(!J){const e=\"undefined\"!=typeof document&&document.cookie.split(\"; \");J={};const t=e&&e.length;for(let n=0;n<t;n++){const t=e[n].indexOf(\"=\");J[e[n].slice(0,t).toLocaleLowerCase()]=z(e[n].slice(t+1))}}const n=e.toLocaleLowerCase();return J[n]||null}function X(e){const t=K(e);return t&&encodeURIComponent(t)||null}function Q(e){let t,n=!1;const i=function(){return n||(n=!0,t=e()),t};return i.cache={clear:()=>{n=!1,t=void 0}},i}!function(e){e.NotAvailable=\"notAvailable\",e.Expired=\"expired\",e.Valid=\"valid\"}($||($={}));const Y=\"currentaccount\",Z=Q((()=>{const e=K(Y,!0),t=e&&f(e),{login_hint:n}=t||{};return n&&t})),ee=Q((()=>!!K(Y)));function te(){try{return localStorage}catch(e){return null}}const ne=\"__PageExperimentInstance__\";function ie(e){s.set(ne,e)}function oe(e){if(!s.has(ne)&&i()){var t;ie(se(document.head.dataset.info||(null===(t=window.document.getElementsByClassName(\"peregrine-widget-settings\")[0])||void 0===t||null===(t=t.dataset)||void 0===t?void 0:t.info)||\"\"))}const n=s.get(ne);return n&&n.has(e)}function se(e){const t=(e||\"\").replace(/(^f:|;.*$)/g,\"\").split(\",\");return new Set(t)}const re=\"uxlogin\",ae=\"uxlogout\",ce=\"uxedit\",de=\"useRedirect\",le=\"uxswitch\",ue=Q((()=>{const e=te();return e&&\"1\"===e.getItem(re)||i()&&location.search&&location.search.includes(`${re}=1`)}));const he=Q((()=>{const e=function(){try{return sessionStorage}catch(e){return null}}();return e&&!!e.getItem(ae)}));const ge=Q((()=>{const e=te();return e&&\"1\"===e.getItem(ce)}));const pe=Q((()=>{const e=te();return!oe(\"prg-noredirect\")&&e&&\"1\"===e.getItem(de)&&function(){const{browser:e}=window._clientSettings||{},{browserType:t=\"\"}=e||{};return!!/safari/i.test(t)}()}));const me=Q((()=>{const e=te();return e&&\"1\"===e.getItem(le)}));const we=\"OnOOutlookMobile\",fe=\"OnOStartApp\",ve=\"xmweather-\",be=\"launcherntp\",ye=\"launcherInterests\",Se=\"swiftKey\",Ce=\"superappdhp\",ke=\"_ocidIsomorphicKey\",Ie=()=>(s.get(ke)||s.set(ke,He(\"ocid\").toLowerCase()),s.get(ke)),Ae=\"_platformFlagKey\",qe=\"_hostAppNameKey\",Te=\"_clientTypeKey\",_e=()=>(s.get(Ae)||s.set(Ae,He(\"chpltfm\")),s.get(Ae)),Ee=()=>{if(!s.get(qe)){const e=_e();s.set(qe,e.toLowerCase().split(\"-\")[0])}return s.get(qe)},xe=()=>{if(!s.get(Te)){const e=_e();s.set(Te,e.toLowerCase().split(\"-\")[1])}return s.get(Te)};function Re(){if(!i()||De())return!1;const e=window.sapphireWebViewBridge,t=window.webkit,n=e&&e.send,o=t&&t.messageHandlers&&t.messageHandlers.send&&t.messageHandlers.send.postMessage;return!(!n&&!o)}function Oe(){return!!window._isCopilotBridge||\"copilot\"===He(\"MBApp\")}function Le(){const e=_e()&&\"outlook\"===Ee()&&[\"ios\",\"android\",\"ipados\"].includes(xe()),t=Ie()===we.toLowerCase();return e||t}function We(){return!(!i()||De())&&(Re()||Ie()===fe.toLowerCase()||Ie()===Ce.toLowerCase())}function Ne(){const e=He(\"edge\");return(\"emmx\"===Ee()||\"1\"===e)&&We()||\"mmx\"===Ie()}function Pe(){return\"3rdp\"===Ee()||Ie().startsWith(ve)||De()}function Fe(){return[be.toLowerCase(),ye.toLowerCase()].includes(Ie())&&We()}function De(){return Ie().toLowerCase()===Se.toLowerCase()||\"swiftkey\"==Ee().toLowerCase()}const Me=Q((()=>Re()||Le()||We()||Ie()===\"weather-app-win\".toLowerCase()||Ie()===\"finance-app-win\".toLowerCase()||Ne()||Pe()||Fe()||De()||-1!==window.location.href.indexOf(\"metaos=true\")||Oe()));function He(e){try{return new URL(location.href).searchParams.get(e)||\"\"}catch(e){return\"\"}}const Ue=N()||{};const $e={winWidgets:1,webWidgets:1,windowsShell:1,windowsShellV2:1,distribution:1,superApp:1,channelmobile:1,edgeMobile:1},je={edgeChromium:1},Be={winWidgets:1,webWidgets:1},Je={edgeChromium:1,shopping:1,winWidgets:1,webWidgets:1,windowsShell:1,windowsShellV2:1,msn:1},Ve={edgeChromium:1,channelmobile:1,cgHomePage:1,distribution:1,winWidgets:1,webWidgets:1,windowsShell:1,windowsShellV2:1,superApp:1,edgeMobile:1},Ge=Q((()=>Ue&&Ue.apptype)),ze=(Q((()=>i()&&Ge()in Be)),Q((()=>i()&&!Me()&&!(Ge()in $e)&&!Qe()&&!Ye()))),Ke=Q((()=>ze()&&!(Ge()in je)));Q((()=>ze()&&(!(Ge()in je)||!ee()))),Q((()=>ze()&&!et()&&!tt()));const Xe=Q((()=>tt()||et())),Qe=Q((()=>!(Ge()in Je)&&nt()&&function(){if(\"homePage\"==Ge())return!0;const e=new URLSearchParams(h()).get(\"ocid\")||(null==Ue?void 0:Ue.ocid),t=null==e?void 0:e.toLowerCase();if(W.has(t))return!0;return!L.has(t)}()&&!function(){const e=\"homePage\"==Ge()&&\"hp\"==(Ue&&Ue.pagetype)&&(i()?window.location.href:u().url.href).includes(`${P}`);return e}()));const Ye=()=>i()&&document&&document.body&&document.body.getElementsByClassName(\"peregrine-widgets\").length>0;Q((()=>Ge()in Ve||Qe()));function Ze(){if(B)return B;const e=V();return e||void 0}function et(){return Ke()&&ue()||function(){const e=Ke()&&!it()&&!ue()&&Z()&&(\"expired\"==function(){if(j=G(),!j)return\"notAvailable\";const e=j.expiresOn,t=new Date(e),n=\"expired\";return t.getTime()&&t>new Date?\"valid\":n}()||ge());return e}()||he()||pe()}function tt(){return Ke()&&!Z()&&me()}const nt=Q((()=>{const{browser:e}=Ue,{browserType:t=\"\",detected:n={}}=e||{},{browserType:i=\"\"}=n||{};return/edgeChromium/i.test(t)||/edgeAndroid/i.test(t)||/edgeiOS/i.test(t)||/edge/i.test(i)}));Q((()=>Ke()&&!Qe()));const it=()=>{const e=Z();if(!e)return!1;let t;switch(e.account_type){case\"MSA\":t=Ke()?\"ace\":\"aace\";break;case\"AAD\":t=Ke()?\"WID\":\"APP_WID\";break;default:return!1}return!!K(t)};const ot=\"__core_auth_authHeaderPromiseParts__\",st=()=>s.get(ot);let rt;function at(){return rt}!function(){let e;const t={promise:new Promise((t=>e=t)),resolver:e};s.set(ot,t)}();const ct=Q((()=>f(K(\"AL_STATE\",!0))));class dt{constructor(){this.childCookieName=\"child\",this.command=\"command\",this.domInfo=document.head.dataset.info,this.canvasInfo=document.head.dataset.canvasInfo||\"\"}init(e,t){this.clientSettings=e||N(),this.authCookieName=at()||window._authCookieName||\"\",this.bundle=window._webWorkerBundle||\"\",t=t||{};const n=this.buildWebWorkerUrl(this.clientSettings,t),i=window.webWorker=this.createWebWorkerInstance(n);i?this.initCore(i):console.warn(\"Error while initializing the web worker.\")}initCore(e){const t=window.webWorkerMessages=[];e.onmessage=e=>{t.push(e.data)};const n=window.webWorkerErrors=[];e.onerror=e=>{n.push(e)},Xe()&&st().promise.then((t=>e.postMessage({id:\"AuthHeaders\",type:this.command,payload:t||{}})))}createWebWorkerInstance(e){return new Worker(e,{name:\"web-worker\"})}shouldFetchAppAnonCookie(){return!0}getAudienceMode(e){const t=K(this.authCookieName),n=K(this.childCookieName),{child:i}=t&&JSON.parse(t)||{};return t&&(1===i||n)?\"kids\":e.audienceMode||\"adult\"}buildWebWorkerUrl(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1?arguments[1]:void 0;return(this.bundle||\"\")+\"#\"+this.qsFromDocument()+this.qsFromCookies()+this.qsFromDataAttribute(e||this.clientSettings)+this.qsMisc(t)+this.qsAuth(e||this.clientSettings)+this.qsFromUrl()+this.qsFromServiceWorker()+this.qsSelectedPivot()+this.qsXboxXToken()}qs(e,t,n){return e?t+\"=\"+(n||e)+\"&\":\"\"}qsFromDocument(){var e;return this.qs(document.getElementsByTagName(\"html\")[0].getAttribute(\"lang\"),\"lang\")+this.qs(encodeURIComponent(window.location.href),\"adsReferer\")+this.qs(null===(e=i()?window.devicePixelRatio:u().devicePixelRatio)||void 0===e?void 0:e.toString(),\"devicePixelRatio\")}qsFromServiceWorker(){if(!navigator.serviceWorker||!navigator.serviceWorker.controller)return\"\";const e=navigator.serviceWorker.controller;if(\"activated\"!==e.state||!e.scriptURL)return\"\";if(e.scriptURL){const t=new URL(e.scriptURL).searchParams.toString();return t?\"&\"+t+\"&\":\"\"}return\"\"}qsFromCookies(){return this.qs(X(this.authCookieName),\"aace\",\"1\")+this.qs(X(\"muid\"),\"muid\")+this.qs(X(this.childCookieName),\"child\")+this.qs(X(\"cbypass\"),\"cbypass\")+this.qs(this.cookieBannerConsentRequired().toString(),\"ccRequired\")+this.qs(X(\"ecasession\"),\"ecasession\")+this.qs(X(\"TOptOut\"),\"browserOptOut\")}qsFromDataAttributeHelper(e,t,n){var i;e&&e.featureFlags&&e.featureFlags.wpoEnabled&&t&&!t.includes(\"remwpofltww\")&&(t=this.appendWpoFlightsToInfo(e,t));const o=(null===(i=e.modes)||void 0===i?void 0:i.uxMode)||void 0;return this.qs(e.pcsInfo&&e.pcsInfo.env||e.env,\"env\")+this.qs(e.aid,\"aid\")+this.qs(e.apptype,\"apptype\")+this.qs(e.pagetype,\"pagetype\")+this.qs(e.audienceMode,\"audienceMode\")+this.qs(e.configIndexDocId,\"configIndexDocId\")+this.qs(e.deviceFormFactor,\"deviceFormFactor\")+this.qs(e.domain,\"domain\")+this.qs(e.configRootUrl,\"configRootUrl\")+this.qs(this.getHighestCbid(e.cbid,e.apptype),\"cbid\")+this.qs(e.ocid,\"ocid\")+this.qs(e.os,\"os\")+this.qs(JSON.stringify(e.locale),\"locale\")+this.qs(e.geo_lat,\"lat\")+this.qs(e.geo_long,\"long\")+this.qs(JSON.stringify(e.featureFlags),\"featureFlags\")+this.qs(JSON.stringify(e.browser),\"browser\")+this.qs(JSON.stringify(e.servicesEndpoints),\"servicesEndpoints\")+this.qs(e.bundleInfo&&e.bundleInfo.v||\"\",\"buildVersion\")+this.qs(t,\"dataInfo\")+this.qs(n,\"canvasInfo\")+this.qs(o,\"uxMode\")}qsSelectedPivot(){const e=\"selectedPivot\";let t;try{const n=te();n&&(t=n.getItem(e))}catch(e){console.warn(\"Error getting pivot id from local storage. \"+e)}return this.qs(t,e)}qsXboxXToken(){const e=\"xboxXTokenId\";let t;try{const n=te();n&&(t=n.getItem(e))}catch(e){console.warn(\"Error getting xbox XToken from local storage. \"+e)}return this.qs(t,e)}appendWpoFlightsToInfo(e,t){const n=this.getWpoFlightsFromLocalStorage(e);if(n&&n.length){const e=\";\",i=t.split(e);for(let e=0;e<i.length;e++){const t=i[e];if(t&&t.startsWith(\"f:\")){i[e]=t+\",\"+n.join();break}}return i.join(e)}return t}getWpoFlightsFromLocalStorage(e){const t=te(),n=e.locale,i=`wpo_data_ ${e.audienceMode}_${n.language}_${n.market}_${e.pagetype}_${e.apptype}`;try{const e=t.getItem(i);if(e){return JSON.parse(e).treatments.map((e=>e.id))}}catch(e){console.warn(`Error getting wpo flights from ls for ${i} Error:${e}`)}return null}getHighestCbid(e,t){try{const n=te(),i=parseInt(e),o=parseInt(JSON.parse(n.getItem(`cbid_${t||\"\"}`)));let s;if(i&&!isNaN(i)&&o&&!isNaN(o)&&(s=i>o?i:o),s=s||i||o,s)return window._cbid=s.toString()}catch(e){console.warn(\"Error getting highest CBID\"+e)}}qsFromDataAttribute(e){let t,n,i;const o=te();if(o){const i=JSON.parse(o.getItem(\"shd_\"+e.pagetype)||null);i&&i.clientSettings&&i.info&&(t={...i.clientSettings,bundleInfo:e.bundleInfo},n=i.info)}return(!t||!n||e.pageGenTime>=t.pageGenTime)&&(t=e,n=this.domInfo,i=this.canvasInfo),t.audienceMode=this.getAudienceMode(t),this.qsFromDataAttributeHelper(t,n,i)}qsFromUrl(){const e=location.search;return e&&e.length?e.substring(1,e.length):\"\"}qsMisc(e){return this.qs(performance.timeOrigin&&performance.timeOrigin.toString(),\"mainTimeOrigin\")+this.qs(e.disableWWConfig&&\"1\",\"disableWWConfig\")}qsAuth(e){let t=this.qs(Ke()&&\"1\",\"disableWWAuth\");if(!Xe()){const e=Ke()&&Ze()||{};t+=this.qs(encodeURIComponent(JSON.stringify(function(e){const t={};return e&&(t[_]=`Bearer ${e}`),t}(e.accessToken))),\"authHeaders\")}return t+=this.qs(function(e){const t=e||at()||\"al_app_anon\",n=ct();return!(!n||!([\"anon\",\"al_anon\"].includes(t)&&n.AL_ANON||[\"app_anon\",\"al_app_anon\"].includes(t)&&n.AL_APP_ANON))}()&&\"1\",\"isAccountLinked\"),t+=this.qs(this.shouldFetchAppAnonCookie()&&\"1\",\"fetchAppAnonCookie\"),t}cookieBannerConsentRequired(){const e=null!=document.getElementById(\"consent-banner-container\")&&null===K(\"MSCC\"),t=null!=document.getElementById(\"onetrust-sdk\")&&null===K(\"eupubconsent-v2\");return e||t}}const lt=Object.freeze({AIDRequestHeaderName:\"pcs-aid\",Product:Object.freeze({edge:\"spartan\",edgeChromium:\"anaheim\",hybrid:\"spartan\",hub:\"prime\",microsoftNews:\"msnews\",office:\"entnews\",weather:\"prime\",views:\"prime\"}),PageTimings:Object.freeze({inlineStart:\"page.inlineStart\",fragmentFetchStart:\"ssr.fragmentFetching\",fragmentFetchEnd:\"ssr.fragmentFetched\",fragmentFetchStatusCode:\"ssr.fragmentFetchStatusCode\",fragmentBrowserCached:\"ssr.fragmentBrowserCached\",fragmentCDNCached:\"ssr.fragmentCDNCached\",injectingDOM:\"ssr.injectingDOM\",awaitingRAF:\"ssr.awaitingRAF\",markersSet:\"ssr.markersSet\",completed:\"ssr.completed\",CSRTimeout:\"ssr.CSRTimeout\",CSROnly:\"ssr.csrOnly\",didSSR:\"ssr.isSSRPagePresent\",riverEnabled:\"ssr.riverEnabled\",ssrState:\"ssr.state\",pageTTVR:\"TTVR\",fetchDelayReason:\"ssr.fetchDelayReason\",awaitingInnerWidthTime:\"ssr.awaitingInnerWidthTime\",skipDueToHiddenReason:\"ssr.skipDueToHiddenReason\",pssrRejectedReason:\"ssr.pssrRejected\",ssrResponseSize:\"ssr.responseSize\",inlineBodyStart:\"ssr.inlineBodyStart\",pcsEnv:\"pcsEnv\"}),pauseThresholdMS:1200,oneServiceHeaders:\"1s-headers\",edgeNTPHeader:\"sec-edge-ntp\",defaultSSRFeedLayout:\"multi\"});var ut,ht,gt;!function(e){e[e.Hub=0]=\"Hub\",e[e.Homepage=1]=\"Homepage\",e[e.Edge=2]=\"Edge\",e[e.Weather=3]=\"Weather\",e[e.Finance=4]=\"Finance\",e[e.Community=5]=\"Community\",e[e.Views=6]=\"Views\",e[e.Gaming=7]=\"Gaming\",e[e.Sports=8]=\"Sports\",e[e.Health=9]=\"Health\",e[e.Shopping=10]=\"Shopping\",e[e.CgHomePage=11]=\"CgHomePage\"}(ut||(ut={})),function(e){e[e.documentHidden=0]=\"documentHidden\",e[e.hasPaused=1]=\"hasPaused\",e[e.invalidInnerWidth=2]=\"invalidInnerWidth\",e[e.slowFetchStart=3]=\"slowFetchStart\"}(ht||(ht={})),function(e){e.JSON=\"application/json;charset=UTF-8\",e.HTML=\"text/html;charset=UTF-8\"}(gt=gt||(gt={}));function pt(){return i()?window._pageTimings||(window._pageTimings={}):s.get(\"__diagnostics_pageTimings\",(()=>({})))}function mt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:performance.now(),n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];const i=pt();e in i||(i[e]=n?Math.round(t):t)}function wt(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];window.performance.mark(e),t&&mt(e)}let ft,vt;function bt(){if(vt)return vt;const e=document.head.getAttribute(\"data-info\");return vt=((/f:\\s*([^;]+)/i.exec(e)||{})[1]||\"\").toLowerCase(),vt}function yt(e,t,n){const o=n&&\"phone\"===n.toLowerCase(),s=t&&\"enterprise\"===t;let r=\"Edge\";De()?r=\"swiftKey\":Fe()?r=\"Launcher\":function(){var e;if(!i())return!1;if(\"bingHomepageMobile\"===(null===(e=window._clientSettings)||void 0===e?void 0:e.pagetype))return!0;{var t;const e=null===(t=document.getElementsByClassName(\"peregrine-widget-settings\")[0])||void 0===t?void 0:t.getAttribute(\"data-client-settings\");if(e){const t=JSON.parse(e);return t&&t.pagetype&&\"bingHomepageMobile\"===t.pagetype}return!1}}()&&(r=\"bingHomepageMobile\");return{bingHomepage:\"binghomepage\",mmx:\"emmx\",edge:\"spartan\",edgeChromium:s?\"entnews\":\"anaheim\",hybrid:\"spartan\",hub:o?\"prime_mobile\":\"prime\",microsoftNews:\"msnews\",office:\"entnews\",views:o?\"prime_mobile\":\"prime\",homePage:o?\"prime_mobile\":\"prime\",windowsShell:\"windowsshell\",edgeMobile:r}[e]}const St={homePage:{vertical:\"homepage\",category:\"\",id:\"\",domainId:\"13041\",title:\"undefined\"!=typeof document?document.title:\"\"}};function Ct(e){let t=e;switch(e){case\"windowsshellhp\":t=\"dhp\";break;case\"video\":t=\"watch\";break;case\"EdgeMobile\":t=Ne()?\"ntp\":\"dhp\"}return t}let kt;var It;!function(e){e[e.Alert=0]=\"Alert\",e[e.Deprecated=1]=\"Deprecated\",e[e.HighImpact=2]=\"HighImpact\",e[e.Critical=3]=\"Critical\"}(It||(It={}));const At=new class{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:20;this.maxLength=e,this.list=[]}push(e){this.list.push(e),this.list.length>this.maxLength&&this.list.shift()}get data(){return this.list}};function qt(e,t,n){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:It.Alert,o=arguments.length>4?arguments[4]:void 0,s=arguments.length>5?arguments[5]:void 0,r=arguments.length>6?arguments[6]:void 0;try{const a=function(){if(!ft){const e=document.head.getAttribute(\"data-client-settings\");e&&(ft=JSON.parse(e))}return ft}(),c=bt(),d=function(e){if(e){const{pcsInfo:t,pageGenTime:n}=e,i=new Date(n).getTime(),o=!t||[\"prod\",\"prod-ssr\",\"prod-ssrntp\"].includes(t.env);kt=o?\"browser.events.data.msn.com\":\"events-sandbox.data.msn.com\";return{cors:\"true\",\"content-type\":\"application/x-json-stream\",\"client-id\":\"NO_AUTH\",\"client-version\":\"1DS-Web-JS-2.2.2\",apikey:o?\"0ded60c75e44443aa3484c42c1c43fe8-9fc57d3f-fdac-4bcf-b927-75eafe60192e-7279\":\"f8857dedc6f54ca8962cfb713e01e7d7-e9250191-fe0b-446f-95ae-07516262f98c-7028\",\"upload-time\":i,w:\"0\",anoncknm:\"app_anon\"}}return null}(a);let l=\"\";d&&d.apikey&&\"\"!==d.apikey&&(l=function(e){if(e){const t=e.indexOf(\"-\");if(t>0)return e.substring(0,t)}return\"\"}(d.apikey));const u=function(e,t,n,i,o,s){let r=arguments.length>6&&void 0!==arguments[6]?arguments[6]:It.Alert,a=arguments.length>7&&void 0!==arguments[7]?arguments[7]:\"\";if(n){o=o||{};const{aid:u,apptype:h,audienceMode:g,pagetype:p,pageGenTime:m,bundleInfo:w,deviceFormFactor:f=\"\",fd_muid:v,os:b,static_page:y}=n;o.pageGenTime=m,o.build=w&&w.v,o.appType=h,o.pageVisible=document.visibilityState;const S=yt(h,g,f),C=S||h,k=document.getElementsByTagName(\"html\")[0].getAttribute(\"lang\");let I,A=\"\",q=\"muid\";try{if(\"edgeChromium\"===h&&\"object\"==typeof window&&window.location&&window.location.search){const e=new URLSearchParams(window.location.search);I=e.has(\"startpage\")?\"msedgdhp\":\"msedgntp\",\"enterprise\"===g?I=\"entnewsntp\":\"xbox\"===b&&(I=\"xboxntp\")}window&&window.getCookieConsentRequired&&\"function\"==typeof window.getCookieConsentRequired&&window.getCookieConsentRequired()||(A=(null===(d=window)||void 0===d||null===(d=d.__SSRUserConfigEarlyLog)||void 0===d?void 0:d.consistentMuid)||(null===(l=window)||void 0===l?void 0:l.__muid)||K(\"muid\"))}catch{}A||(A=n.aid,q=\"aid\");const T={name:\"MS.News.Web.AppError\",time:m,ver:\"4.0\",iKey:`o:${i}`,data:{baseData:{},baseType:\"MS.News.Web.Base\",page:{name:\"default\",product:C,type:Ct(p),content:St[h]??{category:\"standaloneError\"},ocid:I,url:location.href,isStatic:y},browser:{clientId:A,clientIdType:q},flight:{id:s,tmpl:a},request:{activityId:u,requestId:u,afdMuid:v},locale:{mkt:k},extSchema:{id:e,severity:r,pb:o,message:t}}};var c;if(A&&\"muid\"===q)T.ext={...null==T?void 0:T.ext,user:{...null==T||null===(c=T.ext)||void 0===c?void 0:c.user,localId:`t:${A}`}};return\"object\"==typeof window&&(window.isSSREnabled&&(T.data.flight.tmpl+=\";ssr-enabled:1\"),window.isSSRCompleted&&(T.data.flight.tmpl+=\";ssr-completed:1\")),T?JSON.stringify(T):null}var d,l;return null}(t,e,a,l,n,c,i,r);if(s&&!function(e){if(null==e)return!1;return e.startsWith(\"1\")||e.startsWith(\"2\")||e.startsWith(\"3\")||e.startsWith(\"4\")}(null==a?void 0:a.aid))return console.error(u),void console.error(`This App error Id: ${t} will not be sent due to app error sampling!`);if(d&&u){console.error(u),At.push(u);const e=\"https://\"+kt+\"/OneCollector/1.0\"+function(e){return\"?\"+Object.keys(e).map((function(t){return t+\"=\"+encodeURIComponent(e[t])})).join(\"&\")}(d);if(o&&!performance.getEntriesByType(\"visibility-state\").some((e=>\"visible\"===e.name))){const t=()=>{\"visible\"===document.visibilityState&&navigator.sendBeacon(e,u)};document.addEventListener(\"visibilitychange\",t,{once:!0})}else navigator.sendBeacon(e,u)}}catch{}}const Tt=\"experience\",_t=[\"common-monthly\"],Et=Object.create(null),xt=2;function Rt(e,t){if(!(t=t||Array.from(document.scripts).find((t=>t.src.indexOf(`/${e}.`)>-1))))return;const n=function(e,t){const n=document.createElement(\"script\");return n.type=\"text/javascript\",n.crossOrigin=\"anonymous\",n.src=e,n.onerror=()=>{let e=Et[t]||0;Et[t]=++e,e<xt?Rt(t,n):Lt()},n.onload=()=>{delete Et[t];const e=window._pageTimings.retriedBundles;window._pageTimings.retriedBundles=e?`${e},${t}`:t,Lt()},n}((i=t.src,Et[e],i),e);var i;setTimeout((()=>t.replaceWith(n)),100)}function Ot(){const[e={}]=performance.getEntriesByType(\"navigation\");return e.type}function Lt(){if(!Object.values(Et).every((e=>e===xt)))return;let e=\"\";Object.keys(Et).forEach((t=>{e=e?`${e},${t}`:t,delete Et[t]})),e&&qt(`Error when loading bundle(s): ${e}`,20202,{timeElapsed:Math.round(performance.now()),navType:Ot()})}function Wt(e){return new Promise((t=>setTimeout(t,e)))}\"undefined\"!=typeof window&&window.document&&window.document.createElement&&(window._pageTimings=window._pageTimings||{},window.requestIdleCallback=window.requestIdleCallback||window.setTimeout);wt(lt.PageTimings.inlineStart,!0);const{inlineCRS:Nt}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t={};try{var n,o,s;if(!i())return t;const r=f(I(\"clientSettings\"))||{};let a=null===(n=r.apptype)||void 0===n?void 0:n.toLocaleLowerCase();const c=e.crsOptions||{};\"winwidgets\"!==a&&\"homepage\"!==a&&\"travel\"!==a||(c.crsPrefetch=!0),null!==(o=r.widgetAttributes)&&void 0!==o&&null!==(o=o.clientPerf)&&void 0!==o&&o.earlyCRSCall&&(c.crsPrefetch=!0),\"edgechromium\"===a&&(a=\"edgechr\");const d=!(null===(s=r.widgetAttributes)||void 0===s||null===(s=s.clientPerf)||void 0===s||!s.shouldUsePWOnlyFlights),{all:l,prg:g}=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:\"\";const n=T(I(\"info\"))||[];let i=T(I(\"canvasInfo\"));if(!i){i=C(n,k(n,e,t))}return{all:n,prg:i}}(d,a);(c.crsPrefetch||l.includes(\"inlinecrscall\")||l.includes(`icrscall-${a}`))&&(!function(e,t,n){const o=function(e,t){let n;const i=decodeURIComponent(w(e,\"aace\")),{child:o}=i&&f(i)||{},s=w(e,m);var r;return n=i&&(1===o||s)?\"kids\":(null===(r=t.modes)||void 0===r?void 0:r.audienceMode)||t.audienceMode||\"adult\",n}(document.cookie,e),{apptype:s,browser:r,domain:a,bundleInfo:{v:c}={},deviceFormFactor:d,locale:{content:l,display:g}={},ocid:p,os:v,modes:b,pagetype:y,servicesEndpoints:{crs:S}={}}=e,C=(q()?i()?window.location.hostname:u().url.hostname:a)||\"\";if(!s||!c||!S)return;const k={audienceMode:o,uxMode:null==b?void 0:b.uxMode},I={audienceMode:o,browser:r,deviceFormFactor:d,domain:C,locale:{content:l,display:g},ocid:p,os:v,modes:k,platform:\"web\",pageType:y,pageExperiments:t},A=new URLSearchParams([[\"expType\",\"AppConfig\"],[\"expInstance\",\"default\"],[\"apptype\",s],[\"v\",c]]),T=h(),_=T.includes(\"?item=\")||T.includes(\"&item=\")?\"&citem=dev:true&instance=Int1\":\"\",E=`${S.path}${S.v}/config/?${A}&targetScope=${JSON.stringify(I)}${_}`,x=e=>{const t=new URL(`https://${e}${E}`),{timeout:i=5e3}=n;let o;if(window.crsRequestData={url:t.toString()},i>=0){const e=new AbortController;setTimeout((()=>e.abort()),i),o=e.signal}return fetch(t,{signal:o})},R=\"msn.\"+(function(e,t){return!!(e.hostname.toLowerCase().endsWith(\".cn\")||t.isChinaCompliance||t.useCnDomain||e.search.match(/isChinaBuild=true/i)||e.search.match(/item=revip:cn/))}(location,e)?\"cn\":\"com\"),{domain:O}=S,L=O&&new URL(O).host;let W=x(L||`assets.${R}`);n.disableFallback||(W=W.catch((()=>{const{hostname:e}=location,t=e.match(/\\.msn\\.(com|cn)$/)?e:`www.${R}`;return x(t)})));n.silenceUnhandledError&&(W=W.catch((e=>{console.error(\"InlineHeadCommon: CRS fetch error in silence\",e)})));window.crsPromise=W}(r,g,c),t.inlineCRS=!0)}catch(e){console.error(\"InlineHeadCommon: \"+e)}return t}(),Pt=N();function Ft(e){var t;return((null===(t=document)||void 0===t||null===(t=t.head)||void 0===t||null===(t=t.getAttribute(\"data-info\"))||void 0===t?void 0:t.split(\";\").find((e=>e.startsWith(\"f:\"))))||\"\").includes(e)}window._clientSettings=Pt,Ft(\"prg-wx-nowebworker\")||(new dt).init(Pt,{disableWWConfig:Nt}),function(e){const t=[\"vendors\",\"microsoft\",\"common\"];e&&t.push(..._t),window.addEventListener(\"load\",(()=>{window._pageTimings.navType=Ot(),window.requestIdleCallback((()=>{const e=function(e){const t=window.weatherWebpackChunks;if(!t)return[...e,Tt];if(t.length>e.length)return[];t.forEach((t=>e.splice(e.indexOf(t[0][0]),1))),t.push===Array.prototype.push&&e.push(Tt);return e}(t);e.length&&e.forEach((e=>Rt(e)))}))}),{once:!0})}(),Ft(\"prg-wx-psdawc\")&&async function(){window.apntag=window.apntag||{anq:[]},window.apntag.anq=window.apntag.anq||[],await async function(e){if(!i())return;const{src:t,id:n,async:o=!0,container:s=document.head,retryNumber:r=0,retryDelayMs:a=0}=e;try{await async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:100;if(t<=1)return e();let i,o=1;do{try{i=await e()}catch(e){if(o===t)throw e&&e.message&&(e.message=`${e.message} after ${o} attempts`),e;await Wt(n)}}while(!i&&++o<=t);return i}((()=>function(e,t,n){let o=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];return new Promise(((s,r)=>{const a=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:\"anonymous\",s=arguments.length>4?arguments[4]:void 0,r=arguments.length>5?arguments[5]:void 0;if(!i())throw new Error(\"createScriptTag is unsupported server side. This call should be wrapped in canUseDOM().\");const a=document.createElement(\"script\");return t&&(a.id=t),s&&a.setAttribute(\"class\",s),r&&(a.onload=r),a.type=\"text/javascript\",e&&(window&&window.trustedTypesURLValidation?a.src=window.trustedTypesURLValidation(e,\"scriptSrcUrlPolicy\"):a.src=e,a.async=n,a.crossOrigin=o),window&&window.NONCE_ID&&(a.nonce=window.NONCE_ID),a}(e,t,o,void 0,void 0,(()=>s(!0)));a.crossOrigin=void 0,a.onerror=r,n.prepend(a)}))}(t,n,s,o)),r,a)}catch(e){qt(\"Failed to download Static JS\",11103,{scriptId:n,scriptSrc:t},It.Alert)}}({id:\"display-sdk\",src:\"https://adsdk.microsoft.com/ast/ast.js\",container:document.head,retryDelayMs:200,retryNumber:5})}()}();\r\n        </script>\r\n         <script nonce=\"\" src=\"https://assets.msn.cn/staticsb/statics/latest/oneTrustCN/1.1/scripttemplates/otSDKStub.js\" data-document-language=\"true\" type=\"text/javascript\" charset=\"UTF-8\" data-domain-script=\"42e7c639-42ed-4e0f-936c-e7f2a5fcb01e\" id=\"onetrust-sdk\"></script>\r\n\r\n\r\n<link rel=\"dns-prefetch\" href=\"//img-s-msn-com.akamaized.net\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//sb.scorecardresearch.com\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//th.bing.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//img-s-msn-com.akamaized.net\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//sb.scorecardresearch.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//th.bing.com\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//ecn.api.tiles.virtualearth.net\" crossorigin=\"anonymous\"><link rel=\"preconnect\" href=\"//ecn.api.tiles.virtualearth.net\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//t.ssl.ak.dynamic.tiles.virtualearth.net\" crossorigin=\"anonymous\"><link rel=\"preconnect\" href=\"//t.ssl.ak.dynamic.tiles.virtualearth.net\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//t.ssl.ak.tiles.virtualearth.net\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//api.msn.cn\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//assets.msn.cn\" crossorigin=\"anonymous\"><link rel=\"dns-prefetch\" href=\"//assets.msn.cn\" crossorigin=\"use-credentials\"><link rel=\"dns-prefetch\" href=\"//c.msn.cn\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//api.msn.cn\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//assets.msn.cn\" crossorigin=\"anonymous\"><link rel=\"preconnect\" href=\"//assets.msn.cn\" crossorigin=\"use-credentials\"><link rel=\"preconnect\" href=\"//c.msn.cn\" crossorigin=\"use-credentials\">\r\n        <script type=\"text/javascript\" nonce=\"\">\r\n            if(matchMedia(\"(prefers-color-scheme:dark)\").matches){const s=document.createElement(\"style\");document.head.appendChild(s);s.type=\"text/css\";s.appendChild(document.createTextNode(\"body{background:#242424}\"));}\r\n        </script>\r\n\r\n\r\n\r\n\n        <meta name=\"description\" content=\"使用 MSN 天气 获取 Longquanyi-District 今天、晚上和明天的每小时准确预测，以及 10 天每日预测和天气雷达。随时了解降水、严重天气警告、空气质量和野火警报。\">\n<link rel=\"canonical\" href=\"https://www.msn.cn/zh-cn/weather/forecast/in-Longquanyi-District\"/>\n<meta property=og:url content=\"https://www.msn.cn/zh-cn/weather/forecast/in-Longquanyi-District\">\n        \n        </head>\r\n<body>\r\n    \r\n\r\n\r\n    <link rel=\"stylesheet\" href=\"/bundles/v1/weather/latest/SSR-service-entry.6d86847df0dd6cebc71b.css\" />\r\n\r\n<div id=\"root\" data-config-indexdocid=\"\">\r\n\n        \n        <script nonce>\n            \n        </script>\n        <style id=\"ssr-styles\">.svgRadarContainer-E1_1 {\n  position: absolute;\n  display: flex;\n  flex-wrap: wrap;\n  flex-direction: row;\n  align-content: flex-start;\n  opacity: 0;\n  pointer-events: none;\n  top: 0;\n  left: 0;\n}\n.svgRadarContainerVisible-E1_1 {\n  opacity: 0.7;\n}\n.svgRadarContainerVisibleAtBackground-E1_1 {\n  opacity: 1;\n}\n.svgRadarTile-E1_1 {\n  padding: 0;\n  margin: 0;\n  margin-top: -1px !important;\n  margin-left: -1px !important;\n}\n.svgRadarTileNoMargin-E1_1 {\n  padding: 0;\n  margin: 0;\n}\n.pane-E1_1 {\n  position: absolute;\n  display: block;\n  top: 0;\n  left: 0;\n  overflow: hidden;\n  width: 100%;\n  height: 100%;\n}\n.tileContainer-E1_1 {\n  position: absolute;\n  display: flex;\n  flex-wrap: wrap;\n  pointer-events: none;\n  direction: ltr;\n}\n.tile-E1_1 {\n  padding: 0;\n  margin: 0;\n  position: relative;\n}\n.tile-E1_1:focus {\n  outline: none;\n}\n.hideImageAltTagInFF:-moz-loading {\n  visibility: hidden;\n}\n.miniMapContainer-E1_1 {\n  position: relative;\n  width: 100%;\n  height: 210px;\n  border: 0px solid #003fee;\n  overflow: hidden;\n  cursor: pointer;\n  border-radius: 6px;\n  transform: translateZ(0);\n  box-sizing: border-box;\n}\n.miniMapContainer-E1_1 svg {\n}\n.miniMapContainer-E1_1:focus-within {\n  outline: 1px solid #2B2B2B;\n}\n@media (-ms-high-contrast:active) {\n  .miniMapContainer-E1_1 {\n    border: 1px solid highlight;\n  }\n}\n.miniMapContainer-E1_1.round {\n  border-radius: 16px;\n}\n.miniMapContainer-E1_1 [data-type='maskElement'] {\n  display: block;\n  position: absolute;\n  background: none;\n  border: none;\n  width: 100%;\n  height: 100%;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 5;\n  text-decoration: none;\n  curson: pointer;\n  pointer-events: all;\n}\n@media (-ms-high-contrast:active) {\n  .miniMapContainer-E1_1 svg {\n    opacity: 1;\n    fill: ButtonText !important;\n  }\n}\n.miniMapContainerLink-E1_1 {\n  position: relative;\n  display: block;\n  height: 100%;\n  width: 100%;\n  z-index: 1;\n}\n.miniMapButtonGroupContainer-E1_1 {\n  position: absolute;\n  width: 40px;\n  height: 180px;\n  border: 0px solid #003fee;\n  top: 12px;\n  left: 12px;\n  z-index: 5;\n}\n.miniMapStaticMap-E1_1 {\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  height: 100%;\n  width: 100%;\n  object-fit: none;\n  object-position: center center;\n  transform: translate(-50%, -50%);\n  overflow: hidden;\n  pointer-events: none;\n  border-radius: initial;\n}\n.miniMapExpandContainer-E1_1 {\n  position: absolute;\n  display: inline-flex;\n  width: -webkit-fit-content;\n  height: 32px;\n  right: 16px;\n  bottom: 18px;\n  border-radius: 4px;\n  background: #FFFFFF;\n  cursor: pointer;\n  text-decoration: none;\n}\n.miniMapExpandContainer-E1_1:focus {\n  outline: none;\n}\nbody:not(.js-focus-visible) .miniMapExpandContainer-E1_1:focus, .js-focus-visible .miniMapExpandContainer-E1_1.focus-visible, .js-focus-visible [data-focus-visible-added].miniMapExpandContainer-E1_1 {\n  border: 1px solid;\n  border-color: rgb(16, 16, 16);\n}\n.miniMapExpandContainer-E1_1> span:first-child {\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  outline: none;\n}\n@media (-ms-high-contrast:active) {\n  .miniMapExpandContainer-E1_1 {\n    border: 1px solid highlight;\n  }\n  .miniMapExpandContainer-E1_1:hover, :hover > .miniMapExpandContainer-E1_1, .miniMapExpandContainer-E1_1:focus {\n    text-decoration: underline;\n  }\n}\n.miniMapExpandContainer-E1_1> span:first-child> span {\n}\n.miniMapExpandContainer-E1_1> span:first-child> span > svg {\n  margin: 8px;\n}\n.miniMapExpandTopRightContainer-E1_1 {\n  position: absolute;\n  cursor: pointer;\n  z-index: 3;\n  animation: delayFadeIn ease 0.8s;\n  text-decoration: none;\n  top: 12px;\n  right: 8px;\n  border-radius: 8px;\n  background: white;\n  display: flex;\n  flex-direction: row;\n  align-content: center;\n  justify-content: center;\n  align-items: center;\n  box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.15);\n  width: 40px;\n  height: 40px;\n}\n.miniMapExpandTopRightContainer-E1_1:hover {\n  background: rgba(0, 0, 0, 0.3);\n}\n.miniMapExpandTopRightContainer-E1_1:focus {\n  outline: none;\n}\nbody:not(.js-focus-visible) .miniMapExpandTopRightContainer-E1_1:focus, .js-focus-visible .miniMapExpandTopRightContainer-E1_1.focus-visible, .js-focus-visible [data-focus-visible-added].miniMapExpandTopRightContainer-E1_1 {\n  border: 1px solid;\n  border-color: rgb(16, 16, 16);\n}\n@media (-ms-high-contrast:active) {\n  .miniMapExpandTopRightContainer-E1_1 {\n    border: 1px solid highlight;\n  }\n  .miniMapExpandTopRightContainer-E1_1:hover, :hover > .miniMapExpandTopRightContainer-E1_1, .miniMapExpandTopRightContainer-E1_1:focus {\n    text-decoration: underline;\n  }\n}\n.miniMapExpandTopRightContainer-E1_1> span {\n}\n.miniMapExpandTopRightContainer-E1_1> span > svg {\n  width: 14px;\n  height: 14px;\n  margin: 0px;\n  fill: black;\n}\n.miniMapExpandTopRightL1-E1_1 {\n  background: white !important;\n  right: unset;\n  left: 12px;\n  top: unset;\n  bottom: 57px;\n  backdrop-filter: blur(4px);\n}\n.miniMapExpandTopRightL1Precip-E1_1 {\n  bottom: 113px !important;\n}\n.miniMapExpandButton-E1_1 {\n  font-style: normal;\n  font-weight: 500;\n  font-size: 14px;\n  line-height: 32px;\n  text-align: center;\n  width: -webkit-fit-content;\n  color: #666666;\n  z-index: 1;\n  border-right: 1px solid #ECECEC;\n  padding-left: 10px;\n  padding-right: 10px;\n}\n.miniMapExpandButton-E1_1:hover {\n  color: #444444;\n}\n.miniMapMarker-E1_1 {\n  position: absolute;\n  cursor: pointer;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  pointer-events: none;\n  object-fit: cover;\n  box-sizing: border-box;\n  width: 12px;\n  height: 12px;\n  border-radius: 50%;\n  overflow: visible;\n  background: #ffd02e;\n  border: 3px solid #FFF;\n  box-shadow: 0px 0px 10px rgb(0 0 0 / 40%);\n  display: flex !important;\n  align-items: center;\n  justify-content: center;\n  z-index: 4;\n}\n.miniMapMarker-E1_1:before {\n  content: ' ';\n  display: block;\n  width: 12px;\n  height: 12px;\n  min-width: 12px;\n  border-radius: 50%;\n  box-shadow: 0px 0px 3px 3px #FFFC;\n  opacity: 0;\n}\n@keyframes locationPinBorder {\n  0% {\n    opacity: 0;\n  }\n  50% {\n    opacity: 0.8;\n  }\n  100% {\n    opacity: 0;\n  }\n}\n.timestampMask-E1_1 {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 20%;\n  pointer-events: none;\n  background: linear-gradient(180deg, rgba(0, 0, 0, 0.2) 21.57%, rgba(196, 196, 196, 0) 88.24%);\n  z-index: 3;\n}\n.timestamp-E1_1 {\n  position: absolute;\n  top: 4px;\n  right: 4px;\n  color: #FFFFFF;\n  opacity: 1;\n  line-height: 14px;\n  text-shadow: -1px -1px 0 rgb(0 0 0 / 45%), 1px -1px 0 rgb(0 0 0 / 45%), -1px 1px 0 rgb(0 0 0 / 45%), 1px 1px 0 rgb(0 0 0 / 45%);\n  z-index: 3;\n}\n.weatherFullMapButton-E1_1 {\n  z-index: 6;\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n  width: 30px;\n  height: 30px;\n  position: absolute;\n  top: 16px;\n  right: 16px;\n  border-radius: 8.92px;\n  backdrop-filter: blur(30px);\n  box-shadow: 0px 0.5px 3px 0px rgba(0, 0, 0, 0.2);\n  border: 1.12px solid rgba(255, 255, 255, 1);\n  background: rgba(248, 244, 242, 0.6);\n}\n.weatherFullMapButton-E1_1 > span {\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n}\n.weatherFullMapButton-E1_1 > span > svg {\n  fill: #3D3D3D;\n}\n.fallbackImage-E1_1 {\n  width: 100%;\n  height: 100%;\n  object-fit: cover;\n  position: absolute;\n}\n.simpleInfoPane-E1_1 {\n  position: absolute;\n  left: 16px;\n  bottom: 16px;\n  right: 16px;\n  border-radius: 8px;\n  font-size: 14px;\n  font-weight: 600;\n  padding: 10px 16px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  z-index: 5;\n  backdrop-filter: blur(30px);\n  white-space: nowrap;\n  word-break: keep-all;\n  color: #2B2B2B;\n}\n.simpleInfoPane-E1_1:before {\n  content: ' ';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  z-index: -1;\n  opacity: 0.9;\n  background-color: #FFFFFF;\n}\n.weatherMapMini_root-E1_1 {\n  margin: 0 auto;\n  position: relative;\n  color: #2B2B2B;\n}\n@media (max-width: 979px) {\n  .weatherMapMini_root-E1_1 {\n    width: 624px;\n  }\n}\n@media (min-width: 980px) and (max-width: 1303px) {\n  .weatherMapMini_root-E1_1 {\n    width: 948px;\n  }\n}\n@media (min-width: 1304px) and (max-width: 1627px) {\n  .weatherMapMini_root-E1_1 {\n    width: 1272px;\n  }\n}\n.weatherMapMini_root_mobile-E1_1 {\n  width: 100vw;\n  box-sizing: border-box;\n  padding: 0 16px;\n  margin-bottom: 16px;\n}\n@media (min-width: 656px) {\n  .weatherMapMini_root_mobile-E1_1 {\n  }\n  .weatherMapMini_root_mobile-E1_1 img[class*=miniMapStaticMap] {\n    object-fit: cover;\n  }\n}\n.marginBottom-E1_1 {\n  margin-bottom: -24px;\n}\n.marginNormal-E1_1 {\n  margin-top: -24px;\n}\n.mini_root_normal-E1_1 {\n  padding: 0px;\n  background: none;\n  box-shadow: none;\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n}\n.mapCopilotTitle-E1_1 {\n  font-size: 20px;\n  line-height: 26px;\n  font-weight: 600;\n  color: var(--copilot-primary-font);\n  margin: 0 0 16px 0;\n}\n.weatherMapMiniDescLayout-E1_1 {\n  background: rgba(255, 255, 255, 0.1);\n  backdrop-filter: blur(60px);\n  -webkit-backdrop-filter: blur(60px);\n  border-radius: 4px;\n  padding: 16px;\n  margin: 0 16px 16px;\n  width: calc(100vw - 32px);\n}\n.weatherMapMini_title-E1_1 {\n  color: #FFFFFF;\n  font-size: 14px;\n  line-height: 22px;\n  font-weight: 600;\n  text-transform: uppercase;\n  height: 22px;\n  margin-bottom: 12px;\n  display: none;\n}\n@media (max-width: 979px) {\n  .weatherMapMini_title-E1_1 {\n    display: block;\n  }\n}\n.weatherMapMini_common_title-E1_1 {\n  color: #FFFFFF;\n  font-size: 14px;\n  line-height: 22px;\n  font-weight: 600;\n  text-transform: uppercase;\n  height: 22px;\n  margin-bottom: 12px;\n  display: none;\n}\n@media (min-width: 980px) and (max-width: 1303px) {\n  .weatherMapMini_common_title-E1_1 {\n    display: block;\n  }\n}\n.bottomCard-E1_1 {\n  margin-top: 20px;\n  height: 168px;\n}\n.feedbackButtonWrapper-E1_1 {\n  display: flex;\n  flex-direction: row;\n  justify-content: space-between;\n  padding: 10px 12px;\n  backdrop-filter: blur(15px);\n  border: none;\n  cursor: pointer;\n  font-weight: 400;\n  font-size: 14px;\n  line-height: 14px;\n  font-family: inherit;\n  border-radius: 12px;\n  background: var(--feedback_button_background);\n  color: var(--feedback_label_color);\n}\n.feedbackButtonWrapper1U-E1_1 {\n  font-size: 12px;\n  line-height: 14px;\n  padding: 8px 6px 8px 9px;\n}\n.feedbackButtonLabel-E1_1 {\n  pointer-events: none;\n}\n.feedbackButtonIcon-E1_1 {\n  display: flex;\n  height: 16px;\n  width: 16px;\n  margin-right: 7px;\n  pointer-events: none;\n}\n.feedbackButtonIcon-E1_1 svg {\n}\n.feedbackButtonIcon-E1_1 svg path {\n  fill: var(--feedback_label_color);\n}\n.overallContainer-E1_1 {\n  padding-top: 16px;\n}\n.overallContainer-E1_1.showing1uBanner {\n  max-height: 254px;\n  overflow: hidden;\n}\n.labelContainerCompact-E1_1 {\n  margin-bottom: 8px;\n  width: 100%;\n}\n.size1uCard .labelContainerCompact-E1_1 {\n  margin-bottom: 0;\n  width: 50%;\n}\n.labelContainerMobile-E1_1 {\n  padding-left: 16px;\n  padding-right: 16px;\n  display: flex;\n  justify-content: space-between;\n}\n.labelWeather-E1_1 {\n  height: 22px;\n  font-size: 14px;\n  line-height: 22px;\n  font-weight: 600;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.labelUpdatetime-E1_1 {\n  height: 16px;\n  font-size: 12px;\n  line-height: 14px;\n  color: var(--label_color_2);\n  margin: 0;\n}\n.summaryContainerCompact-E1_1 {\n  padding-left: 16px;\n}\n.size1uCard .summaryContainerCompact-E1_1 {\n  height: 86px;\n  transition: margin 0.5s;\n}\n.summaryCardShowAnimation-E1_1 {\n  margin-top: 105px;\n}\n.summaryContainerMobile-E1_1 {\n  padding-left: 16px;\n  margin-bottom: 16px;\n}\n.summaryLineGroupCompact-E1_1 {\n  display: inline-flex;\n}\n.summaryIconAndTemperatureWrapper-E1_1 {\n  overflow-y: hidden;\n  position: relative;\n  overflow-x: visible;\n  overflow-y: clip;\n  height: 72px;\n  display: flex;\n  flex-direction: column;\n}\n.summaryIconAndTemperature-E1_1 {\n  display: inline-flex;\n}\n.summaryIconAndTemperature-E1_1[data-anim=scroll-in] {\n  animation: current-condition-scroll-in 0.5s linear forwards;\n}\n.summaryIconAndTemperature-E1_1[data-anim=scroll-out] {\n  animation: current-condition-scroll-out 0.5s linear forwards;\n}\n@keyframes current-condition-scroll-in {\n  0% {\n    opacity: 0;\n    transform: translate3d(0, 0%, 0);\n  }\n  100% {\n    opacity: 1;\n    transform: translate3d(0, -100%, 0);\n  }\n}\n@keyframes current-condition-scroll-out {\n  0% {\n    opacity: 1;\n    transform: translate3d(0, 0%, 0);\n  }\n  100% {\n    opacity: 0;\n    transform: translate3d(0, -100%, 0);\n  }\n}\n.summaryLineGroupMobile-E1_1 {\n  display: inline-flex;\n}\n.summaryLineGroupMobile-E1_1 .summaryIconAndTemperature-E1_1 {\n  align-items: center;\n}\n.summaryTemperatureCompact-E1_1 {\n  margin-left: 8px;\n  font-size: 64px;\n  line-height: 62px;\n  align-items: center;\n  direction: ltr;\n  display: flex;\n}\n.summaryTemperatureCompact-E1_1 > span {\n  direction: unset;\n}\n.summaryTemperatureIcon-E1_1 {\n}\n.summaryTemperatureHover-E1_1 {\n  text-decoration: none;\n  color: inherit;\n}\n.summaryTemperatureUnit-E1_1 {\n  height: 100%;\n  font-size: 34px;\n}\n.newSummaryLabel-E1_1 {\n  box-sizing: border-box;\n  text-transform: uppercase;\n  padding-right: 10px;\n}\n.newSummaryValue-E1_1 {\n  font-weight: bold;\n  margin-right: 5px;\n  box-sizing: border-box;\n}\n.newSummaryContainer-E1_1 {\n  max-width: 200px;\n  font-size: 14px;\n  position: absolute;\n  right: 10px;\n}\n.summaryCaptionAndFeelLikeContainerCompact-E1_1 {\n  margin-left: 32px;\n}\n.summaryCaptionAndFeelLikeContainerMobile-E1_1 {\n  margin-left: 20px;\n  display: flex;\n  flex-direction: column-reverse;\n  justify-content: center;\n  align-items: flex-start;\n  font-size: 12px;\n}\n.summaryFeelLikeContainerCompact-E1_1 {\n  display: flex;\n  height: 22px;\n  color: var(--label_color);\n  font-weight: normal;\n  align-items: center;\n  padding-top: 2px;\n}\n.summaryFeelLikeContainerCompact-E1_1>*:first-child {\n  font-size: 14px;\n  line-height: 16px;\n  opacity: 0.8;\n  margin-right: 12px;\n}\n.summaryFeelLikeContainerCompact-E1_1>*:last-child {\n  font-size: 14px;\n  line-height: 22px;\n}\n.summaryTemperatureMobile-E1_1 {\n  font-size: 56px;\n  margin-left: 20px;\n}\n.summaryTemperatureMobile-E1_1>span {\n  font-size: 30px;\n  position: relative;\n  bottom: 18px;\n}\n.summaryCaptionCompact-E1_1 {\n  display: inline-block;\n  font-size: 18px;\n  line-height: 24px;\n  font-weight: 600;\n  white-space: nowrap;\n  padding-top: 17px;\n}\n.summaryCaptionMobile-E1_1 {\n  padding-bottom: 4px;\n  font-weight: 600;\n  font-size: 16px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  max-width:  150px;\n  white-space: nowrap;\n}\n.summaryDescContainer-E1_1 {\n  display: flex;\n  height: 65px;\n  align-items: center;\n  margin-bottom: 9px;\n}\n.weatherFeedbackEntryNewButton-E1_1 {\n  position: relative;\n  display: inline-block;\n  margin: 0 14px 0 8px;\n  vertical-align: middle;\n  z-index: 100;\n}\n.size1uCard .weatherFeedbackEntryNewButton-E1_1 {\n  margin: 0;\n}\n.summaryDescCompact-E1_1 {\n  font-size: 16px;\n  line-height: 22px;\n  font-weight: 350px;\n  margin: 0;\n}\n.summaryCurrentInfoSection-E1_1 {\n  display: flex;\n  box-sizing: border-box;\n  height: 100px;\n  margin-inline-start: -10px;\n  margin-bottom: 20px;\n}\n.summaryTempIconK-E1_1 {\n  display: block;\n  padding: 10px;\n  width: 80px;\n  height: 80px;\n  box-sizing: content-box;\n}\n.summaryCurCondition-E1_1 {\n  display: flex;\n  box-sizing: border-box;\n  flex-direction: column;\n  margin-inline-start: 12px;\n  padding: 6px 0;\n  height: 100px;\n}\n.summaryCurTempText-E1_1 {\n  display: flex;\n}\n.summaryCurTemperature-E1_1 {\n  font-size: 64px;\n  font-style: normal;\n  font-weight: 600;\n  line-height: 64px;\n  text-decoration: none;\n  color: inherit;\n}\n.summaryCurCondText-E1_1 {\n  margin-inline-start: 10px;\n  font-size: 32px;\n  font-style: normal;\n  font-weight: 550;\n  line-height: 48px;\n  max-width: 378px;\n  overflow: hidden;\n  white-space: pre;\n  text-overflow: ellipsis;\n}\n.summaryHlTempFeels-E1_1 {\n  display: flex;\n  font-size: 14px;\n  font-style: normal;\n  font-weight: 400;\n  line-height: 20px;\n  margin-top: 4px;\n}\n.summaryHlTempSection-E1_1 {\n  display: flex;\n}\n.summaryHighTemp-E1_1 {\n  display: flex;\n}\n.summaryHighTempLabel-E1_1 {\n  display: block;\n}\n.summaryHighTempText-E1_1 {\n  margin-inline-start: 4px;\n}\n.summaryLowTemp-E1_1 {\n  display: flex;\n  margin-inline-start: 8px;\n}\n.summaryLowTempLabel-E1_1 {\n  display: block;\n}\n.summaryLowTempText-E1_1 {\n  margin-inline-start: 4px;\n}\n.summaryFeelsLike-E1_1 {\n  margin-inline-start: 14px;\n  display: flex;\n  text-decoration: none;\n  color: inherit;\n}\n.summaryFeelsLike-E1_1:hover {\n  text-decoration: underline;\n}\n.summaryFeelsLabel-E1_1 {\n}\n.summaryFeelsValue-E1_1 {\n  margin-inline-start: 4px;\n}\n.detailContainerCompact-E1_1 {\n  padding-left: 16px;\n  margin-bottom: 16px;\n  padding-right: 16px;\n}\n.size1uCard .detailContainerCompact-E1_1 {\n  margin-top: 24px;\n}\n.detailItemLine-E1_1 {\n  display: grid;\n  grid-template-columns: repeat(3, 2fr);\n  grid-column-gap: 110px;\n  margin-bottom: 32px;\n}\n.detailItemLineCompact-E1_1 {\n  display: flex;\n  justify-content: space-between;\n  flex-wrap: wrap;\n}\n.size1uCard .detailItemLineCompact-E1_1 {\n  display: grid;\n  grid-template-columns: 74px 58px 58px 58px;\n  gap: 14px 8px;\n}\n.size1uCard .detailItemLineCompact-E1_1[data-windposition='1'] {\n  grid-template-columns: 58px 74px 58px 58px;\n}\n.size1uCard .detailItemLineCompact-E1_1[data-windposition='2'] {\n  grid-template-columns: 58px 58px 74px 58px;\n}\n.detailItemLineCompactMobile-E1_1 {\n  display: flex;\n  justify-content: space-between;\n  flex-wrap: wrap;\n  height: 48px;\n  max-width: 100%;\n  width: auto;\n  overflow: hidden;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='detailItemGridCompact'] {\n  margin-right: 10px;\n  height: 48px;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='aqiLabelHint'] {\n  position: static;\n  overflow: visible;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='detailLabelHint'] {\n  position: static;\n  overflow: visible;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='detailLabelHint'] div[class*='detailHintTips'] {\n  visibility: visible;\n  opacity: 1;\n  top: 20px;\n  transform: translateX(-50%);\n  left: 50%;\n  z-index: 901;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='aqiLabelHint'] p {\n  display: none;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='aqiLabelHint']:hover {\n}\n.detailItemLineCompactMobile-E1_1 div[class*='aqiLabelHint']:hover p {\n  top: 20px;\n  transform: translateX(-50%);\n  left: 50%;\n  display: block;\n  z-index: 20;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='detailItemGridCompact']>*:first-child {\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='detailItemGridCompact']>*:first-child>*:first-child {\n  line-height: 12px;\n}\n.detailItemLineCompactMobile-E1_1 div[class*='detailItemGridCompact']>*:first-child>*:last-child {\n  line-height: 16px;\n  margin-bottom: 10px;\n}\n.modalMask-E1_1 {\n  position: fixed;\n  top: 0;\n  left: 0;\n  height: 100%;\n  width: 100%;\n  z-index: 900;\n}\n.detailItemGridCompact-E1_1 {\n  height: 38px;\n  display: inline-flex;\n}\n.detailItemGridCompact-E1_1 > span {\n  align-self: center;\n  margin: 7px 16px 7px 0;\n  height: 24px;\n}\n:lang(hu-hu) .detailItemGridCompact-E1_1:nth-child(n+6) {\n  display: none;\n}\n.detailItemGridCompact-E1_1:nth-child(n+9) {\n  display: none;\n}\n.size1uCard :lang(hu-hu) .detailItemGridCompact-E1_1:nth-child(n+6) {\n  display: inline-flex;\n  width: 100%;\n}\n.size1uCard :lang(hu-hu) .detailItemGridCompact-E1_1:nth-child(n+6) > a {\n  width: 100%;\n}\n.detailItemGroup-E1_1 {\n  white-space: nowrap;\n}\n.size1uCard .detailItemGroup-E1_1 {\n  width: 100%;\n}\n.detailItemGroup-E1_1>*:first-child {\n  font-size: 12px;\n  font-weight: normal;\n  line-height: 16px;\n  opacity: 0.8;\n}\n.detailItemGroup-E1_1>*:last-child {\n  font-size: 16px;\n  line-height: 22px;\n  font-weight: normal;\n}\n.detailItemGroup-E1_1>*:last-child > span {\n  direction: ltr;\n  display: inline-block;\n}\n.size1uCard .detailItemGroup-E1_1>*:last-child {\n  font-size: 14px;\n  line-height: 18px;\n  font-weight: 600;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.size1uCard .detailItemGroup-E1_1>*:last-child * {\n  display: inline;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.icon_indicator-E1_1 {\n  padding-left: 5px;\n}\n.icon_indicator-E1_1 > svg {\n  opacity: 1 !important;\n}\n.icon_indicator-E1_1 svg {\n}\n.icon_indicator-E1_1 svg path {\n  fill: var(--label_color);\n}\n.aqiDetailItemGroupCompact-E1_1 {\n}\n.size1uCard .aqiDetailItemGroupCompact-E1_1 {\n  width: 100%;\n}\n.aqiDetailItemGroupCompact-E1_1>*:first-child {\n  font-size: 12px;\n  font-weight: normal;\n  line-height: 16px;\n  opacity: 1;\n}\n.aqiDetailItemGroupCompact-E1_1>*:last-child {\n  font-size: 16px;\n  line-height: 22px;\n  font-weight: normal;\n}\n.size1uCard .aqiDetailItemGroupCompact-E1_1>*:last-child {\n  font-size: 14px;\n  line-height: 18px;\n  font-weight: 600;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.aqiDetailItemGroup-E1_1 {\n}\n.aqiDetailItemGroup-E1_1>*:first-child {\n  text-transform: uppercase;\n  font-size: 13px;\n  font-weight: normal;\n  line-height: 16px;\n  opacity: 1;\n}\n.aqiDetailItemGroup-E1_1>*:last-child {\n  font-size: 16px;\n  line-height: 20px;\n  font-weight: normal;\n}\n.aqiLabelHint-E1_1 {\n  position: relative;\n  display: block;\n  overflow: hidden;\n}\n.aqiLabelHint-E1_1:hover {\n  overflow: visible;\n}\n.aqiLabelHint-E1_1>span {\n  white-space: nowrap;\n  opacity: 0.8;\n}\n.aqiLabelHint-E1_1 p {\n  color: var(--hover_label_color);\n  background-color: var(--hover_background);\n  box-shadow: var(--hover_shadow_color);\n  pointer-events: none;\n  visibility: hidden;\n  width: max-content;\n  text-align: left;\n  border-radius: 6px;\n  padding: 8px;\n  position: absolute;\n  z-index: 9999;\n  top: 5px;\n  left: 80%;\n  opacity: 0;\n  transition: opacity 0.3s;\n  display: block;\n  overflow: hidden;\n  text-transform: none;\n  font-size: 12px;\n  line-height: 20px;\n}\n.aqiLabelHint-E1_1:after {\n}\n.aqiLabelHint-E1_1:after p {\n  content: ;\n  position: absolute;\n  top: 100%;\n  left: 50%;\n  margin-left: -5px;\n  border-width: 5px;\n  border-style: solid;\n  border-color: #555 transparent transparent transparent;\n}\n.aqiLabelHint-E1_1 p>span {\n  font-weight: 600;\n}\n.size1uCard .aqiLabelHint-E1_1>span {\n  display: block;\n}\n.size1uCard .aqiLabelHint-E1_1>span:first-child {\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.aqiLabelHint-E1_1:hover p {\n  visibility: visible;\n  opacity: 1;\n}\n.pollenValueContent-E1_1 {\n  display: block;\n  max-width: 160px;\n}\n.size1uCard .pollenValueContent-E1_1 {\n  display: flex;\n  align-items: center;\n}\n.size1uCard .pollenValueContent-E1_1>*:first-child {\n  display: block;\n}\n.size1uCard .pollenValueContent-E1_1>*:last-child {\n  display: block;\n  width: 44px;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  overflow: hidden;\n}\n.detailLabelHint-E1_1 {\n  position: relative;\n  display: block;;\n  opacity: 1 !important;\n  overflow: hidden;\n}\n.detailLabelHint-E1_1:hover {\n  overflow: visible;\n}\n.detailLabelHint-E1_1:hover .detailHintTips-E1_1 {\n  visibility: visible;\n  opacity: 1;\n}\n.detailLabelHint-E1_1>span {\n  opacity: 0.8;\n}\n.detailLabelHint-E1_1:after .detailHintTips-E1_1 {\n  content: ;\n  position: absolute;\n  top: 100%;\n  left: 50%;\n  margin-left: -5px;\n  border-width: 5px;\n  border-style: solid;\n  border-color: #555 transparent transparent transparent;\n}\n.size1uCard .detailLabelHint-E1_1>span {\n  display: block;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.detailHintTips-E1_1 {\n  pointer-events: none;\n  visibility: hidden;\n  width: max-content;\n  height: auto;\n  background-color: var(--hover_background);\n  color: var(--hover_label_color);\n  text-align: left;\n  border-radius: 6px;\n  padding: 8px;\n  position: absolute;\n  z-index: 20;\n  top: 5px;\n  left: 80%;\n  opacity: 0;\n  transition: opacity 0.3s;\n  display: block;\n  overflow: hidden;\n  text-transform: none;\n  font-size: 12px;\n  line-height: 20px;\n  white-space: normal;\n  box-shadow: var(--hover_shadow_color);\n}\n.detailHintTips-E1_1 >span {\n  font-weight: 600;\n}\n.detailHintTips-E1_1 >div {\n  height: auto;\n  max-width: 200px;\n}\n.slugInfoSpace-E1_1 {\n  margin-left: 3px;\n  cursor: inherit;\n  display: inline-block;\n  width: 12px;\n  height: 12px;\n}\n.size1uCard .slugInfoSpace-E1_1 {\n  display: none !important;\n}\n.slugInfoSpace-E1_1 svg {\n}\n.slugInfoSpace-E1_1 svg path {\n  fill: var(--label_color);\n}\n@media (-ms-high-contrast:active) {\n  .slugInfoSpace-E1_1 svg path {\n    fill: ButtonText;\n  }\n}\n.aqiColorCycle-E1_1 {\n  opacity: 1 !important;\n}\n.aqiDetailItemGroupHoverCompact-E1_1 {\n  text-decoration: none;\n  color: var(--label_color);\n}\n.aqiDetailItemGroupHoverCompact-E1_1:hover {\n}\n.aqiDetailItemGroupHoverCompact-E1_1:hover>*:first-child {\n}\n.aqiDetailItemGroupHoverCompact-E1_1:hover>*:first-child> span {\n  text-decoration: underline;\n}\n.detailItemGroupHover-E1_1 {\n  text-decoration: none;\n  color: var(--label_color);\n}\n.detailItemGroupHover-E1_1:hover {\n}\n.detailItemGroupHover-E1_1:hover>*:first-child {\n  text-decoration: underline;\n}\n.weather_feedback_root-E1_1 {\n  position: relative;\n  display: block;\n}\n.linkMoreArrow-E1_1 {\n}\n.linkMoreArrow-E1_1:after {\n  content: '';\n  width: 5px;\n  height: 5px;\n  margin: 0 1px 1px;\n  display: inline-block;\n  border: rgba(255,255,255,0.8) solid;\n  border-width: 1px 1px 0 0;\n  transform: rotate(45deg);\n}\n.feedbackRow-E1_1 {\n  display: flex;\n  height: 36px;\n  width: 100%;\n  justify-content: flex-end;\n}\n.headerRow-E1_1 {\n  display: flex;\n  padding: 0 16px;\n  justify-content: space-between;\n}\n.weatherWatcherBannerWrapper-E1_1 {\n  display: flex;\n  box-sizing: border-box;\n  height: 44px;\n  margin: -4px 0 6px 0;\n  padding: 0 12px 0 12px;\n}\n.u1SummaryContainer-E1_1 {\n  display: flex;\n  align-items: center;\n}\n.u1SummaryTextContainer-E1_1 {\n  display: flex;\n  flex-direction: column;\n  align-items: flex-start;\n  margin-inline-start: 6px;\n}\n.u1TempUnitContainer-E1_1 {\n  display: flex;\n  align-items: flex-start;\n}\n.u1SummaryTemperatureCompact-E1_1 {\n  font-size: 48px;\n  line-height: 64px;\n  font-weight: 600;\n  direction: ltr;\n  text-decoration: none;\n  color: inherit;\n  display: flex;\n  align-items: flex-start;\n}\n.u1SummaryTemperatureCompact-E1_1 > span {\n  direction: unset;\n}\n.u1SummaryTemperatureUnit-E1_1 {\n  font-weight: 500;\n  font-size: 15px;\n  line-height: 20px;\n  padding-top: 14px;\n}\n.u1SummaryCaptionCompact-E1_1 {\n  display: block;\n  font-size: 18px;\n  line-height: 22px;\n  font-weight: 600;\n  white-space: nowrap;\n}\n.size1uCard .u1SummaryCaptionCompact-E1_1 {\n  max-width: 200px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.icon-E1_1 {\n  max-height: 100%;\n  max-width: 100%;\n}\n.inline-E1_1 {\n}\n.inline-E1_1.inline-E1_1 {\n  width: auto;\n  height: auto;\n}\n.weather_alerts_root-E1_1 {\n  display: flex;\n  align-items: center;\n  color: #FFFFFF;\n  font-size: 14px;\n  position: relative;\n  left: 369px;\n  margin-left: 35px;\n  flex: 1;\n  white-space: nowrap;\n}\n.weather_alerts_level-E1_1 {\n  display: flex;\n  align-items: center;\n  font-weight: 500;\n  line-height: 20px;\n  font-size: 12px;\n  padding: 5px 12px 5px 12px;\n  border-radius: 16px;\n  cursor: pointer;\n}\n.weather_alerts_level-E1_1 :first-child {\n  height: 16px;\n}\n.weather_alerts_default-E1_1 {\n  padding: 6px 12px;\n  border-radius: 16px;\n  display: flex;\n  align-items: center;\n  background: rgba(255, 255, 255, 0.05);\n  backdrop-filter: blur(60px);\n  font-weight: 600;\n  line-height: 17px;\n  font-size: 14px;\n  color: #ffffff;\n}\n.weather_alerts_default-E1_1 path {\n  fill: #ffffff;\n}\n.weather_alerts_color_y-E1_1 {\n  background-color: #FFD02C;\n  color: #795700;\n}\n.weather_alerts_color_y-E1_1 svg {\n  fill: #795700;\n}\n.weather_alerts_color_o-E1_1 {\n  background-color: #E67E22;\n}\n.weather_alerts_color_o-E1_1 svg {\n  fill: #FFFFFF;\n}\n.weather_alerts_color_r-E1_1 {\n  background-color: #E74C3C;\n}\n.weather_alerts_color_r-E1_1 svg {\n  fill: #FFFFFF;\n}\n.weather_alerts_color_b-E1_1 {\n  background-color: #3967E4;\n}\n.weather_alerts_color_b-E1_1 svg {\n  fill: #FFFFFF;\n}\n.weather_alerts_logo-E1_1 {\n  margin-right: 5px;\n}\n.weather_alerts_title-E1_1 {\n  color: #FFFFFF;\n  margin: 0 12px 0 12px;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n}\n.weather_alerts_link-E1_1 {\n  position: relative;\n  text-decoration: none;\n  height: 16px;\n  margin-bottom: 2px;\n  padding-bottom: 1px;\n}\n.weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1 {\n  color: #FFFFFF;\n}\n.weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1:hover, .weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1:link, .weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1:visited {\n  color: #FFFFFF;\n  border-bottom: 1px solid white;\n}\n.weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1.weather_alerts_link-E1_1:focus {\n  color: #FFFFFF;\n  outline: rgb(16, 16, 16) auto 1px;\n}\n.weather_alerts_anchor-E1_1 {\n  text-decoration: none;\n  color: inherit;\n}\n.location_name_set_home_icon-E1_1 {\n  width: 26px;\n  height: 26px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  padding: 0px;\n  margin: 0;\n  position: relative;\n  margin-left: 16px;\n}\n.location_name_set_home_icon-E1_1>*:first-child {\n  position: relative;\n  top: -0.5px;\n}\n.location_name_set_home_icon-E1_1>*:nth-child(2) {\n  display: none;\n}\n.location_name_set_home_icon-E1_1:focus {\n  outline: none;\n}\n.location_name_set_home_icon-E1_1:focus-visible {\n  outline: rgb(16, 16, 16) auto 1px;\n}\n.location_name_set_home_button-E1_1 {\n  position: relative;\n  display: flex;\n  flex-shrink: 0;\n  align-items: center;\n  justify-content: center;\n  background: transparent;\n  border: var(--button_border);\n  outline: none;\n  padding: 0;\n  width: 24px;\n  height: 24px;\n  cursor: pointer;\n  margin-inline-start: 16px;\n  border-radius: 50%;\n}\n.location_name_set_home_button-E1_1:hover {\n  background: rgba(255, 255, 255, 0.1);\n}\n.location_name_set_home_button-E1_1 svg > path {\n  fill: var(--button_color);\n}\n.location_icon-E1_1 {\n  margin-right: 11px;\n}\n@media screen and (forced-colors: active) and (prefers-color-scheme: dark) {\n  .location_icon-E1_1 {\n    fill: #FFF !important;\n  }\n}\n@media screen and (forced-colors: active) and (prefers-color-scheme: light) {\n  .location_icon-E1_1 {\n    fill: #000 !important;\n  }\n}\n.location_icon-E1_1 svg > path {\n  fill: var(--button_color);\n}\n.location_name_label-E1_1 {\n  color: var(--label_color);\n  line-height: 24px;\n  text-decoration: none;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  overflow: hidden;\n}\n.location_name_link-E1_1 {\n  cursor: inherit;\n}\n.location_name_link-E1_1:hover, .location_name_link-E1_1:active {\n  outline: none;\n}\n.location_name_link-E1_1:hover, .location_name_link-E1_1:focus, .location_name_link-E1_1:active {\n  text-decoration: underline;\n  text-underline-position: under;\n}\n.plain_text-E1_1 {\n  cursor: text !important;\n  text-decoration: none !important;\n}\n.location_name_main_button-E1_1 {\n  border: none;\n  cursor: pointer;\n  background: inherit;\n  color: unset;\n  padding: 0;\n}\n.location_name_detect_icon-E1_1 {\n  position: relative;\n  top: 1px;\n}\n.location_name_detect_icon-E1_1 svg > path {\n  fill: var(--label_color);\n}\n.location_name_container-E1_1 {\n  display: flex;\n  height: 24px;\n  position: relative;\n  align-items: center;\n  max-width: 612px;\n  cursor: pointer;\n  overflow: hidden;\n}\n.location_name_container-E1_1 path {\n}\n.location_name_container-E1_1 rect {\n}\n.location_name_container-E1_1[data-theme='dark'] {\n  --label_color: #fff;\n  --button_border: 1px solid rgba(255, 255, 255, 0.8);\n  --button_border_2: #fff solid;\n  --button_color: rgba(255, 255, 255, 0.8);\n}\n.location_name_container-E1_1[data-theme='light'] {\n  --label_color: #000;\n  --button_border: 1px solid rgba(0, 0, 0, 0.8);\n  --button_border_2: #000 solid;\n  --button_color: rgba(0, 0, 0, 0.8);\n}\n@media(prefers-color-scheme: dark) {\n  .location_name_container-E1_1 {\n  }\n  .location_name_container-E1_1[data-theme='light'] {\n    --label_color: #fff;\n    --button_border: 1px solid rgba(255, 255, 255, 0.8);\n    --button_border_2: #fff solid;\n    --button_color: rgba(255, 255, 255, 0.8);\n  }\n}\n@media (-ms-high-contrast:active) {\n  .location_name_container-E1_1 rect {\n    stroke: ButtonText;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .location_name_container-E1_1 path {\n    fill: ButtonText !important;\n  }\n}\n.location_change_button-E1_1 {\n  width: 9px;\n  height: 9px;\n  display: block;\n  margin-top: -4px;\n  border: var(--button_border_2);\n  border-width: 1px 1px 0 0;\n  transform: rotate(135deg);\n}\n.location_change_button_container-E1_1 {\n  margin-left: 8px;\n  width: 15px;\n}\n.weather_alerts_root-E1_2 {\n  left: unset;\n  right: unset;\n}\n:focus-visible {\n  outline: rgb(16, 16, 16) auto 1px;\n}\n#ForecastDays {\n  z-index: 1;\n}\n.weatherOverview_root-E1_1 {\n  border-radius: 6px;\n  position: relative;\n  width: 620px;\n  height: 310px;\n  color: var(--label_color);\n}\n.weatherOverview_root-E1_1.size1uCard {\n  width: 300px;\n}\n.weatherOverview_root-E1_1[data-theme='dark'] {\n  --container_background: rgba(255, 255, 255, 0.05);\n  --label_color: #FFF;\n  --label_color_2: rgba(255,255,255,0.7);\n  --feedback_button_background: rgba(255, 255, 255, 0.09);\n  --feedback_label_color: rgba(184, 209, 255, 1);\n  --hover_label_color: rgba(255, 255, 255, 1);\n  --hover_background: rgba(41, 41, 41, 1);\n  --hover_shadow_color: 0px 0px 2px 0px rgba(0, 0, 0, 0.12), 0px 8px 16px 0px rgba(0, 0, 0, 0.14);\n  --card_border_color_k: rgba(255, 255, 255, 0.05);\n}\n.weatherOverview_root-E1_1[data-theme='light'] {\n  --container_background: rgba(255, 255, 255, 0.8);\n  --label_color: #000;\n  --label_color_2: rgba(0, 0, 0, 0.7);\n  --feedback_button_background: rgba(0, 0, 0, 0.03);\n  --feedback_label_color: rgba(43, 80, 243, 1);\n  --hover_label_color: rgba(68, 68, 68, 1);\n  --hover_background: rgba(255, 255, 255, 1);\n  --hover_shadow_color: 0px 0px 2px 0px rgba(0, 0, 0, 0.12), 0px 8px 16px 0px rgba(0, 0, 0, 0.14);\n  --card_border_color_k: rgba(0, 0, 0, 0.1);\n}\n@media(prefers-color-scheme: dark) {\n  .weatherOverview_root-E1_1 {\n  }\n  .weatherOverview_root-E1_1[data-theme='light'] {\n    --container_background: rgba(255, 255, 255, 0.05);\n    --label_color: #FFF;\n    --label_color_2: rgba(255,255,255,0.7);\n    --feedback_button_background: rgba(255, 255, 255, 0.09);\n    --feedback_label_color: rgba(184, 209, 255, 1);\n    --hover_label_color: rgba(255, 255, 255, 1);\n    --hover_background: rgba(41, 41, 41, 1);\n    --hover_shadow_color: 0px 0px 2px 0px rgba(0, 0, 0, 0.12), 0px 8px 16px 0px rgba(0, 0, 0, 0.14);\n    --card_border_color_k: rgba(255, 255, 255, 0.05);\n  }\n}\n.locationNameContainer-E1_1 {\n  display: flex;\n  height: 24px;\n  position: relative;\n  width: var(--weather-container-width);\n  margin-bottom: 16px;\n}\n.backgroundContainerCompact-E1_1 {\n  height: 270px;\n  border-radius: 6px;\n  background-color: var(--container_background);\n}\n.backgroundContainerFull-E1_1 {\n  height: 620px;\n  border-radius: 6px;\n}\n.weatherSsrLayoutContainer-E1_1 {\n  display: grid;\n  gap: 20px;\n  align-items: end;\n}\n@media (max-width: 979px) {\n  .weatherSsrLayoutContainer-E1_1 {\n    grid-template-columns: 1fr;\n    grid-template-rows: 310px 270px;\n  }\n}\n@media (min-width: 980px) and (max-width: 1303px) {\n  .weatherSsrLayoutContainer-E1_1 {\n    grid-template-columns: 1fr 1fr 1fr;\n    grid-template-rows: auto;\n  }\n}\n@media (min-width: 1304px) and (max-width: 1627px) {\n  .weatherSsrLayoutContainer-E1_1 {\n    grid-template-columns: 1fr 1fr;\n    grid-template-rows: auto;\n  }\n}\n@media (min-width: 1628px) {\n  .weatherSsrLayoutContainer-E1_1 {\n    grid-template-columns: 1fr 1fr;\n    grid-template-rows: auto;\n  }\n}\n.weatherOverviewV2Container-E1_1 {\n  grid-area: 1 / 1 / 2 / 2;\n}\n.weatherMapMiniContainer-E1_1 {\n}\n@media (max-width: 979px) {\n  .weatherMapMiniContainer-E1_1 {\n    grid-area: 2 / 1 / 3 / 2;\n  }\n}\n@media (min-width: 980px) and (max-width: 1303px) {\n  .weatherMapMiniContainer-E1_1 {\n    grid-area: 1 / 2 / 2 / 3;\n  }\n}\n@media (min-width: 1304px) and (max-width: 1627px) {\n  .weatherMapMiniContainer-E1_1 {\n    grid-area: 1 / 2 / 2 / 3;\n  }\n}\n@media (min-width: 1628px) {\n  .weatherMapMiniContainer-E1_1 {\n    grid-area: 1 / 2 / 2 / 3;\n  }\n}\n.aboveFolderContainer-E1_1 {\n  position: relative;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.00);\n}\n#weatherhomepage .aboveFolderContainer-E1_1 {\n  --customized-native-ad-card-fill-color: #FFFFFF;\n  --customized-native-ad-card-secondary-color: #FFFFFF;\n  --customized-native-ad-card-text-color: #000000;\n  --customized-native-ad-card-bg: #FFFFFF;\n}\n#weatherhomepage [class*='newsTitleNightMode'] .aboveFolderContainer-E1_1 {\n  --customized-native-ad-card-fill-color: #565656;\n  --customized-native-ad-card-secondary-color: #565656;\n  --customized-native-ad-card-text-color: #FFFFFF;\n  --customized-native-ad-card-bg: rgba(255, 255, 255, 0.08);\n}\n.scrollForFourColsForwards-E1_1 {\n  -webkit-animation: scroll 1s ease forwards 1;\n  animation: scrollForwards 1s ease forwards 1;\n}\n@keyframes scrollForwards {\n  0% {\n    -webkit-transform: translateX(0);\n    transform: translateX(0);\n  }\n  100% {\n    -webkit-transform: translateX(-300px);\n    transform: translateX(-300px);\n  }\n}\n.scrollForFourColsBackwards-E1_1 {\n  -webkit-animation: scrollBackWards 1s ease forwards 1;\n  animation: scrollBackWards 1s ease forwards 1;\n}\n@keyframes scrollBackWards {\n  0% {\n    -webkit-transform: translateX(-300px);\n    transform: translateX(-300px);\n  }\n  100% {\n    -webkit-transform: translateX(0);\n    transform: translateX(0);\n  }\n}\n.chevronBtn-E1_1 {\n  position: absolute;\n  z-index: 1;\n  padding: 12px;\n  top: 292px;\n  right: -23px;\n  cursor: pointer;\n  overflow: hidden;\n  white-space: nowrap;\n  text-overflow: ellipsis;\n  display: inline-flex;\n  justify-content: flex-end;\n  align-items: center;\n  height: 48px;\n  width: 48px;\n  border-radius: 24px;\n  border: 0.5px solid var(--Stroke-Neutral-Grey, rgba(0, 0, 0, 0.10));\n  background: var(--Background-Special-Button, #FFF);\n  box-shadow:  0px 0px 2px 0px rgba(0, 0, 0, 0.12), 0px 2px 4px 0px rgba(0, 0, 0, 0.14);\n}\n.chevronBtn-E1_1:focus-visible {\n  outline: -webkit-focus-ring-color auto 1px;\n}\n@media (-ms-high-contrast:active) {\n  .chevronBtn-E1_1 {\n    border: 1px solid Highlight;\n  }\n}\n.bannerAdsContainer-E1_1 {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  margin-top: 32px;\n  height: 104px !important;\n}\n.weatherTeaserLayoutContainer-E1_1 {\n  display: grid;\n  gap: 72px;\n  align-items: end;\n  grid-template-columns: 1fr;\n  grid-template-rows: auto auto;\n}\n.unitContainerSSR-E1_1 {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 4px;\n  font-size: 20px;\n  line-height: 23px;\n  color: #FFFFFF;\n  width: 36px;\n  height: 36px;\n  display: flex;\n  align-items: center;\n  justify-content: space-around;\n  cursor: pointer;\n  position: relative;\n}\n.unitTextSSR-E1_1 {\n  background: none;\n  border: none;\n  padding: 0;\n  font-size: 18px;\n  line-height: 21px;\n  color: #FFFFFF;\n  cursor: pointer;\n  width: 100%;\n  height: 100%;\n  display: flex;\n  border-radius: 4px;\n  justify-content: space-evenly;\n  align-items: center;\n}\n.triangleSSR-E1_1 {\n  width: 7px;\n  display: flex;\n}\n.textField-E1_1 {\n  font-family: inherit;\n  box-sizing: border-box;\n  margin: 0;\n  transition: all 0.2s ease-in-out;\n  font-size: 14px;\n  line-height: 20px;\n  font-weight: 400;\n  background: #FFFFFF;\n  border: 1px solid #BEBEBE;\n  color: #2B2B2B;\n  padding: 0 11px;\n  border-radius: 2px;\n  height: 32px;\n}\n.textField-E1_1:hover:enabled {\n  background: #FFFFFF;\n  border-color: #979797;\n}\n.textField-E1_1:active:enabled {\n  background: #FFFFFF;\n  border-color: #D6D6D6;\n}\n.textField-E1_1:focus:enabled {\n  outline: none;\n  box-shadow: 0 0 0 1px #888888 inset;\n  border-color: #888888;\n}\n.textField-E1_1:disabled {\n  cursor: not-allowed !important;\n  opacity: 0.3;\n}\n.textField-E1_1::placeholder {\n  color: #767676;\n}\n@media (-ms-high-contrast:active) {\n  .textField-E1_1 {\n    -ms-high-contrast-adjust: none;\n    background: ButtonFace;\n    border-color: ButtonText;\n    color: ButtonText;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .textField-E1_1::placeholder {\n    color: GrayText;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .textField-E1_1:disabled {\n    opacity: 1;\n    background: ButtonFace !important;\n    color: GrayText !important;\n    fill: GrayText !important;\n    border-color: GrayText !important;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .textField-E1_1:focus:enabled {\n    border-color: ButtonText;\n    box-shadow: 0 0 0 1px inset ButtonText;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .textField-E1_1:hover:enabled {\n    background: ButtonFace;\n    border: 1px solid Highlight;\n  }\n}\n.textField__filled-E1_1 {\n  font-family: inherit;\n  box-sizing: border-box;\n  margin: 0;\n  transition: all 0.2s ease-in-out;\n  font-size: 14px;\n  line-height: 20px;\n  font-weight: 400;\n  background: #EDEDED;\n  border: 1px solid transparent;\n  color: #2B2B2B;\n  padding: 0 11px;\n  border-radius: 2px;\n}\n.textField__filled-E1_1:hover:enabled {\n  border-color: transparent;\n  background: #E5E5E5;\n}\n.textField__filled-E1_1:active:enabled {\n  border-color: transparent;\n}\n.textField__filled-E1_1:focus:enabled {\n  border-color: #888888;\n}\n.textField__filled-E1_1:disabled {\n  cursor: not-allowed !important;\n  opacity: 0.3;\n}\n.textField__filled-E1_1::placeholder {\n  color: #6B6B6B;\n}\n@media (-ms-high-contrast:active) {\n  .textField__filled-E1_1 {\n    -ms-high-contrast-adjust: none;\n    background: ButtonFace;\n    border-color: ButtonText;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .textField__filled-E1_1::placeholder {\n    color: GrayText;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .textField__filled-E1_1:disabled {\n    opacity: 1;\n    background: ButtonFace !important;\n    color: GrayText !important;\n    fill: GrayText !important;\n    border-color: GrayText !important;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .textField__filled-E1_1:hover:enabled {\n    background: ButtonFace;\n    border: 1px solid Highlight;\n  }\n}\n.suggestionContainer-E1_1 {\n  display: flex;\n  flex-direction: row;\n  margin-left: 5px;\n}\n.suggestions-E1_1 {\n  position: absolute;\n  left: 3px;\n  z-index: 100;\n  background: #FFFFFF;\n  border: 1px solid #E5E5E5;\n  width: 200px;\n  display: block;\n  height: auto;\n  overflow-y: auto;\n  margin-top: 32px;\n  border-bottom-left-radius: 16px;\n  border-bottom-right-radius: 16px;\n  padding-left: 4px;\n}\n.suggestions-E1_1 > li {\n  padding: 2px;\n  list-style: none;\n  display: block;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  overflow: hidden;\n  text-align: left;\n  font-size: 14px;\n  line-height: 20px;\n}\n.suggestions-E1_1 > li:hover, .suggestions-E1_1 > li:focus {\n  cursor: pointer;\n  background: #f1f1f1;\n}\n.suggestions-E1_1 > li > button {\n  text-align: left;\n  height: auto!important;\n  background: transparent;\n  color: unset;\n  width: 100%;\n  margin: 0;\n  padding: 0;\n  cursor: pointer;\n  display: flex;\n  justify-content: space-between;\n}\n.suggestionActive-E1_1 {\n  background-color: #f1f1f1;\n}\n@media (-ms-high-contrast:active) {\n  .suggestionActive-E1_1 {\n    outline: 1px solid;\n  }\n}\n.toolTipContainer-E1_1 {\n  align-items: center;\n  padding: 6px 8px 8px 8px;\n  position: absolute;\n  width: 100%;\n  top: 100%;\n  margin-top: 4px;\n  background: #E0E0E0;\n  box-sizing: border-box;\n  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);\n  border-radius: 6px;\n}\n.firstLineToolTip-E1_1 {\n  font-weight: 500;\n  font-size: 12px;\n  word-wrap: break-word;\n  line-height: 16px;\n}\n.secondLineToolTip-E1_1 {\n  font-size: 12px;\n  word-wrap: break-word;\n  line-height: 14px;\n}\n.suggestion-E1_1 {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n}\n.suggestionText-E1_1 {\n  display: flex;\n  flex-direction: column;\n  width: calc(100% - 30px);\n}\n.firstLine-E1_1 {\n  font-size: 16px;\n  line-height: 22px;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  overflow: hidden;\n  font-weight: 500;\n  display: block;\n}\n.secondLine-E1_1 {\n  font-size: 14px;\n  font-weight: 400;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n  line-height: 22px;\n  display: block;\n}\n.searchIcon-E1_1 {\n  border: none;\n  outline: none;\n  border-radius: 4px !important;\n  padding: 0px !important;\n  background: inherit;\n  min-width: 16px;\n  width: 16px !important;\n  margin-right: 20px !important;\n}\n.searchIcon-E1_1> span:nth-child(1) {\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n  width: 100%;\n  height: 100%;\n  outline: none;\n}\n.searchIcon-E1_1:focus {\n  outline: none;\n}\n.searchIcon-E1_1:focus-visible {\n  outline: rgb(16, 16, 16) auto 1px;\n}\n.clearAllIconContainer-E1_1 {\n  border: none;\n  outline: none;\n  cursor: pointer;\n  padding: 0px !important;\n  background: none;\n  height: 12px;\n  width: 15px;\n  margin-right: 10px !important;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n.clearAllIconContainer-E1_1:focus {\n  outline: none;\n}\n.clearAllIconContainer-E1_1:focus-visible {\n  border-radius: 4px !important;\n  outline: rgb(16, 16, 16) auto 1px;\n}\n.clearAllIconSvg-E1_1 {\n  height: 12px;\n  width: 15px;\n}\n.clearAllIconSvg-E1_1 path {\n  border: none;\n  fill: black;\n  display: inline-block;\n  height: 12px;\n  width: 15px;\n  font-size: 12px;\n}\n@media (forced-colors: active) and (prefers-color-scheme: dark) {\n  .clearAllIconSvg-E1_1 {\n  }\n  .clearAllIconSvg-E1_1 path {\n    fill: #FFF;\n  }\n}\n@media (forced-colors: active) and (prefers-color-scheme: light) {\n  .clearAllIconSvg-E1_1 {\n  }\n  .clearAllIconSvg-E1_1 path {\n    fill: #000;\n  }\n}\n.textField-E1_2 {\n  -webkit-appearance: none;\n  -moz-appearance: none;\n  appearance: none;\n  border: 0px;\n  background: #FFFFFF;\n  width: 100%;\n  height: 40px;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n  border-radius: 0px;\n  border-top: solid 1px #9F9F9F;\n  border-bottom: solid 1px #9F9F9F;\n}\n.textField-E1_2:hover:enabled {\n  border: 0px;\n  border-top: solid 1px #9F9F9F;\n  border-bottom: solid 1px #9F9F9F;\n  box-shadow: none;\n  webkit-box-shadow: none;\n  moz-box-shadow: none;\n}\n.textField-E1_2:active:enabled {\n  border: 0px;\n  border-top: solid 1px #9F9F9F;\n  border-bottom: solid 1px #9F9F9F;\n  background: #FFFFFF;\n  box-shadow: none;\n  webkit-box-shadow: none;\n  moz-box-shadow: none;\n}\n.textField-E1_2:focus:enabled {\n  box-shadow: none;\n  border: 0px;\n  border-top: solid 1px #9F9F9F;\n  border-bottom: solid 1px #9F9F9F;\n  background: #FFFFFF;\n  webkit-box-shadow: none;\n  moz-box-shadow: none;\n}\n.textField-E1_2::placeholder {\n  color: black;\n}\n.wls_root-E1_1 {\n  position: relative;\n}\n.searchRoot-E1_1 {\n  border-radius: 6px;\n  padding: 15px;\n  border: 0px solid;\n  font-size: 14px;\n  line-height: 20px;\n  position: relative;\n  color: #2B2B2B;\n}\n.searchRootV2-E1_1 {\n  font-size: 14px;\n  line-height: 20px;\n  position: relative;\n  overflow: hidden;\n  color: #2B2B2B;\n}\n.searchRoot_onlybox-E1_1 {\n  border-radius: 6px;\n  padding: 0;\n  border: 0px solid;\n  font-size: 14px;\n  line-height: 20px;\n  position: relative;\n  color: #2B2B2B;\n}\n.inputBox-E1_1 {\n  position: relative;\n  display: flex;\n}\n.suggestionContainer-E1_2 {\n  right: 0px;\n  padding-right: 10px;\n  border: 1px solid var(--card_border);\n  border-radius: 12px;\n  box-sizing: border-box;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  position: relative;\n  width: 281px;\n  box-shadow: none;\n  color: unset;\n  backdrop-filter: none;\n  background-color: var(--background_color);\n  border-color: var(--card_border);\n  margin-left: 0px !important;\n  flex-wrap: wrap;\n  padding: 0;\n}\n.suggestionContainer-E1_2 input, .suggestionContainer-E1_2 input:focus:enabled, .suggestionContainer-E1_2 input:hover:enabled, .suggestionContainer-E1_2 input:active:enabled {\n  border-top: none;\n  border-bottom: none;\n  width: calc(100% - 69px);\n  height: 40px;\n  margin-left: 6px;\n}\n.suggestionContainer-E1_2 img {\n  border-top: none;\n  border-bottom: none;\n  height: 16px;\n  margin: 7px 0px 7px 0px;\n}\n.suggestionContainer-E1_2 button {\n  height: 32px;\n  margin-right: 20px;\n  border: none;\n}\n.weather-L1 .suggestionContainer-E1_2 input {\n  background: none !important;\n  padding: 0 10px;\n  font-size: 14px;\n}\n.suggestionContainer-E1_2 li[role='option']:hover {\n}\n.suggestionContainer-E1_2 div[class*='searchIcon'] svg>path {\n  fill: #919191;\n}\n.suggestionContainer-E1_2 > div {\n  width: 100%;\n}\n.suggestionContainer-E1_2 input {\n  background: none !important;\n  padding: 0 10px;\n  color: var(--location_color)!important;\n}\n@media (-ms-high-contrast:active) {\n  .suggestionContainer-E1_2 input {\n    -ms-high-contrast-adjust: auto;\n  }\n}\n.suggestionContainer-E1_2 input::placeholder {\n  color: var(--location_color);\n}\n.suggestionContainer-E1_2 input::-ms-input-placeholder {\n  color: var(--location_color);\n}\n@media (-ms-high-contrast:active) {\n  .suggestionContainer-E1_2 li[role='option']:hover {\n    outline: #FFFFFF auto 1px;\n  }\n}\n.suggestions-E1_2 {\n  background-color: var(--popup_background);\n  width: 281px;\n  left: -17px;\n}\n.suggestions-E1_2 {\n  position: relative;\n  margin: 0;\n  border: none;\n  border-top: 1px solid var(--card_border);\n  padding: 10px 0px;\n  width: 100%;\n  border-bottom-left-radius: 12px;\n  border-bottom-right-radius: 12px;\n  left: 0px;\n}\n.suggestions-E1_2 div[class*='noResultContainer'] div {\n  color: var(--popup_location_color);\n}\n.suggestions-E1_2 li {\n  padding: 6px 16px;\n}\n.suggestions-E1_2 > li:hover, .suggestions-E1_2 > li:focus {\n  cursor: pointer;\n  background: var(--popup_item_hover);\n}\n.suggestions-E1_2 li button {\n  color: var(--popup_location_color);\n}\n.suggestions-E1_2 li path {\n  fill: var(--popup_location_color);\n}\n.suggestions-E1_2 li rect {\n}\n@media (-ms-high-contrast:active) {\n  .suggestions-E1_2 li rect {\n    stroke: #919191;\n  }\n}\n@media (-ms-high-contrast:active) {\n  .suggestions-E1_2 li path {\n    fill: #919191;\n  }\n}\n.suggestionActive-E1_2 {\n  background: var(--popup_item_hover);\n}\n@media (-ms-high-contrast:active) {\n  .suggestionActive-E1_2 {\n    border: 1px solid;\n  }\n}\n.background-E1_1 {\n  background: rgba(255, 255, 255, 0.08);\n}\n.backgroundHP20-E1_1 {\n  display: flex;\n  align-items: center;\n  height: 72px;\n}\n.backgroundHP20-E1_1[data-theme='dark'] {\n  --container_background_active: rgba(255, 255, 255, 0.15);\n  --container_background: rgba(255, 255, 255, 0.05);\n  --theme_base_10: rgba(255, 255, 255, 1);\n  --theme_base_05: rgba(255, 255, 255, 0.1);\n  --location_color: rgba(255, 255, 255, 0.9);\n  --card_border: 1px solid rgba(250, 249, 248, 0.15);\n  --popup_background: #292929;\n  --popup_item_hover: rgba(255, 255, 255, 0.15);\n}\n.backgroundHP20-E1_1[data-theme='light'] {\n  --theme_base_10: rgba(0, 0, 0, 1);\n  --theme_base_05: rgba(0, 0, 0, 0.05);\n  --container_background: rgba(255, 255, 255, 0.6);\n  --container_background_active: rgba(255, 255, 255, 1);\n  --location_color: #323130;\n  --card_border: none;\n  --popup_background: #fff;\n  --popup_item_hover: #EFEFEF;\n}\n@media(prefers-color-scheme: dark) {\n  .backgroundHP20-E1_1 {\n    background: rgba(0, 0, 0, 0.36);\n  }\n  .backgroundHP20-E1_1[data-theme='light'] {\n    --container_background_active: rgba(255, 255, 255, 0.15);\n    --container_background: rgba(255, 255, 255, 0.05);\n    --theme_base_10: rgba(255, 255, 255, 1);\n    --theme_base_05: rgba(255, 255, 255, 0.1);\n    --location_color: rgba(255, 255, 255, 0.9);\n    --card_border: 1px solid rgba(250, 249, 248, 0.15);\n    --popup_background: #292929;\n    --popup_item_hover: rgba(255, 255, 255, 0.15);\n  }\n}\n.backgroundFilter-E1_1 {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  backdrop-filter: blur(12px);\n  -webkit-backdrop-filter: blur(12px);\n}\n.scrollContainer-E1_1 {\n  display: flex;\n  flex: 1 1 auto;\n  align-items: stretch;\n  width: 100%;\n}\n.scrollContainer-E1_1 button[class*='temperatureButton'] {\n  forced-color-adjust: none;\n}\ndiv[class^='locationSearchContainer'] + .scrollContainer-E1_1 {\n  width: 924px;\n}\n@media (min-width: 656px) and (max-width: 979px) {\n  div[class^='locationSearchContainer'] + .scrollContainer-E1_1 {\n    width: 300px;\n  }\n}\n@media (min-width: 980px) and (max-width: 1303px) {\n  div[class^='locationSearchContainer'] + .scrollContainer-E1_1 {\n    width: 612px;\n  }\n}\n.scrollContainerHP20-E1_1 {\n  height: 40px;\n}\n.scrollContainerHP20-E1_1 div[class*='unitContainerSSR'] {\n  background: var(--container_background);\n  border-radius: 12px;\n  font-size: 20px;\n  line-height: 23px;\n  width: 36px;\n  height: 36px;\n}\n.scrollContainerHP20-E1_1 div[class*='unitContainerSSR'] button[class*='unitTextSSR'] {\n  color: var(--theme_base_10);\n}\n.scrollContainerHP20-E1_1 div[class*='unitContainerSSR'] path {\n  fill: var(--theme_base_10);\n}\n.cardContainer-E1_1 {\n  display: flex;\n  list-style: none;\n  white-space: nowrap;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  height: 48px;\n}\n.carouselmaskContainer-E1_1 {\n  position: absolute;\n  display: none;\n  width: 100px;\n  height: 48px;\n  top: 0;\n  bottom: 0;\n  right: 104px;\n  pointer-events: none;\n  transform: rotate(0);\n}\n.carouselmaskContainer-E1_1.notToScrollEnd {\n  display: block;\n}\n.carouselmaskContent-E1_1 {\n  position: relative;\n  display: block;\n  width: 100px;\n  height: 48px;\n  background: linear-gradient(270deg, rgba(255, 255, 255, 0.08) 0%, transparent 100%);\n}\n.chevron-E1_1 {\n  bottom: 50%;\n  fill: white;\n  z-index: 100;\n  background: transparent;\n  border: none;\n  padding: 0;\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n}\n.chevron-E1_1> span:nth-child(1) {\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  align-items: center;\n  outline: none;\n}\n.chevron-E1_1 span[class^='chevronIcon'] {\n  width: 24px;\n  height: 24px;\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 50%;\n  padding: 0;\n}\n.chevron-E1_1:focus {\n  outline: none;\n}\n.chevron-E1_1:focus-visible {\n  outline: rgb(16, 16, 16) auto 1px;\n}\n.chevron-E1_1 path {\n  fill: #fff;\n}\n@media (-ms-high-contrast:active) {\n  .chevron-E1_1 path {\n    fill: ButtonText !important;\n  }\n}\n.chevron-E1_1 span[class^='chevronIcon'] svg {\n  width: 8px;\n  position: relative;\n}\n.chevronHP20-E1_1 {\n  top: 0px;\n}\n.chevronHP20-E1_1 span[class^='chevronIcon'] {\n  background: none;\n}\n.chevronHP20-E1_1 path {\n  fill: var(--theme_base_10);\n}\n@media (-ms-high-contrast:active) {\n  .chevronHP20-E1_1 path {\n    fill: ButtonText !important;\n  }\n}\n.chevronHP20-E1_1 span[class^='chevronIcon']:hover {\n  background: rgba(255, 255, 255, 0.1);\n}\n.chevron__left-E1_1 {\n  position: relative;\n}\n.chevron__left-E1_1 svg {\n  transform: rotate(180deg) scale(1.5);\n  right: 0px;\n}\n@media (max-width: 655px) {\n  .chevron__left-E1_1 {\n    display: none;\n  }\n}\n.chevronLeftCover-E1_1 {\n  position: absolute;\n  width: 31px;\n  height: 100%;\n  left: 0;\n  background: linear-gradient(to right, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0));\n  top: 0;\n}\n.chevron__right-E1_1 {\n  position: relative;\n  margin-left: 5px;\n}\n.chevron__right-E1_1 svg {\n  transform: scale(1.5);\n  right: -1px;\n}\n@media (max-width: 655px) {\n  .chevron__right-E1_1 {\n    display: none;\n  }\n}\n.chevronRightCover-E1_1 {\n  position: absolute;\n  width: 31px;\n  height: 100%;\n  right: 0;\n  background: linear-gradient(to left, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0));\n  top: 0;\n}\n.chevronIcon-E1_1 {\n  padding: 2px;\n  cursor: pointer;\n}\n.chevronIcon-E1_1:hover {\n  background: rgba(255,255,255,.4);\n}\n.chevronIcon-E1_1:focus {\n  outline: none;\n}\n.disabledIcon-E1_1 {\n  cursor: default;\n}\n.disabledIcon-E1_1:hover {\n  background: none !important;\n}\n.cdBorder-E1_1 {\n  border-left: 1px solid rgba(255, 255, 255, 0.2);\n}\n.cdBorder-E1_1 li[class^=popular] {\n  padding-left: 8px;\n}\n.cardItem-E1_1 {\n  border-right: 1px solid rgba(255, 255, 255, 0.2);\n  box-sizing: border-box;\n  flex-shrink: 0;\n}\n.cardItemHP20-E1_1 {\n  margin-left: 8px;\n  box-sizing: border-box;\n  flex-shrink: 0;\n  height: 40px;\n}\n.disabled-E1_1 {\n  opacity: 0.5;\n  outline: none !important;\n}\n.ulContainer-E1_1 {\n  width: 0;\n  flex: 1 1 auto;\n  position: relative;\n}\n.contentContainer-E1_1 {\n  margin: auto;\n  position: relative;\n  height: 48px;\n  display: flex;\n  align-items: center;\n}\n@media (max-width: 655px) {\n  .contentContainer-E1_1 {\n    width: 300px;\n  }\n}\n@media (min-width: 656px) and (max-width: 979px) {\n  .contentContainer-E1_1 {\n    width: 620px;\n  }\n}\n@media (min-width: 980px) and (max-width: 1303px) {\n  .contentContainer-E1_1 {\n    width: 940px;\n  }\n}\n@media (min-width: 1304px) and (max-width: 1627px) {\n  .contentContainer-E1_1 {\n    width: 1260px;\n  }\n}\n@media (min-width: 1628px) {\n  .contentContainer-E1_1 {\n    width: 1580px;\n  }\n}\n.contentContainerHP20-E1_1 {\n  height: 40px;\n}\n.locationSearchContainer-E1_1 {\n  width: 300px;\n  height: 32px;\n}\n.locationSearchContainer-E1_1 div[class^='searchRootV2'] {\n  background: none;\n  overflow: visible;\n}\n.locationSearchContainer-E1_1 #m-locs:not(.white) div[class^='suggestionContainer'] {\n  box-shadow:  0px 1px 3px rgb(0 0 0 / 14%);\n  background-color: rgba(255, 255, 255, 0.15);\n}\n.locationSearchContainer-E1_1 #m-locs:not(.white) div[class^='suggestionContainer'] > input {\n  color: #fff;\n}\n.locationSearchContainer-E1_1 #m-locs:not(.white) div[class^='suggestionContainer'] input::-webkit-input-placeholder {\n  color: #fff;\n  font-size: 14px;\n}\n.locationSearchContainer-E1_1 #m-locs:not(.white) div[class^='suggestionContainer'] input::-ms-input-placeholder {\n  color: #fff;\n  font-size: 14px;\n}\n.locationSearchContainer-E1_1 #m-locs:not(.white) div[class^='suggestionContainer'] input::-moz-placeholder {\n  color: #fff;\n  font-size: 14px;\n}\n.locationSearchContainer-E1_1 #m-locs:not(.white) div[class^='suggestionContainer'] input:-moz-placeholder {\n  color: #fff;\n  font-size: 14px;\n}\n.locationSearchContainer-E1_1 #m-locs:not(.white) div[class^='suggestionContainer'] button[class^='searchIcon'] {\n  background: none;\n}\n.locationSearchContainer-E1_1 #m-locs:not(.white) div[class^='suggestionContainer'] svg[class^='clearAllIconSvg'] path {\n  fill: rgb(245,245,245);\n}\n.locationSearchContainer-E1_1 \n", "id": "6e3ee82a-97b4-4c64-a540-59452a237535", "mentionables": []}, {"role": "assistant", "applyStatus": 0, "content": "", "reasoningContent": "", "id": "6ba45b49-2d51-4a94-a4bd-40db66d1bd77", "metadata": {}}], "createdAt": 1749957130322, "updatedAt": 1749957176105, "schemaVersion": 1}