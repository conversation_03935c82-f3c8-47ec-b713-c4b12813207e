/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var mt=Object.defineProperty;var Lt=Object.getOwnPropertyDescriptor;var Mt=Object.getOwnPropertyNames;var It=Object.prototype.hasOwnProperty;var Et=(H,I)=>{for(var t in I)mt(H,t,{get:I[t],enumerable:!0})},Nt=(H,I,t,s)=>{if(I&&typeof I=="object"||typeof I=="function")for(let n of Mt(I))!It.call(H,n)&&n!==t&&mt(H,n,{get:()=>I[n],enumerable:!(s=Lt(I,n))||s.enumerable});return H};var $t=H=>Nt(mt({},"__esModule",{value:!0}),H);var Ft={};Et(Ft,{default:()=>dt});module.exports=$t(Ft);var a=require("obsidian"),i={APB_title:"Progress",APB_total:100,APB_progressBarPercentage:83,APB_widthToggle:!0,APB_width:190,APB_height:8,APB_endcapToggle:!0,APB_marksToggle:!0,APB_autoMarksToggle:!0,APB_manualMarks:3,APB_marksColor:"#000000",APB_marksLightColor:"#ffffff",APB_marksWidth:3,APB_titleToggle:!0,APB_titleColor:"#8fa0ba",APB_titleLightColor:"#44546f",APB_percentageToggle:!0,APB_percentageColor:"#c1d7f9",APB_percentageLightColor:"#6d85ad",APB_fractionToggle:!0,APB_fractionColor:"#8fa0ba",APB_fractionLightColor:"#576a8a",APB_completedToggle:!0,APB_completedColor:"#c1d7f9",APB_completedLightColor:"#44546f",APB_inlineEditToggle:!1,APB_inlineEditStepSize:1,APB_gearColor:"#24647f",APB_gearLightColor:"#796c72",APB_gearHoverColor:"#3db9d1",APB_gearHoverLightColor:"#bb3574",APB_inlinePanelColor:"#5e67ed",APB_inlineButtonColor:"#ffffff",APB_inlineTextColor:"#3f46b0",APB_overageToggle:!1,APB_overageColor:"#38edef",APB_overageLightColor:"#d33411",APB_borderToggle:!0,APB_colorBorder:"#474f62",APB_colorLightBorder:"#ced0d6",APB_backgroundToggle:!0,APB_colorBackground:"#242a35",APB_colorLightBackground:"#f1f2f4",APB_TopMarginToggle:!1,APB_boxShadowToggle:!1,APB_boxShadowTypeToggle:!1,APB_hOffset:2,APB_vOffset:2,APB_blur:5,APB_boxShadowInsetToggle:!1,APB_colorBoxShadow:"#00fff7",APB_colorLightBoxShadow:"#474747",APB_colorBarCompleted:"#576178",APB_colorLightBarCompleted:"#fcfcfd",APB_colorBarBackground:"#3b4252",APB_colorLightBarBackground:"#d9dde2",APB_allowTasksToggle:!1,APB_autoTasksToggle:!1,APB_colorTaskText:"#8fa0ba",APB_colorLightTaskText:"#ffffff",APB_colorTaskBackground:"#3b4252",APB_colorLightTaskBackground:"#44546F",APB_allowSubTasksToggle:!1,APB_colorSubTaskText:"#8fa0ba",APB_colorLightSubTaskText:"#6c7a90",APB_colorSubTaskCompletedText:"#6dd374",APB_colorLightSubTaskCompletedText:"#349a16",APB_progressBarChange:!0,APB_fallbackColor:"#2978ef",defaultLightColors:["#278378","#2baab9","#4a32e2","#7c328e","#c11e49"],defaultDarkColors:["#2978ef","#8ec822","#dfaa22","#c84922","#dd4a86"],defaultRainbowColors:["#ff0000","#eeff00","#11ff00","#7300ff","#00e1ff"],defaultTemplate:{name:"Default",gradient:!0,gradientType:!1,colors:["#2978ef","#8ec822","#dfaa22","#c84922","#dd4a86"]},templates:[]},dt=class extends a.Plugin{constructor(){super(...arguments);this.isUpdating=!1;this.lastProcessedFile=null;this.lastChangeTime=0;this.MIN_CHANGE_INTERVAL=5e3;this.hasRegisteredEvents=!1;this.lastProcessedFileTime=new Map}async onload(){await this.loadSettings(),this.settingsTab=new Ct(this.app,this),this.addSettingTab(this.settingsTab),this.app.workspace.onLayoutReady(this.initializeProgressBars.bind(this)),this.hasRegisteredEvents||(this.registerEvents(),this.hasRegisteredEvents=!0),this.addCommand({id:"paste-code-block",name:"Paste code block",editorCallback:async(t,s)=>{if(t.focus(),!t){new a.Notice("No active editor found!");return}let n=Math.floor(this.settings.APB_total/100*this.settings.APB_progressBarPercentage),l=`\`\`\`apb
${this.settings.APB_title}: ${n}/${this.settings.APB_total}
\`\`\``,g=t.getCursor();t.replaceRange(l,g);let r=l.split(`
`),u={line:g.line+r.length-1,ch:r[r.length-1].length};setTimeout(()=>{t.setCursor(u),t.refresh()},0)}}),this.addCommand({id:"task-manual-refresh",name:"Task manual refresh",callback:async()=>{if(this.isUpdating||!this.settings.APB_allowTasksToggle||this.settings.APB_autoTasksToggle)return;new a.Notice("Processing Task Update ...",2e3);try{this.isUpdating=!0,await this.updateProgress(),new a.Notice("Task Update Completed",2e3)}catch(s){new a.Notice("Task Update Failed: "+s.message,2e3)}finally{setTimeout(()=>{this.isUpdating=!1},2e3)}if(!this.app.workspace.getActiveFile()){new a.Notice("No active file to refresh",2e3);return}}})}async hasApbWithTag(t,s){try{let n=await s.vault.read(t);return/```apb\b[\s\S]*?#(\w+)[\s\S]*?\n```/g.test(n)}catch(n){return console.error("Error checking hasApbWithTag:",n),!1}}isDashboardPage(t){return t?t.contentEl.querySelector(".dashboard")!==null:!1}async shouldUpdateProgress(t,s){let n=s.workspace.getActiveViewOfType(a.MarkdownView);return await this.hasApbWithTag(t,s)?!0:this.isDashboardPage(n)}registerEvents(){this.settings.APB_allowTasksToggle&&this.settings.APB_autoTasksToggle&&this.registerEvent(this.app.metadataCache.on("changed",St(async t=>{let s=Date.now(),n=this.lastProcessedFileTime.get(t.path)||0;if(this.isUpdating||s-this.lastChangeTime<this.MIN_CHANGE_INTERVAL||!this.settings.APB_allowTasksToggle)return;let l=this.app.workspace.getActiveFile();if(!l||t.path!==l.path||!await this.shouldUpdateProgress(l,this.app))return;let g=this.app.workspace.getActiveViewOfType(a.MarkdownView);if(!g)return;let r=g.editor,u=r.getCursor();new a.Notice("Processing Task Update ...",2e3);try{this.isUpdating=!0,this.lastChangeTime=s,this.lastProcessedFileTime.set(t.path,s),this.lastProcessedFile=l,await this.updateProgress(),new a.Notice("Task Update Completed",2e3)}catch(P){new a.Notice("Task Update Failed: "+P.message,2e3)}finally{setTimeout(()=>{this.isUpdating=!1},2e3)}r.setCursor(u)},2e3))),this.registerEvent(this.app.workspace.on("file-open",St(async()=>{var s;if(this.isUpdating||!this.settings.APB_allowTasksToggle)return;let t=this.app.workspace.getActiveFile();if(!(!t||t.path===((s=this.lastProcessedFile)==null?void 0:s.path))&&await this.shouldUpdateProgress(t,this.app))try{this.isUpdating=!0;let n=Date.now();this.lastProcessedFileTime.set(t.path,n),this.lastProcessedFile=t,await this.updateProgress()}finally{setTimeout(()=>{this.isUpdating=!1},2e3)}},2e3)))}async updateProgress(){let t=this.app.workspace.getActiveFile();if(!t)return;let s=new Map,n=await this.app.vault.read(t),l=/(?:^|\n)([ \t]*)(?:-|\*|\d+\.) \[([ x])\] (.+)$/gm,g,r=null;for(;(g=l.exec(n))!==null;){let[u,P,T,A]=g,m=P.replace(/\t/g,"    ").length,z=A.match(/#([\p{L}\p{N}\p{Emoji}_-]+)/u),b=z?z[1].normalize("NFC"):null;m===0?(r=b||null,r&&(s.has(r)||s.set(r,{total:0,completed:0,subTotal:0,subCompleted:0}),s.get(r).total++,T.trim()==="x"&&s.get(r).completed++)):r&&(s.get(r).subTotal++,T.trim()==="x"&&s.get(r).subCompleted++)}for(let[u,{total:P,completed:T,subTotal:A,subCompleted:y}]of s.entries())this.updateProgressBarInNote(u.normalize("NFC"),T,P,y,A)}async findMatchingTasks(t){let s=this.app.workspace.getActiveFile();if(!s)return!1;let n=await this.app.vault.read(s),l=/- \[.\] (.*?)#([\p{L}\p{N}\p{Emoji}_-]+)/gu,g;for(;(g=l.exec(n))!==null;)if(g[2].normalize("NFC")===t.normalize("NFC"))return!0;return!1}async updateTagSpan(t,s){if(s)try{if(this.settings.APB_allowTasksToggle){let n=await this.findMatchingTasks(s);t.id=n?"APB_tag":"APB_notag",t.id=="APB_tag"&&(t.style.color=this.settings.APB_colorTaskText,t.style.background=this.settings.APB_colorTaskBackground),t.textContent=n?s:"#"+s+" not found"}else t.id="APB_tasksDisabled",t.textContent="tasks are disabled"}catch(n){console.error("Error searching tasks for tag:",n),t.textContent="#"+s+" not found",t.id="APB_notag"}else console.warn("No tag was found.")}updateProgressBarInNote(t,s,n,l,g){let r=this.app.workspace.getActiveViewOfType(a.MarkdownView);if(!r)return;let u=r.editor.getValue(),P=/```apb\n([\s\S]+?)\n```/g,T=u.replace(P,(A,y)=>"```apb\n"+y.trim().split(`
`).map(b=>{let x=b.match(/^(.+?)\s*(?:#([\p{L}\p{N}\p{Emoji}_-]+))?(?:\s*~(\d+)\/(\d+))?\s*:\s*(\d+)\/(\d+)(?:\{([^}]+)\})?(?:\s*(?:\[.*?\])?)?$/u);if(x){let f=x[1].trim()||"",h=x[2]||"",W=x[3]?parseInt(x[3],10):null,R=x[4]?parseInt(x[4],10):null,C=x[7]||"";if(t.normalize("NFC")===h.normalize("NFC")){let O=C?`{${C}}`:"";return`${f}#${t}~${l}/${g}: ${s}/${n}${O}`}}return b}).join(`
`)+"\n```");this.isUpdating=!0;try{if(u!==T){let A=Date.now();r.editor.setValue(T)}}finally{setTimeout(()=>{this.isUpdating=!1},2e3)}}initializeProgressBars(){this.registerMarkdownCodeBlockProcessor("apb",(t,s,n)=>{this.renderProgressBar(t,s)})}renderProgressBar(t,s){{let n=t.trim().split(`
`);s.empty(),n.forEach((l,g)=>{let r=l.match(/^(.+?)(?:#([\p{L}\p{N}\p{Emoji}_-]+))?(?:~(\d+)\/(\d+))?(?::\s*(\d+)\/(\d+))(?:\{([^}]+)\})?(?:\s.*)?$/u);if(!r){ht(s,"APB_Error: Invalid block format");return}let u=r[1]||"",P=r[2]||"",T=r[3]?parseInt(r[3],10):null,A=r[4]?parseInt(r[4],10):null,y=r[5]?parseInt(r[5],10):0,m=r[6]?parseInt(r[6],10):0,z=r[7]||"";if(!this.settings.APB_overageToggle&&y>m&&m!==0){ht(s,"APB_Error: Value is too large");return}if(isNaN(y)||isNaN(m)||m===0){ht(s,"APB_Error: Invalid number");return}let b=y/m*100,x=Math.round(b-100),f=Math.min(Math.max(Math.round(b),0),100),h=document.createElement("div");h.addClass("progressBar-container"),g===0&&this.settings.APB_TopMarginToggle?h.style.marginTop="25px":h.style.marginTop="7px",this.settings.APB_borderToggle?h.style.border="1px solid"+this.settings.APB_colorBorder:h.style.border="0px",this.settings.APB_backgroundToggle?h.style.background=this.settings.APB_colorBackground:h.style.background="transparent";let W=this.settings.APB_boxShadowInsetToggle?" inset ":"";this.settings.APB_boxShadowToggle?(this.settings.APB_boxShadowTypeToggle?h.style.boxShadow=this.settings.APB_hOffset+"px "+this.settings.APB_vOffset+"px "+this.settings.APB_blur+"px "+this.settings.APB_colorBoxShadow+W:h.style.boxShadow="0px 0px "+this.settings.APB_blur+"px "+this.settings.APB_colorBoxShadow+W,s.style.padding=this.settings.APB_blur+"px"):(h.style.boxShadow="",s.style.padding="0px");let R=document.createElement("div");R.addClass("progressBar-text-container");let C=document.createElement("div");C.addClass("progressBar-background"),f!==100&&(C.style.overflow="hidden");let O=document.createElement("div");if(O.addClass("progressBar-title"),this.settings.APB_titleToggle){let B=u.split("~")[1],N=null,v=null;if(B){let k=B.match(/(\d+)\/(\d+)/);k&&(N=parseInt(k[1],10),v=parseInt(k[2],10))}if(P&&this.app.workspace.getActiveViewOfType(a.MarkdownView)){let $=O.createEl("span",{text:P});this.updateTagSpan($,P)}O.createEl("span",{text:u}),O.style.color=this.settings.APB_titleColor}let w=document.createElement("div");this.settings.APB_allowTasksToggle&&this.settings.APB_allowSubTasksToggle&&A!==null&&A!==0&&(T==A?(w.addClass("progressBar-subtask-completed"),w.style.color=this.settings.APB_colorSubTaskCompletedText,w.createEl("span",{text:"Sub Tasks - "+T+"/"+A+" completed"})):(w.addClass("progressBar-subtask"),w.style.color=this.settings.APB_colorSubTaskText,w.createEl("span",{text:"Sub Tasks - "+T+"/"+A})));let Z=document.createElement("div");Z.addClass("progressBar-percentage"),this.settings.APB_percentageToggle&&(this.settings.APB_overageToggle&&x>0?(Z.createEl("span",{text:x+100+"%"}),Z.style.color=this.settings.APB_overageColor):(Z.createEl("span",{text:f+"%"}),Z.style.color=this.settings.APB_percentageColor));let X=document.createElement("div");X.addClass("progressBar-value"),this.settings.APB_fractionToggle&&(X.createEl("span",{text:"("+y+"/"+m+")"}),X.style.color=this.settings.APB_fractionColor);let S=document.createElement("div");S.addClass("progressBar-completed"),this.settings.APB_completedToggle&&(S.createEl("span",{text:""}),S.style.color=this.settings.APB_percentageColor);let _=null,et=!1,st=!1;if(z&&z.trim()!==""){st=!0;let p=this.settings.templates.find(B=>B.name.toLowerCase()===z.toLowerCase());if(et=!!p,_=et?p:this.settings.defaultTemplate,!et){ht(s,`APB_Error: Template "${z}" not found`);return}}else _=this.settings.defaultTemplate;let ct=_?_.gradient:!1,pt=_?_.gradientType:!1,nt=_&&_.colors?Array.isArray(_.colors)?_.colors:[_.colors]:["#ff0000","#00ff00"],E=nt.filter(p=>p&&p!=="#000000").slice(0,5),q=E.length,M=document.createElement("div");M.className="progressBar-filled",M.style.position="relative",M.style.width=`${f}%`,M.style.height=`${this.settings.APB_height}px`,C.appendChild(M);let D=document.createElement("div");D.addClass("progressBar");let lt=this.settings.APB_width,at=parseInt(lt.toString(),10);this.settings.APB_widthToggle?(h.style.width="100%",C.style.width="100%",D.style.width=`${100-f}%`):(C.style.width=`${lt}px`,h.style.width=`${at+10}px`,D.style.width=`${100-f}%`),D.style.background=this.settings.APB_colorBarBackground,D.style.height=`${this.settings.APB_height}px`,this.settingsTab.setEndCaps(C,D,this.settings.APB_endcapToggle,f),f!==100&&C.appendChild(D);let rt=document.createElement("div");rt.addClass("marks");var K;if(this.settings.APB_autoMarksToggle?K=q:K=this.settings.APB_manualMarks+1,f!==100){if(this.settings.APB_marksToggle)for(let p=1;p<=K-1;p++){let B=document.createElement("div");B.addClass("mark"),B.style.left=`${p*(100/K)}%`,rt.appendChild(B);let N=window.devicePixelRatio||1,v=this.settings.APB_marksWidth/N,k=this.settingsTab.hexToRgba(this.settings.APB_marksColor,.5);B.style.borderLeft=v+"px solid "+k}C.appendChild(rt)}if(M.appendChild(S),this.settings.APB_endcapToggle&&f>0&&f<100){let p=window.devicePixelRatio||1,B=document.createElementNS("http://www.w3.org/2000/svg","svg");B.setAttribute("class","progressBar-mask"),B.setAttribute("width",`${this.settings.APB_height/2}`),B.setAttribute("height",`${this.settings.APB_height}`),B.setAttribute("viewBox",`0 0 ${this.settings.APB_height/2} ${this.settings.APB_height}`),B.style.position="absolute",B.style.top="0",B.style.right=`-${1/p}px`,B.style.width=`${(this.settings.APB_height+.5)/2}px`,B.style.height=`${this.settings.APB_height}px`;let N=`round-end-mask-${Math.random().toString(36).substring(2,9)}`;B.innerHTML=`
						<defs>
							<mask id="${N}">
							<rect x="0" y="-1" width="${(this.settings.APB_height+10.5)/2}" height="${this.settings.APB_height+3}" fill="white"/>
							<circle cx="0" cy="${this.settings.APB_height/2}" r="${this.settings.APB_height/2}" fill="black"/>
							</mask>
						</defs>
						<rect x="0" y="-1" width="${(this.settings.APB_height+10.5)/2}" height="${this.settings.APB_height+3}" fill="${this.settings.APB_colorBarBackground}" mask="url(#${N})"/>
						`,M.appendChild(B)}if(C.appendChild(D),f!==100&&(C.style.overflow="hidden"),f<100)if(ct)if(E.length===0&&E.push(this.settings.APB_fallbackColor),E.length===1){let p=E[0]||this.settings.APB_fallbackColor;C.style.backgroundColor=p,M.style.backgroundImage=""}else{let p=E.join(", ");pt?(M.style.backgroundImage=`linear-gradient(to right, ${p})`,C.style.backgroundImage=""):(M.style.backgroundImage="",C.style.backgroundImage=`linear-gradient(to right, ${p})`)}else{let p=100/q,B=Math.min(Math.floor(f/p),q-1);M.style.backgroundColor=q>0?E[B]:this.settings.APB_fallbackColor,C.style.backgroundImage=""}else D.style.backgroundColor=this.settings.APB_colorBarCompleted,this.settings.APB_completedToggle&&(S.addClass("progressBar-completed"),S.style.color=this.settings.APB_completedColor,S.textContent="COMPLETED"),M.style.backgroundImage="",C.style.backgroundColor=f===100?this.settings.APB_colorBarCompleted:nt[0];if(C.style.backgroundSize="100% 100%",C.style.backgroundRepeat="no-repeat",this.settings.APB_progressBarChange=!0,h.appendChild(R),this.settings.APB_titleToggle&&R.appendChild(O),this.settings.APB_percentageToggle&&R.appendChild(Z),this.settings.APB_fractionToggle&&R.appendChild(X),h.appendChild(C),h.appendChild(w),!P&&this.settings.APB_inlineEditToggle){g==0?h.style.marginTop="25px":h.style.marginTop="7px";let p=document.createElement("button");p.className="progressBar-settings-button",p.innerHTML=`
					<svg class="settings-icon" width="16" height="16" viewBox="0 0 24 24" fill="`+this.settings.APB_gearColor+`" xmlns="http://www.w3.org/2000/svg">
						<path d="M12 15.5A3.5 3.5 0 0 1 8.5 12 3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97s-.03-.66-.07-.97l2.03-1.63a.5.5 0 0 0 .11-.64l-2-3.46a.5.5 0 0 0-.61-.22l-2.39.96c-.51-.38-1.06-.7-1.65-.97l-.36-2.55a.5.5 0 0 0-.5-.41h-4a.5.5 0 0 0-.5.41l-.36 2.55c-.59.27-1.14.59-1.65.97l-2.39-.96a.5.5 0 0 0-.61.22l-2 3.46a.5.5 0 0 0 .11.64l2.03 1.63c-.04.31-.07.65-.07.97s.03.66.07.97l-2.03 1.63a.5.5 0 0 0-.11.64l2 3.46a.5.5 0 0 0 .61.22l2.39-.96c.51.38 1.06.7 1.65.97l.36 2.55a.5.5 0 0 0 .5.41h4a.5.5 0 0 0 .5-.41l.36-2.55c.59-.27 1.14-.59 1.65-.97l2.39.96a.5.5 0 0 0 .61-.22l2-3.46a.5.5 0 0 0-.11-.64l-2.03-1.63z"/>
					</svg>
					`,p.style.backgroundColor=this.settings.APB_colorBackground,p.style.border="1px solid "+this.settings.APB_colorBackground,p.style.display="inline-flex",p.style.alignItems="center",p.style.justifyContent="center",p.style.boxShadow="none";let B=this.settings.APB_gearHoverColor||"#666666",N=document.createElement("style");N.setAttribute("data-plugin","SvgIconPlugin"),N.textContent=`
						.progressBar-settings-button:hover .settings-icon {
							fill: ${B};
						}
					`,document.head.appendChild(N);let v=document.createElement("div");v.className="progressBar-settings-panel",v.style.backgroundColor=this.settings.APB_inlinePanelColor;let k=document.createElement("input");k.type="number",k.value=y.toString(),k.style.width="60px",k.style.marginRight="5px",k.style.border="none",k.style.boxShadow="none";let $=document.createElement("div");$.style.display="flex",$.style.flexWrap="wrap",$.style.gap="5px",$.style.margin="3px";let it=document.createElement("button");it.textContent="+"+this.settings.APB_inlineEditStepSize,it.style.backgroundColor=this.settings.APB_inlineButtonColor,it.style.color=this.settings.APB_inlineTextColor;let j=document.createElement("button");j.textContent="\u2212"+this.settings.APB_inlineEditStepSize,j.style.backgroundColor=this.settings.APB_inlineButtonColor,j.style.color=this.settings.APB_inlineTextColor;let ot=document.createElement("button");ot.textContent="Apply",ot.style.backgroundColor=this.settings.APB_inlineButtonColor,ot.style.color=this.settings.APB_inlineTextColor;let Q=document.createElement("button");Q.textContent="Cancel",Q.style.backgroundColor=this.settings.APB_inlineButtonColor,Q.style.color=this.settings.APB_inlineTextColor,it.addEventListener("click",F=>{F.stopPropagation();let V=parseInt(k.value);if(isNaN(V)){new a.Notice("Invalid progress value",2e3);return}let U=V+this.settings.APB_inlineEditStepSize;this.settings.APB_overageToggle||(U=Math.min(Math.max(U,0),m)),k.value=U.toString()}),j.addEventListener("click",F=>{F.stopPropagation();let V=parseInt(k.value);if(isNaN(V)){new a.Notice("Invalid progress value",2e3);return}let U=this.settings.APB_inlineEditStepSize||1,gt=V-U;gt=Math.max(gt,0),k.value=gt.toString()}),ot.addEventListener("click",async F=>{if(F.stopPropagation(),!this.settings.APB_allowTasksToggle){new a.Notice("Task updates are disabled",2e3);return}let V=this.app.workspace.getActiveFile();if(!V){new a.Notice("No active file",2e3);return}let U=parseInt(k.value);if(isNaN(U)){new a.Notice("Invalid number",2e3);return}await this.updateProgressValue(V,g,U,m,l),v.style.display="none"}),Q.addEventListener("click",F=>{F.stopPropagation(),v.style.display="none"});let ut=F=>{let V=F.target;!v.contains(V)&&!p.contains(V)&&(v.style.display="none")};p.addEventListener("click",F=>{F.stopPropagation(),F.preventDefault();let V=v.style.display==="flex";v.style.display=V?"none":"flex",V?document.removeEventListener("click",ut,{capture:!0}):(k.value=y.toString(),k.focus(),document.addEventListener("click",ut,{capture:!0}))}),$.appendChild(k),$.appendChild(j),$.appendChild(it),$.appendChild(ot),$.appendChild(Q),v.appendChild($),R.appendChild(p),R.appendChild(v)}s.appendChild(h);let Y=this.app.workspace.getActiveViewOfType(a.MarkdownView);if(Y){let B=Y.contentEl.querySelector(".dashboard")!==null,N=s.closest("li");N&&B&&!N.classList.contains("apb-list-item")&&N.classList.add("apb-list-item")}})}}async updateProgressValue(t,s,n,l,g){var y;let r=n;this.settings.APB_overageToggle?r=Math.max(n,0):r=Math.min(Math.max(n,0),l);let P=(await this.app.vault.read(t)).split(`
`),T=-1;for(let m=0;m<P.length;m++)if(P[m].trim()===g.trim()){T=m;break}if(T===-1){console.error("Could not find row matching originalRow:",g),new a.Notice("Failed to update: Progress bar row not found",2e3);return}let A=P[T];if(s<P.length){let m=A.match(/^(.+?)(?:#([\p{L}\p{N}\p{Emoji}_-]+))?(?:~(\d+)\/(\d+))?(?::\s*(\d+)\/(\d+))(?:\{([^}]+)\})?(?:\s.*)?$/u);if(m){let z=m[1]||"",b=m[2]?`#${m[2]}`:"",x=m[3]||"",f=m[4]||"",h=x&&f?`~${x}/${f}`:"",W=m[7]?`{${m[7]}}`:"",R=((y=A.match(/(?:\s.*)?$/))==null?void 0:y[0])||"",C=`${z}${b}${h}: ${r}/${l}${W}`;P[T]=C;let O=P.join(`
`);await this.app.vault.modify(t,O);try{await this.app.vault.modify(t,O);let w=await this.app.vault.read(t)}catch(w){console.error("File write failed:",w),new a.Notice("Failed to write to file: "+w.message,2e3);return}new a.Notice("APB Data Update ...",2e3);try{this.isUpdating=!0,await this.updateProgress(),new a.Notice("APB Data Update Completed",2e3)}catch(w){new a.Notice("APB Data Update Failed: "+w.message,2e3)}finally{setTimeout(()=>{this.isUpdating=!1},2e3)}}else console.error("Regex failed to match row:",A),new a.Notice("Failed to update: Invalid row format",2e3)}}async loadSettings(){this.settings=Object.assign({},i,await this.loadData())}async saveSettings(){await this.saveData(this.settings)}async onunload(){let t=document.querySelector("#copypastebutton");t&&t.removeEventListener("click",this.copyButtonListener)}},Ct=class extends a.PluginSettingTab{constructor(t,s){super(t,s);this.plugin=s,this.display()}display(){this.containerEl.empty(),this.createSettingsUI(this.containerEl)}createSettingsUI(t){var At,bt,_t,Tt,wt,kt;let s,n,l=t.createEl("p",{text:"If you find value in this plugin and would like to support us, please consider using our "});var g="https://ko-fi.com/cactuzhead";l.createEl("a",{href:g,text:"ko-fi",cls:"custom-link"});let r=t.createEl("div");r.addClass("custom-container"),r.appendChild(l);let u=r.createEl("a");u.target="_blank";var P=new DOMParser,T='<svg width="80" height="22" viewBox="0 0 80 22" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;"><g transform="matrix(0.108521,0,0,0.108521,-0.00522351,0.26727)"><clipPath id="_clip1"><rect x="-0.006" y="0.3" width="737.184" height="200.806"/></clipPath><g clip-path="url(#_clip1)"><defs><mask id="Mask"><g transform="matrix(9.2148,-0,-0,9.2148,0.0481336,-2.46284)"><image id="_Image2" width="80px" height="22px" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFAAAAAWCAAAAABye0QAAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAMUlEQVQ4jWNcKcxATfCE8akkVQ28xcLISFUDGZmoahwDA8OogaMGjho4auCogUPFQAC80QOoo1cvEAAAAABJRU5ErkJggg=="/></g></mask></defs><g mask="url(#Mask)"><path d="M345.948,199.597C333.351,199.597 320.101,192.881 308.601,181.463C308.164,182.224 307.701,182.96 307.225,183.671C302.379,190.933 293.043,199.597 276.038,199.597C264.399,199.597 248.713,195.081 240.055,173.567C234.251,159.149 231.549,137.051 231.549,104.018C231.549,75.038 234.892,51.931 241.482,35.345C245.294,25.742 250.299,18.143 256.35,12.758C263.454,6.441 272.112,3.098 281.378,3.098C293.607,3.098 304.098,8.616 310.917,18.638C311.957,20.173 312.908,21.803 313.764,23.528C327.326,9.942 341.121,3.092 354.999,3.092C373.279,3.092 385.084,15.72 385.084,35.269C385.084,46.305 379.97,59.117 369.879,73.338C364.063,81.533 356.826,89.924 348.593,98.024C363.72,120.871 377.649,149.261 377.649,167.179C377.649,175.844 374.402,183.963 368.496,190.039C362.515,196.198 354.504,199.591 345.935,199.591L345.948,199.597Z" style="fill:white;fill-rule:nonzero;"/><path d="M369.867,35.269C369.867,25.977 366.15,18.315 355.006,18.315C342.929,18.315 325.049,26.91 303.217,62.207C303.914,58.027 304.143,54.076 304.143,50.365C304.143,31.318 296.246,18.315 281.385,18.315C258.855,18.315 246.779,50.131 246.779,104.013C246.779,167.187 256.762,184.369 276.044,184.369C292.301,184.369 300.66,173.218 301.593,144.657C313.207,169.971 332.019,184.369 345.954,184.369C355.012,184.369 362.446,176.935 362.446,167.18C362.446,152.782 347.813,121.893 328.537,95.881C350.604,76.605 369.879,51.754 369.879,35.263L369.867,35.269Z" style="fill:white;fill-rule:nonzero;"/><path d="M695.015,83.124C672.151,83.124 653.549,64.241 653.549,41.027C653.549,29.921 657.78,19.633 665.451,12.059C673.142,4.473 683.639,0.3 695.015,0.3C706.396,0.3 717.069,4.442 724.879,11.958C732.811,19.595 737.177,29.997 737.177,41.255C737.177,64.343 718.264,83.124 695.015,83.124Z" style="fill:white;fill-rule:nonzero;"/><path d="M695.016,67.902C709.879,67.902 721.954,55.832 721.954,41.256C721.954,26.68 710.578,15.523 695.016,15.523C679.458,15.523 668.769,26.68 668.769,41.028C668.769,55.375 680.384,67.902 695.016,67.902Z" style="fill:white;fill-rule:nonzero;"/><path d="M691.999,200.862C677.025,200.862 664.762,193.416 657.486,179.906C651.558,168.888 648.67,153.627 648.67,133.261C648.67,112.894 651.569,97.659 657.532,86.641C664.935,72.96 677.505,65.425 692.924,65.425C708.344,65.425 719.501,72.078 726.574,84.662C732.54,95.28 735.32,110.141 735.32,131.434C735.32,152.727 732.498,168.349 726.689,179.493C719.509,193.27 707.184,200.862 691.999,200.862Z" style="fill:white;fill-rule:nonzero;"/><path d="M692.924,80.654C672.951,80.654 663.894,98.42 663.894,133.261C663.894,168.102 673.185,185.64 691.999,185.64C710.812,185.64 720.104,168.559 720.104,131.441C720.104,94.323 711.047,80.654 692.932,80.654L692.924,80.654Z" style="fill:white;fill-rule:nonzero;"/><path d="M597.708,200.865C582.396,200.865 571.631,193.92 565.725,180.226C561.94,171.447 559.966,159.681 559.275,140.983C554.353,138.896 550.275,136.194 546.95,132.826C540.627,126.42 537.424,118.06 537.424,107.981C537.424,97.281 540.496,88.515 546.551,81.931C549.838,78.361 553.923,75.5 558.887,73.312C559.77,51.791 566.578,33.701 578.711,20.787C591.124,7.575 608.831,0.3 628.597,0.3C642.753,0.3 655.044,4.105 664.139,11.298C673.8,18.941 679.121,29.743 679.121,41.712C679.121,57.582 670.782,70.191 657.317,76.623C665.706,82.084 672.152,91.046 672.152,105.932C672.152,119.328 667.514,129.61 658.369,136.505C653.047,140.513 646.405,143.272 637.616,145.036C636.468,161.248 634.019,172.57 629.96,180.885C623.567,193.958 612.418,200.865 597.708,200.865Z" style="fill:white;fill-rule:nonzero;"/><path d="M663.892,41.713C663.892,27.365 651.118,15.523 628.588,15.523C597.934,15.523 574.01,36.473 574.01,78.152L574.01,84.526C559.147,87.489 552.639,94.32 552.639,107.982C552.639,119.824 559.143,126.655 574.236,129.617C574.701,173.801 580.51,185.643 597.692,185.643C614.873,185.643 621.846,173.116 623.006,131.672C647.624,129.624 656.916,122.792 656.916,105.94C656.916,90.907 647.624,84.761 623.006,82.941C622.771,74.74 622.771,68.822 622.545,64.496C626.261,65.409 631.138,66.088 635.319,66.088C652.969,66.088 663.885,56.295 663.885,41.719L663.892,41.713Z" style="fill:white;fill-rule:nonzero;"/><path d="M418.638,199.597C398.105,199.597 379.287,190.927 365.65,175.184C352.565,160.082 345.353,139.588 345.353,117.49C345.353,95.392 352.539,75.704 365.586,61.477C378.957,46.895 397.801,38.859 418.638,38.859C439.475,38.859 458.354,46.781 471.65,61.166C484.912,75.52 491.923,94.992 491.923,117.484C491.923,139.975 484.716,160.075 471.623,175.178C457.982,190.92 439.164,199.591 418.638,199.591L418.638,199.597Z" style="fill:white;fill-rule:nonzero;"/><path d="M418.639,54.082C384.494,54.082 360.576,80.094 360.576,117.484C360.576,154.875 385.427,184.369 418.639,184.369C451.85,184.369 476.7,155.573 476.7,117.484C476.7,79.396 452.78,54.082 418.639,54.082ZM416.089,131.654C408.89,131.654 403.55,125.153 403.55,117.256C403.55,110.057 408.89,104.247 416.089,104.247C423.288,104.247 428.399,110.05 428.399,117.256C428.399,125.153 423.058,131.654 416.089,131.654Z" style="fill:white;fill-rule:nonzero;"/><path d="M511.712,143.855C498.455,143.855 488.921,141.363 481.703,136.009C472.868,129.451 468.384,119.309 468.384,105.875C468.384,90.138 475.456,81.461 481.388,76.952C489.094,71.091 499.395,68.357 513.797,68.357C528.203,68.357 538.846,70.894 546.621,76.337C553.106,80.878 560.839,89.809 560.839,106.573C560.839,123.337 552.63,132.337 545.742,136.669C537.844,141.642 527.347,143.855 511.705,143.855L511.712,143.855Z" style="fill:white;fill-rule:nonzero;"/><path d="M513.797,83.581C492.2,83.581 483.607,90.78 483.607,105.876C483.607,123.293 493.363,128.634 511.712,128.634C535.632,128.634 545.619,123.059 545.619,106.568C545.619,90.076 535.636,83.575 513.805,83.575L513.797,83.581Z" style="fill:white;fill-rule:nonzero;"/><path d="M96.246,197.935C61.202,197.935 32.697,182.263 15.991,153.803C1.199,128.856 -0.006,101.836 -0.006,71.644C-0.006,53.745 5.379,38.161 15.559,26.573C24.915,15.929 38.171,9.035 52.893,7.157C70.367,4.944 92.091,4.76 114.678,4.76C151.434,4.76 161.817,5.21 176.279,6.656C195.517,8.565 211.704,15.746 223.082,27.423C234.639,39.283 240.747,55.121 240.747,73.243L240.747,76.884C240.747,107.805 220.076,133.683 191.267,140.717C189.117,145.792 186.453,150.84 183.301,155.813L183.218,155.94C173.07,171.626 149.214,197.942 103.521,197.942L96.24,197.942L96.246,197.935Z" style="fill:white;fill-rule:nonzero;"/><path d="M174.77,21.798C161.114,20.434 151.555,19.977 114.684,19.977C91.013,19.977 70.983,20.206 54.828,22.254C33.434,24.988 15.223,41.372 15.223,71.646C15.223,101.92 16.815,125.362 29.108,146.072C42.992,169.743 66.207,182.714 96.253,182.714L103.534,182.714C140.404,182.714 160.435,163.14 170.45,147.664C174.776,140.833 177.96,134.008 180.009,127.177C206.185,124.9 225.531,103.277 225.531,76.878L225.531,73.238C225.531,44.79 206.871,24.988 174.776,21.798L174.77,21.798Z" style="fill:white;fill-rule:nonzero;"/><rect x="648.67" y="58.671" width="34.387" height="24.363" style="fill:white;fill-rule:nonzero;"/><path d="M668.769,41.028C668.769,55.832 680.384,67.902 695.016,67.902C709.648,67.902 721.954,55.832 721.954,41.256C721.954,26.68 710.578,15.523 695.016,15.523C679.458,15.523 668.769,26.68 668.769,41.028ZM663.893,133.264C663.893,168.562 673.185,185.643 691.998,185.643C710.812,185.643 720.103,168.562 720.103,131.444C720.103,94.326 711.046,80.657 692.931,80.657C672.958,80.657 663.901,98.423 663.901,133.264M552.647,107.982C552.647,119.824 559.148,126.655 574.245,129.617C574.705,173.801 580.518,185.643 597.7,185.643C614.882,185.643 621.854,173.116 623.014,131.672C647.629,129.624 656.921,122.792 656.921,105.94C656.921,90.907 647.629,84.761 623.014,82.941C622.78,74.74 622.78,68.822 622.549,64.496C626.266,65.409 631.146,66.088 635.323,66.088C652.977,66.088 663.893,56.295 663.893,41.719C663.893,27.143 651.119,15.53 628.589,15.53C597.934,15.53 574.01,36.48 574.01,78.158L574.01,84.533C559.148,87.495 552.64,94.326 552.64,107.988M483.606,105.876C483.606,123.293 493.362,128.634 511.711,128.634C535.631,128.634 545.617,123.059 545.617,106.567C545.617,90.076 535.631,83.575 513.803,83.575C491.98,83.575 483.613,90.774 483.613,105.87M403.541,117.255C403.541,110.056 408.881,104.246 416.08,104.246C423.279,104.246 428.394,110.05 428.394,117.255C428.394,125.152 423.053,131.653 416.08,131.653C409.108,131.653 403.541,125.152 403.541,117.255ZM360.575,117.49C360.575,155.578 385.426,184.374 418.637,184.374C451.849,184.374 476.698,155.578 476.698,117.49C476.698,79.401 452.778,54.087 418.637,54.087C384.493,54.087 360.575,80.099 360.575,117.49ZM281.379,18.314C258.849,18.314 246.773,50.129 246.773,104.011C246.773,167.185 256.762,184.368 276.038,184.368C292.295,184.368 300.655,173.218 301.587,144.656C313.201,169.97 332.013,184.368 345.948,184.368C355.006,184.368 362.439,176.934 362.439,167.179C362.439,152.781 347.807,121.892 328.531,95.88C350.598,76.604 369.873,51.753 369.873,35.262C369.873,25.97 366.156,18.308 355.012,18.308C342.936,18.308 325.055,26.902 303.223,62.2C303.921,58.02 304.149,54.068 304.149,50.358C304.149,31.31 296.253,18.308 281.391,18.308" style="fill:rgb(32,32,32);fill-rule:nonzero;"/><path d="M15.218,71.646C15.218,41.372 33.428,24.988 54.822,22.254C70.984,20.206 91.014,19.977 114.679,19.977C151.55,19.977 161.108,20.434 174.764,21.798C206.859,24.982 225.519,44.784 225.519,73.238L225.519,76.878C225.519,103.283 206.174,124.906 179.997,127.177C177.948,134.008 174.764,140.833 170.439,147.664C160.423,163.14 140.393,182.714 103.522,182.714L96.241,182.714C66.195,182.714 42.98,169.743 29.096,146.072C16.804,125.362 15.212,102.37 15.212,71.646" style="fill:rgb(32,32,32);fill-rule:nonzero;"/><path d="M32.285,71.872C32.285,101.233 34.105,120.121 43.664,137.647C54.586,157.905 74.388,165.644 96.924,165.644L103.977,165.644C133.567,165.644 147.907,151.303 155.874,138.788C159.743,132.414 163.156,125.361 164.976,116.481L166.34,110.791L174.535,110.791C192.745,110.791 208.449,96 208.449,77.105L208.449,73.692C208.449,52.527 195.25,41.369 172.258,38.642C159.287,37.507 151.548,37.05 114.672,37.05C89.865,37.05 72.112,37.278 58.684,39.327C39.795,42.061 32.279,52.755 32.279,71.872" style="fill:white;fill-rule:nonzero;"/><path d="M166.348,87.575C166.348,90.308 168.397,92.357 172.037,92.357C183.645,92.357 190.019,85.754 190.019,74.832C190.019,63.91 183.645,57.079 172.037,57.079C168.397,57.079 166.348,59.128 166.348,61.861L166.348,87.581L166.348,87.575Z" style="fill:rgb(32,32,32);fill-rule:nonzero;"/><path d="M54.593,86.205C54.593,99.633 62.103,111.24 71.662,120.348C78.036,126.495 88.051,132.869 94.876,136.966C96.925,138.102 98.974,138.787 101.251,138.787C103.984,138.787 106.255,138.102 108.082,136.966C114.913,132.869 124.922,126.495 131.068,120.348C140.855,111.246 148.365,99.639 148.365,86.205C148.365,71.636 137.443,58.665 121.738,58.665C112.408,58.665 106.033,63.447 101.251,70.044C96.925,63.441 90.329,58.665 80.992,58.665C65.059,58.665 54.587,71.636 54.587,86.205" style="fill:rgb(255,90,22);fill-rule:nonzero;"/></g></g></g></svg>';u.setAttribute("href",g),u.addClass("kofi-button"),u.appendChild(P.parseFromString(T,"text/xml").documentElement),new a.Setting(t).setName("Usage example code block").setDesc(createFragment(e=>{e.appendText("Use this code block and edit the ");let o=e.createEl("span");o.setText("Title: Value/Total"),o.classList.add("highlight-text"),e.appendText(" as needed."),e.createEl("br"),e.appendText("If you require more than one bar, simply create additional ");let c=e.createEl("span");c.setText("Title: Value/Total"),c.classList.add("highlight-text"),e.appendText(" lines within the code block."),e.createEl("br"),e.appendText("Note: ");let d=e.createEl("span");d.setText("Total"),d.classList.add("highlight-text"),e.appendText(" does not need to be 100."),e.createEl("br"),e.appendText("Visit "),e.createEl("a",{href:"https://cactuzhead.github.io/Advanced-Progress-Bars/",text:"cactuzhead",cls:"custom-link"}),e.appendText(" for more detailed information.")})).addTextArea(e=>{let o=Math.floor(this.plugin.settings.APB_total/100*this.plugin.settings.APB_progressBarPercentage);e.setValue(`\`\`\`apb
${this.plugin.settings.APB_title}: ${o}/${this.plugin.settings.APB_total}
\`\`\``),e.inputEl.style.height="80px",e.inputEl.style.width="200px",e.inputEl.style.resize="none";let c=e.inputEl.parentElement;if(c){let d=t.createEl("button",{text:"Copy to Clipboard",attr:{id:"copypastebutton"}});this.copyButtonListener=L=>{navigator.clipboard.writeText(e.inputEl.value).then(()=>{new a.Notice("Content copied to clipboard!")}).catch(G=>{new a.Notice("Failed to copy content: "+G)})},d.addEventListener("click",this.copyButtonListener),d.style.marginTop="10px",d.style.display="block",c.style.display="flex",c.style.flexDirection="column"}else console.error("Unable to find the parent container for the textarea")});let y=new a.Setting(t).setName("Light & dark").setHeading().settingEl.querySelector(".setting-item-name");y&&y.addClass("header-highlight"),s="These two options %%(Light & Dark)%% will change %%ALL%% the color settings below to their respective defaults. (Templates and all other settings will remain unchanged).<br>Note: once selected, all previous colors will be lost forever and can not be retrieved.",n=this.splitTextIntoSegments(s),new a.Setting(t).setName("Apply all light & dark color defaults (excluding templates)").setDesc(createFragment(e=>this.renderSegments(n,e))).addButton(e=>e.setIcon("sun").setTooltip("Reset to light default").onClick(async()=>{this.plugin.settings.APB_marksColor=i.APB_marksLightColor,this.plugin.settings.APB_titleColor=i.APB_titleLightColor,this.plugin.settings.APB_percentageColor=i.APB_percentageLightColor,this.plugin.settings.APB_fractionColor=i.APB_fractionLightColor,this.plugin.settings.APB_colorBorder=i.APB_colorLightBorder,this.plugin.settings.APB_colorBackground=i.APB_colorLightBackground,this.plugin.settings.APB_colorBarCompleted=i.APB_colorLightBarCompleted,this.plugin.settings.APB_colorBarBackground=i.APB_colorLightBarBackground,this.plugin.settings.APB_completedColor=i.APB_completedLightColor,this.plugin.settings.APB_overageColor=i.APB_overageLightColor,this.plugin.settings.APB_gearColor=i.APB_gearLightColor,this.plugin.settings.APB_gearHoverColor=i.APB_gearHoverLightColor,this.plugin.settings.APB_colorBoxShadow=i.APB_colorLightBoxShadow,this.plugin.settings.APB_colorTaskText=i.APB_colorLightTaskText,this.plugin.settings.APB_colorTaskBackground=i.APB_colorLightTaskBackground,this.plugin.settings.APB_colorSubTaskText=i.APB_colorLightSubTaskText,this.plugin.settings.APB_colorSubTaskCompletedText=i.APB_colorLightSubTaskCompletedText,await this.plugin.saveSettings(),this.display()})).addButton(e=>e.setIcon("moon").setTooltip("Reset to dark default").onClick(async()=>{this.plugin.settings.APB_marksColor=i.APB_marksColor,this.plugin.settings.APB_titleColor=i.APB_titleColor,this.plugin.settings.APB_percentageColor=i.APB_percentageColor,this.plugin.settings.APB_fractionColor=i.APB_fractionColor,this.plugin.settings.APB_colorBorder=i.APB_colorBorder,this.plugin.settings.APB_colorBackground=i.APB_colorBackground,this.plugin.settings.APB_colorBarCompleted=i.APB_colorBarCompleted,this.plugin.settings.APB_colorBarBackground=i.APB_colorBarBackground,this.plugin.settings.APB_completedColor=i.APB_completedColor,this.plugin.settings.APB_overageColor=i.APB_overageColor,this.plugin.settings.APB_gearColor=i.APB_gearColor,this.plugin.settings.APB_gearHoverColor=i.APB_gearHoverColor,this.plugin.settings.APB_colorBoxShadow=i.APB_colorBoxShadow,this.plugin.settings.APB_colorTaskText=i.APB_colorTaskText,this.plugin.settings.APB_colorTaskBackground=i.APB_colorTaskBackground,this.plugin.settings.APB_colorSubTaskText=i.APB_colorSubTaskText,this.plugin.settings.APB_colorSubTaskCompletedText=i.APB_colorSubTaskCompletedText,await this.plugin.saveSettings(),this.display()}));let z=new a.Setting(t).setName("Demonstration progress bar").setHeading().settingEl.querySelector(".setting-item-name");z&&z.addClass("header-highlight"),new a.Setting(t).setDesc("Any changes you make to this settings page will be instantly reflected in the demonstration progress bar below.  Use it as a guide when making your decisions. Note that template colors will not be shown in this demo.");let b=t.createEl("div");b.addClass("progressBar-container"),this.plugin.settings.APB_borderToggle?b.style.border="1px solid"+this.plugin.settings.APB_colorBorder:b.style.border="0px",this.plugin.settings.APB_backgroundToggle?b.style.background=this.plugin.settings.APB_colorBackground:b.style.background="transparent";let x=this.plugin.settings.APB_boxShadowInsetToggle?" inset ":"";this.plugin.settings.APB_boxShadowToggle?this.plugin.settings.APB_boxShadowTypeToggle?b.style.boxShadow=this.plugin.settings.APB_hOffset+"px "+this.plugin.settings.APB_vOffset+"px "+this.plugin.settings.APB_blur+"px "+this.plugin.settings.APB_colorBoxShadow+x:b.style.boxShadow="0px 0px "+this.plugin.settings.APB_blur+"px "+this.plugin.settings.APB_colorBoxShadow+x:b.style.boxShadow="";let f=t.createEl("div");f.addClass("progressBar-text-container");let h=t.createEl("div");h.addClass("progressBar-background");let W=t.createEl("div");W.addClass("progressBar-title"),this.plugin.settings.APB_titleToggle&&(W.createEl("span",{text:this.plugin.settings.APB_title}),W.style.color=this.plugin.settings.APB_titleColor);let R=t.createEl("div");R.addClass("progressBar-percentage"),this.plugin.settings.APB_percentageToggle&&(R.createEl("span",{text:this.plugin.settings.APB_progressBarPercentage+"%"}),R.style.color=this.plugin.settings.APB_percentageColor);let C=t.createEl("div");C.addClass("progressBar-value");let O=Math.floor(this.plugin.settings.APB_total/100*this.plugin.settings.APB_progressBarPercentage);this.plugin.settings.APB_fractionToggle&&(C.createEl("span",{text:"("+O+"/"+this.plugin.settings.APB_total+")"}),C.style.color=this.plugin.settings.APB_fractionColor);let w=t.createEl("div");w.addClass("progressBar");let Z=this.plugin.settings.APB_width,X=parseInt(Z.toString(),10),S=Math.min(Math.max(Math.round(this.plugin.settings.APB_progressBarPercentage),0),100);this.plugin.settings.APB_widthToggle?(b.style.width="100%",h.style.width="100%",w.style.width=`${100-S}%`):(b.style.width=`${X}px`,h.style.width=`${String(X-12)}px`,w.style.width=`${100-S}%`),w.style.background=this.plugin.settings.APB_colorBarBackground,b.style.margin="17px",w.style.height=`${this.plugin.settings.APB_height}px`,this.setEndCaps(h,w,this.plugin.settings.APB_endcapToggle,S);let _=document.createElement("div");if(_.className="progressBar-filled",_.style.position="relative",_.style.width=`${S}%`,_.style.height=`${this.plugin.settings.APB_height}px`,h.appendChild(_),this.plugin.settings.APB_endcapToggle&&S>0&&S<100){let e=window.devicePixelRatio||1,o=document.createElementNS("http://www.w3.org/2000/svg","svg");o.setAttribute("class","progressBar-mask"),o.setAttribute("width",`${this.plugin.settings.APB_height/2}`),o.setAttribute("height",`${this.plugin.settings.APB_height}`),o.setAttribute("viewBox",`0 0 ${this.plugin.settings.APB_height/2} ${this.plugin.settings.APB_height}`),o.style.position="absolute",o.style.top="0",o.style.right=`-${1/e}px`,o.style.width=`${(this.plugin.settings.APB_height+.5)/2}px`,o.style.height=`${this.plugin.settings.APB_height}px`,o.innerHTML=`
			<defs>
				<mask id="round-end-mask-demo">
				<rect x="0" y="-1" width="${(this.plugin.settings.APB_height+10.5)/2}" height="${this.plugin.settings.APB_height+3}" fill="white"/>
				<circle cx="0" cy="${this.plugin.settings.APB_height/2}" r="${this.plugin.settings.APB_height/2}" fill="black"/>
				</mask>
			</defs>
			<rect x="0" y="-1" width="${(this.plugin.settings.APB_height+10.5)/2}" height="${this.plugin.settings.APB_height+3}" fill="${this.plugin.settings.APB_colorBarBackground}" mask="url(#round-end-mask-demo)"/>
			`,_.appendChild(o)}h.appendChild(w),S!==100&&(h.style.overflow="hidden");let et="defaultTemplate",st=et?this.plugin.settings.templates.find(e=>e.name.toLowerCase()===et.toLowerCase()):null,ct=st?st.gradient:(bt=(At=this.plugin.settings.defaultTemplate)==null?void 0:At.gradient)!=null?bt:!0,pt=st?st.gradientType:(Tt=(_t=this.plugin.settings.defaultTemplate)==null?void 0:_t.gradientType)!=null?Tt:!1,nt=(kt=(wt=this.plugin.settings.defaultTemplate)==null?void 0:wt.colors)!=null?kt:["#ff0000","#00ff00"],E=nt.filter(e=>e&&e!=="#000000").slice(0,5),q=E.length;if(S<100)if(ct)if(E.length===0&&E.push(this.plugin.settings.APB_fallbackColor),E.length===1){let e=E[0]||this.plugin.settings.APB_fallbackColor;h.style.backgroundColor=e,_.style.backgroundImage=""}else{let e=E.join(", ");pt?(_.style.backgroundImage=`linear-gradient(to right, ${e})`,h.style.backgroundImage=""):(_.style.backgroundImage="",h.style.backgroundImage=`linear-gradient(to right, ${e})`)}else{let e=100/q,o=Math.min(Math.floor(S/e),q-1);_.style.backgroundColor=q>0?E[o]:this.plugin.settings.APB_fallbackColor,h.style.backgroundImage=""}else{let e=t.createEl("div");e.addClass("progressBar-completed"),this.plugin.settings.APB_completedToggle&&(e.style.color=this.plugin.settings.APB_completedColor,this.plugin.settings.APB_progressBarPercentage==100&&(e.textContent="COMPLETED",e.style.position="absolute",e.style.top="50%",e.style.left="50%",e.style.transform="translate(-50%, -50%)",e.style.whiteSpace="nowrap")),_.style.backgroundImage="",_.appendChild(e),h.style.backgroundColor=S===100?this.plugin.settings.APB_colorBarCompleted:nt[0]}h.style.backgroundSize="100% 100%",h.style.backgroundRepeat="no-repeat";let M=t.createEl("div");M.addClass("marks");var D;if(this.plugin.settings.APB_autoMarksToggle?D=q:D=this.plugin.settings.APB_manualMarks+1,this.plugin.settings.APB_progressBarPercentage!==100&&(h.appendChild(M),this.plugin.settings.APB_marksToggle))for(let e=1;e<=D-1;e++){let o=t.createEl("div");o.addClass("mark"),o.style.left=`${e*(100/D)}%`,M.appendChild(o);let c=window.devicePixelRatio||1,d=this.plugin.settings.APB_marksWidth/c,L=this.hexToRgba(this.plugin.settings.APB_marksColor,.5);o.style.borderLeft=d+"px solid "+L}this.plugin.settings.APB_progressBarChange=!0,this.plugin.saveSettings();let lt=t.createEl("p",{text:"This is a demo progress bar."}),at=t.createEl("div");at.addClass("custom-container"),at.appendChild(lt),b.appendChild(f),this.plugin.settings.APB_titleToggle&&f.appendChild(W),this.plugin.settings.APB_percentageToggle&&f.appendChild(R),this.plugin.settings.APB_fractionToggle&&f.appendChild(C),at.appendChild(b),b.appendChild(h);let K=new a.Setting(t).setName("Progress bar").setHeading().settingEl.querySelector(".setting-item-name");K&&K.addClass("header-highlight"),s="Set the default title of a new progress bar. If you change this, the %%Title%% will be automatically changed in the example code block at the top of the settings for easy copy and pasting later on.",n=this.splitTextIntoSegments(s),new a.Setting(t).setName("Default title").setDesc(createFragment(e=>this.renderSegments(n,e))).addText(e=>{let o;e.setPlaceholder("Enter your title").setValue(this.plugin.settings.APB_title||"").onChange(c=>{clearTimeout(o),o=setTimeout(async()=>{this.plugin.settings.APB_title=c,this.plugin.settings.APB_progressBarChange=!0,await this.plugin.saveSettings(),this.display()},2e3)})}).addButton(e=>e.setIcon("rotate-ccw").setTooltip("Reset to default").onClick(async()=>{this.plugin.settings.APB_title=i.APB_title,this.plugin.settings.APB_progressBarChange=!0,await this.plugin.saveSettings(),this.display()}));let Y;if(s="Set the default %%Total%% of a new progress bar. If you change this, the %%Total%% will be automatically changed in the example code block above for easy copy and pasting later on.",n=this.splitTextIntoSegments(s),new a.Setting(t).setName("Default total").setDesc(createFragment(e=>this.renderSegments(n,e))).addText(e=>e.setPlaceholder("100").setValue(this.plugin.settings.APB_total!==void 0?String(this.plugin.settings.APB_total):String(i.APB_total)).onChange(o=>{clearTimeout(Y),Y=setTimeout(async()=>{var d;let c=parseInt(o,10);c>=1&&!isNaN(c)&&c===parseFloat(o)?(this.plugin.settings.APB_total=c,await this.plugin.saveSettings(),this.display()):(e.setValue(String((d=this.plugin.settings.APB_total)!=null?d:i.APB_total)),await this.plugin.saveSettings(),this.display(),new a.Notice("Please enter a value 1 or above."))},1e3)}).inputEl.classList.add("custom-textbox")).addButton(e=>e.setIcon("rotate-ccw").setTooltip("Reset to default").onClick(async()=>{this.plugin.settings.APB_total=i.APB_total,this.plugin.settings.APB_progressBarChange=!0,await this.plugin.saveSettings(),this.display()})),s="Set the percentage for the demonstration progress bar above. This will also update the %%Value%% parameter in the Example Code at the top of the settings. Once you are satisfied with all other settings, you will likely want to set this back to 0.",n=this.splitTextIntoSegments(s),new a.Setting(t).setName("Progress percentage").setDesc(createFragment(e=>this.renderSegments(n,e))).addSlider(e=>{var o;e.setLimits(0,100,1).setDynamicTooltip().setValue((o=this.plugin.settings.APB_progressBarPercentage)!=null?o:i.APB_progressBarPercentage).onChange(async c=>{this.plugin.settings.APB_progressBarPercentage=c,this.plugin.settings.APB_progressBarChange=!0,await this.plugin.saveSettings(),this.display()})}).addButton(e=>e.setIcon("rotate-ccw").setTooltip("Reset to default").onClick(async()=>{this.plugin.settings.APB_progressBarPercentage=i.APB_progressBarPercentage,this.plugin.settings.APB_progressBarChange=!0,await this.plugin.saveSettings(),this.display()})),this.createToggleSetting(t,"Full width","Toggle this on to set the progress bar to fill the entire width of its container. For a fixed width, toggle this off and use the %%Width%% option that will appear below.","APB_widthToggle"),!this.plugin.settings.APB_widthToggle){let e;new a.Setting(t).setName("Width").setDesc("Set the width of the progress bar container in pixels (minimum 100). Note: If you want it to span the full width, make sure to enable the toggle above.").addText(o=>o.setPlaceholder("200").setValue(this.plugin.settings.APB_width!==void 0?String(this.plugin.settings.APB_width):String(i.APB_width)).onChange(c=>{clearTimeout(e),e=setTimeout(async()=>{var L;let d=parseInt(c,10);d>=100&&!isNaN(d)&&d===parseFloat(c)?(this.plugin.settings.APB_width=d,await this.plugin.saveSettings(),this.display()):(o.setValue(String((L=this.plugin.settings.APB_width)!=null?L:i.APB_width)),await this.plugin.saveSettings(),this.display(),new a.Notice("Please enter a value 100 or above."))},1e3)}).inputEl.classList.add("custom-textbox")).addButton(o=>o.setIcon("rotate-ccw").setTooltip("Reset to default").onClick(async()=>{this.plugin.settings.APB_width=i.APB_width,await this.plugin.saveSettings(),this.display()}))}new a.Setting(t).setName("Height").setDesc("Set the height of the progress bar in pixels.  (See demonstration progress bar above)").addSlider(e=>{var o;e.setLimits(1,15,1).setDynamicTooltip().setValue((o=this.plugin.settings.APB_height)!=null?o:i.APB_height).onChange(async c=>{this.plugin.settings.APB_height=c,this.setEndCaps(h,w,this.plugin.settings.APB_endcapToggle,this.plugin.settings.APB_progressBarPercentage),this.plugin.settings.APB_progressBarChange=!0,await this.plugin.saveSettings(),this.display()})}).addButton(e=>e.setIcon("rotate-ccw").setTooltip("Reset to default").onClick(async()=>{this.plugin.settings.APB_height=i.APB_height,this.plugin.settings.APB_progressBarChange=!0,await this.plugin.saveSettings(),this.display()})),this.createToggleSetting(t,"Round end caps","When toggled, the progress bar will have either a round or square end cap.  (See demonstration progress bar above)","APB_endcapToggle");let B=new a.Setting(t).setName("Section marks").setHeading().settingEl.querySelector(".setting-item-name");B&&B.addClass("header-highlight"),this.createToggleSetting(t,"Section marks","When toggled on, the progress bar will have vertical section marks equally spaced along its length.  The number and spacing of these will depend on the settings below. (See demonstration progress bar above)","APB_marksToggle"),this.plugin.settings.APB_marksToggle&&(this.createToggleSetting(t,"Automatically assigned marks","When toggled on, your progress bar will automatically assign equally spaced vertical marks based on how many colors your progress bar template (or default template) uses.<br>Note: if the bar reaches 100% or the bar has only one color or is a gradient, no marks will be displayed automatically.  (toggle off to override this behavior)","APB_autoMarksToggle"),this.plugin.settings.APB_autoMarksToggle||this.createSliderSetting(t,"Number of marks","Manually override the number of evenly spaced marks along the gradient progress bar regardless of the number of colors used.","APB_manualMarks",1,4,1),this.createColorPickerSetting(t,"Section mark color","Select the color of the section %%Marks%%.<br>Note: The color will be set to 50% transparency, allowing the progress bar's color to blend and influence the final appearance.","APB_marksColor",i.APB_marksLightColor,i.APB_marksColor),this.createSliderSetting(t,"Section mark width","Set the width of the section mark lines on the progress bar from a range of 1 to 5 pixels.<br>(See demonstration progress bar above)","APB_marksWidth",1,5,1));let v=new a.Setting(t).setName("Text").setHeading().settingEl.querySelector(".setting-item-name");v&&v.addClass("header-highlight"),this.createToggleSetting(t,"Show title text","When toggled on, the %%Title%% text will be displayed above the progress bar.<br>(See demonstration progress bar above)","APB_titleToggle"),this.createColorPickerSetting(t,"Title text color","Select the color of the %%Title%% text.","APB_titleColor",i.APB_titleLightColor,i.APB_titleColor),this.createToggleSetting(t,"Show percentage text","When toggled on, the %%Percentage%% text will be displayed above the progress bar<br>(See demonstration progress bar above)","APB_percentageToggle"),this.createColorPickerSetting(t,"Percentage text color","Select the color of the %%Percentage%% text.","APB_percentageColor",i.APB_percentageLightColor,i.APB_percentageColor),this.createToggleSetting(t,"Show fraction text (value/total)","When toggled on, the fraction text %%(Value/Total)%% will be displayed above the progress bar.<br>(See demonstration progress bar above)","APB_fractionToggle"),this.createColorPickerSetting(t,"Fraction text color","Select the color of the %%Fraction%% text.","APB_fractionColor",i.APB_fractionLightColor,i.APB_fractionColor),this.createToggleSetting(t,"Show completed text","When toggled on, the word %%COMPLETED%% will be displayed on the progress bar when it reaches 100%.<br>(See demonstration progress bar above)","APB_completedToggle"),this.createColorPickerSetting(t,"Completed text color","Select the color of the %%Completed%% text.","APB_completedColor",i.APB_completedLightColor,i.APB_completedColor);let $=new a.Setting(t).setName("Inline edit").setHeading().settingEl.querySelector(".setting-item-name");$&&$.addClass("header-highlight"),this.createToggleSetting(t,"Inline edit","When toggled on, you will see a gear icon to the right of the progress bar's text which allows you to edit the value without editing the code block.<br>(See documentation for full details)","APB_inlineEditToggle"),this.plugin.settings.APB_inlineEditToggle&&(this.createColorPickerSetting(t,"Gear icon color","Choose the color of the %%gear icon%%.","APB_gearColor",i.APB_gearLightColor,i.APB_gearColor),this.createColorPickerSetting(t,"Gear icon hover color","Choose the color for the %%gear icon's%% mouse hover effect.","APB_gearHoverColor",i.APB_gearHoverLightColor,i.APB_gearHoverColor),this.createSliderSetting(t,"Step size","Set the step size for the increment and decrement buttons on the inline edit panel.<br>(See documentation for full details)","APB_inlineEditStepSize",1,100,1));let j=new a.Setting(t).setName("Override error").setHeading().settingEl.querySelector(".setting-item-name");j&&j.addClass("header-highlight"),this.createToggleSetting(t,"Override large value error","When toggled on, you will not get an error when the %%Value%% is greater than the %%Total%%","APB_overageToggle"),this.plugin.settings.APB_overageToggle&&this.createColorPickerSetting(t,"Overage percentage text color","Select the color of the %%Percentage%% text when it is greater than 100%.","APB_overageColor",i.APB_overageLightColor,i.APB_overageColor);let Q=new a.Setting(t).setName("Progress bar container").setHeading().settingEl.querySelector(".setting-item-name");Q&&Q.addClass("header-highlight"),this.createToggleSetting(t,"Border","When toggled on, a border will be displayed around the progress bar container.","APB_borderToggle"),this.createColorPickerSetting(t,"Border color","Select the color of the border around the progress bar container.","APB_colorBorder",i.APB_colorLightBorder,i.APB_colorBorder),this.createToggleSetting(t,"Background","When toggled on, the background of the progress bar container will show.","APB_backgroundToggle"),this.createColorPickerSetting(t,"Background color","Select the background color of the progress bar container.","APB_colorBackground",i.APB_colorLightBackground,i.APB_colorBackground),this.createToggleSetting(t,"Top margin","When toggled on, the progress bar container will have a larger margin at the top to avoid text being obscured by the %%</>%% in the top right of the code block when you mouse over.","APB_TopMarginToggle");let F=new a.Setting(t).setName("Box shadow").setHeading().settingEl.querySelector(".setting-item-name");F&&F.addClass("header-highlight"),this.createToggleSetting(t,"Box-shadow","When toggled on, a box-shadow will be displayed around the progress bar container.","APB_boxShadowToggle"),this.plugin.settings.APB_boxShadowToggle&&(this.createToggleSetting(t,"Box-shadow type","When toggled on, a box-shadow will be displayed to the lower right of the progress bar container.  When toggled off, a glow shadow will be shown around the progress bar container.","APB_boxShadowTypeToggle"),this.plugin.settings.APB_boxShadowTypeToggle&&(this.createSliderSetting(t,"H-offset","Set the %%horizontal%% offset of the box-shadow from a range of 1 to 5 pixels.  (See demonstration progress bar above)","APB_hOffset",0,5,1),this.createSliderSetting(t,"V-offset","Set the %%vertical%% offset of the box-shadow from a range of 1 to 5 pixels.  (See demonstration progress bar above)","APB_vOffset",0,5,1)),this.createSliderSetting(t,"Blur","Set the %%blur%% of the %%box-shadow%% from a range of 0 to 5 pixels.  %%(See demonstration progress bar above)%%","APB_blur",0,5,1),this.createToggleSetting(t,"Inset","When toggled on, it will be displayed as an inner shadow.  When toggled off, an outer shadow will be shown instead.","APB_boxShadowInsetToggle"),this.createColorPickerSetting(t,"Box-shadow color","Select the color of the container's box-shadow.","APB_colorBoxShadow",i.APB_colorLightBoxShadow,i.APB_colorBoxShadow));let U=new a.Setting(t).setName("Progress bar color").setHeading().settingEl.querySelector(".setting-item-name");U&&U.addClass("header-highlight"),this.createColorPickerSetting(t,"Completed color","Select the color the entire progress bar will use when it reaches 100% completion.","APB_colorBarCompleted",i.APB_colorLightBarCompleted,i.APB_colorBarCompleted),this.createColorPickerSetting(t,"Bar background color","This color will be used for the progress bar's background, representing the remaining percentage.","APB_colorBarBackground",i.APB_colorLightBarBackground,i.APB_colorBarBackground);let Pt=new a.Setting(t).setName("Tasks").setHeading().settingEl.querySelector(".setting-item-name");Pt&&Pt.addClass("header-highlight"),this.createToggleSetting(t,"Enable task linking","When %%toggled on%%, you will be able to automatically update progress bars by using matching tags in the progress bar's title and tasks.  See documentation for full details.","APB_allowTasksToggle"),this.plugin.settings.APB_allowTasksToggle&&(this.createToggleSetting(t,"Auto tasks","When %%toggled on%%, tasks are automatically updated when it detects editing on the current page.  It is %%highly recommended%% that this is turned %%off%% and you use manually triggered updates instead using page switching or hotkey.  See documentation for full details.","APB_autoTasksToggle"),this.createColorPickerSetting(t,"Task text color","Choose the %%text%% color for task badges.","APB_colorTaskText",i.APB_colorLightTaskText,i.APB_colorTaskText),this.createColorPickerSetting(t,"Task background color","Choose the %%background%% color for task badges.","APB_colorTaskBackground",i.APB_colorLightTaskBackground,i.APB_colorTaskBackground),this.createToggleSetting(t,"Enable sub-task linking","When %%toggled on%%, you will be able to automatically see your subtask status under the progerss bar.<br>See documentation for full details.","APB_allowSubTasksToggle"),this.plugin.settings.APB_allowSubTasksToggle&&(this.createColorPickerSetting(t,"Sub task color","Choose the %%text%% color for the sub task shown under your progress bar.","APB_colorSubTaskText",i.APB_colorLightSubTaskText,i.APB_colorSubTaskText),this.createColorPickerSetting(t,"Sub task completed color","Choose the %%completed text%% color for the sub task shown under your progress bar.","APB_colorSubTaskCompletedText",i.APB_colorLightSubTaskCompletedText,i.APB_colorSubTaskCompletedText)));let ft=new a.Setting(t).setName("Templates").setHeading().settingEl.querySelector(".setting-item-name");ft&&ft.addClass("header-highlight"),s="Create a template with custom gradient or stepped colors.<br>Note that %%color pickers%% with a value of %%#000000%% (%%pure black%%) will be ignored.<br>The %%default template%% is applied to all progress bars that do %%not%% have a custom template assigned.<br>(See documentation for full details)",n=this.splitTextIntoSegments(s),new a.Setting(t).setName("Add new template").setDesc(createFragment(e=>this.renderSegments(n,e))).addButton(e=>e.setButtonText("Add template").setTooltip("Create a new template").onClick(async()=>{let o=this.plugin.settings.templates.length;this.plugin.settings.templates.push({name:"New Template "+(o+1),gradient:!1,gradientType:!1,colors:["#000000","#000000","#000000","#000000","#000000"]}),await this.plugin.saveSettings(),this.display()}));let xt=t.createDiv({cls:"settings-row-container"}).createDiv({cls:"settings-row-headers"});["Template Name","|","Gradient Toggle","|","Gradient Type Toggle","|","Color 1 - 5","|","3 Color Presets"].forEach((e,o)=>{xt.createEl("span",{text:e,cls:["settings-row-header",e==="Gradient Toggle"||e==="Gradient Type Toggle"?"gradient-header":""]})});let J=new a.Setting(t).setClass("default-setting-row");J.addExtraButton(e=>{e.setIcon("lock").setTooltip("Default template (non-deletable)").extraSettingsEl.createEl("span",{text:"Default",cls:"setting-item-name",attr:{style:"flex: 1; padding: 4px 8px; color: #888888;"}})}),J.addToggle(e=>{e.setValue(this.plugin.settings.defaultTemplate.gradient).setTooltip("Enable gradient").onChange(async o=>{this.plugin.settings.defaultTemplate.gradient=o,await this.plugin.saveSettings(),this.display()})}),J.addToggle(e=>{e.setValue(this.plugin.settings.defaultTemplate.gradientType).setTooltip("Gradient type: (off) Linear / (on) Color-mix").onChange(async o=>{this.plugin.settings.defaultTemplate.gradientType=o,await this.plugin.saveSettings(),this.display()})});for(let e=0;e<5;e++)J.addColorPicker(o=>{o.setValue(this.plugin.settings.defaultTemplate.colors[e]||"#000000").onChange(async c=>{this.plugin.settings.defaultTemplate.colors[e]=c,await this.plugin.saveSettings(),this.display()})});J.addButton(e=>{e.setIcon("sun").setTooltip("Reset to light default").onClick(async()=>{this.plugin.settings.defaultTemplate.colors=[...this.plugin.settings.defaultLightColors],await this.plugin.saveSettings(),this.display()})}),J.addButton(e=>{e.setIcon("moon").setTooltip("Reset to dark default").onClick(async()=>{this.plugin.settings.defaultTemplate.colors=[...this.plugin.settings.defaultDarkColors],await this.plugin.saveSettings(),this.display()})}),J.addButton(e=>{e.setIcon("rainbow").setTooltip("Reset to rainbow default").onClick(async()=>{this.plugin.settings.defaultTemplate.colors=[...this.plugin.settings.defaultRainbowColors],await this.plugin.saveSettings(),this.display()})}),J.settingEl.createDiv({cls:"spacer-button"}),this.plugin.settings.templates.forEach((e,o)=>{let c=new a.Setting(t).setClass("template-setting-row").setName(`${o+1}`);c.addText(d=>{d.setPlaceholder("Template name").setValue(e.name).onChange(async L=>{clearTimeout(Y),Y=setTimeout(async()=>{let G=L.trim()||"Unnamed",yt=G;if(this.plugin.settings.templates.filter((tt,Bt)=>Bt!==o).some(tt=>tt.name.toLowerCase()===G.toLowerCase())||G.toLowerCase()===this.plugin.settings.defaultTemplate.name.toLowerCase()){let tt=2;for(;this.plugin.settings.templates.some((Bt,vt)=>vt!==o&&Bt.name.toLowerCase()===`${G} ${tt}`.toLowerCase())||`${G} ${tt}`.toLowerCase()===this.plugin.settings.defaultTemplate.name.toLowerCase();)tt++;yt=`${G} ${tt}`}this.plugin.settings.templates[o].name=yt,await this.plugin.saveSettings(),this.display()},1e3)}),d.inputEl.style.flex="1"}),c.addToggle(d=>{d.setValue(e.gradient).setTooltip("Enable gradient").onChange(async L=>{this.plugin.settings.templates[o].gradient=L,await this.plugin.saveSettings(),this.display()})}),c.addToggle(d=>{d.setValue(e.gradientType).setTooltip("Gradient type: (off) Linear / (on) Color-mix").onChange(async L=>{this.plugin.settings.templates[o].gradientType=L,await this.plugin.saveSettings(),this.display()})});for(let d=0;d<5;d++)c.addColorPicker(L=>{L.setValue(e.colors[d]||"#000000").onChange(async G=>{this.plugin.settings.templates[o].colors[d]=G,await this.plugin.saveSettings(),this.display()})});c.addButton(d=>{d.setIcon("sun").setTooltip("Reset to light default").onClick(async()=>{this.plugin.settings.templates[o].colors=[...this.plugin.settings.defaultLightColors],await this.plugin.saveSettings(),this.display()})}),c.addButton(d=>{d.setIcon("moon").setTooltip("Reset to dark default").onClick(async()=>{this.plugin.settings.templates[o].colors=[...this.plugin.settings.defaultDarkColors],await this.plugin.saveSettings(),this.display()})}),c.addButton(d=>{d.setIcon("rainbow").setTooltip("Reset to rainbow default").onClick(async()=>{this.plugin.settings.templates[o].colors=[...this.plugin.settings.defaultRainbowColors],await this.plugin.saveSettings(),this.display()})}),c.addButton(d=>{d.setIcon("arrow-up").setTooltip("Move up").setClass("subtle-button").setDisabled(o===0).onClick(async()=>{if(o>0){let L=this.plugin.settings.templates[o];this.plugin.settings.templates[o]=this.plugin.settings.templates[o-1],this.plugin.settings.templates[o-1]=L,await this.plugin.saveSettings(),this.display()}})}),c.addButton(d=>{d.setIcon("arrow-down").setTooltip("Move down").setClass("subtle-button").setDisabled(o===this.plugin.settings.templates.length-1).onClick(async()=>{if(o<this.plugin.settings.templates.length-1){let L=this.plugin.settings.templates[o];this.plugin.settings.templates[o]=this.plugin.settings.templates[o+1],this.plugin.settings.templates[o+1]=L,await this.plugin.saveSettings(),this.display()}})}),c.addButton(d=>{d.setIcon("trash").setTooltip("Delete template").setClass("subtle-button").onClick(async()=>{this.plugin.settings.templates.splice(o,1),await this.plugin.saveSettings(),this.display()})})})}setEndCaps(t,s,n,l){t&&s?n?(t.style.borderRadius="7px",l==0?s.style.borderRadius="7px":s.style.borderRadius="0 7px 7px 0"):(t.style.borderRadius="0px",s.style.borderRadius="0px"):console.error("Elements are not defined")}hexToRgba(t,s=1){t=t.replace("#",""),t.length===3&&(t=t.split("").map(r=>r+r).join(""));let n=parseInt(t.slice(0,2),16),l=parseInt(t.slice(2,4),16),g=parseInt(t.slice(4,6),16);return`rgba(${n}, ${l}, ${g}, ${s})`}addColorResetButtonsForSettings(t,s,n,l){t.addButton(g=>{this.addColorResetButton(g,"sun","Reset to light default",s,n)}),t.addButton(g=>{this.addColorResetButton(g,"moon","Reset to dark default",s,l)})}addColorResetButton(t,s,n,l,g){t.setIcon(s).setTooltip(n).onClick(async()=>{this.plugin.settings[l]=g,await this.plugin.saveSettings(),this.display()})}createColorPickerSetting(t,s,n,l,g,r){let u=this.splitTextIntoSegments(n),P=new a.Setting(t).setName(s).setDesc(createFragment(T=>this.renderSegments(u,T))).addColorPicker(T=>T.setValue(this.plugin.settings[l]).onChange(async A=>{this.plugin.settings[l]=A,await this.plugin.saveSettings(),this.display()}));this.addColorResetButtonsForSettings(P,l,g,r)}createToggleSetting(t,s,n,l){let g=this.splitTextIntoSegments(n),r=new a.Setting(t).setName(s).setDesc(createFragment(u=>this.renderSegments(g,u))).addToggle(u=>u.setValue(this.plugin.settings[l]).onChange(async P=>{this.plugin.settings[l]=P,await this.plugin.saveSettings(),this.display()})).addButton(u=>u.setIcon("rotate-ccw").setTooltip("Reset to default").onClick(async()=>{this.plugin.settings[l]=i[l],await this.plugin.saveSettings(),this.display()}))}createSliderSetting(t,s,n,l,g,r,u){let P=this.splitTextIntoSegments(n),T=new a.Setting(t).setName(s).setDesc(createFragment(A=>this.renderSegments(P,A))).addSlider(A=>{var y;A.setLimits(g,r,u).setDynamicTooltip().setValue((y=this.plugin.settings[l])!=null?y:i[l]).onChange(async m=>{this.plugin.settings[l]=m,this.plugin.settings.APB_progressBarChange=!0,await this.plugin.saveSettings(),this.display()})}).addButton(A=>A.setIcon("rotate-ccw").setTooltip("Reset to default").onClick(async()=>{this.plugin.settings[l]=i[l],this.plugin.settings.APB_progressBarChange=!0,await this.plugin.saveSettings(),this.display()}))}splitTextIntoSegments(t){let s=/%%([^%]*)%%|<br>/g,n=[],l=0,g;for(;(g=s.exec(t))!==null;){let r=t.slice(l,g.index);if(r&&n.push({text:r,isHighlighted:!1}),g[0]==="<br>")n.push({text:"",isHighlighted:!1,isNewLine:!0});else{let u=g[1];n.push({text:u,isHighlighted:!0})}l=g.index+g[0].length}return l<t.length&&n.push({text:t.slice(l),isHighlighted:!1}),n.length===0&&n.push({text:t,isHighlighted:!1}),n}renderSegments(t,s){t.forEach(n=>{if(n.isNewLine)s.appendChild(document.createElement("br"));else{let l=s.createSpan();l.textContent=n.text,n.isHighlighted&&l.addClass("highlight-text")}})}};function St(H,I){let t;return(...s)=>{clearTimeout(t),t=setTimeout(()=>H.apply(this,s),I)}}function ht(H,I){let t=document.createElement("div");t.addClass("error-container");let s=document.createElement("div");s.addClass("error-text-container"),s.createEl("span",{text:I}),t.appendChild(s),H.appendChild(t)}

/* nosourcemap */