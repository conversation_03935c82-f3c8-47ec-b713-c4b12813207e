@charset "UTF-8";
/**
 * ABC 样式文件
 *
 * 修改/使用说明：
 *
 * vuepress-mdit环境直接用scss，ob这边需要使用css
 * 编辑ABC模块的该文件时，需要手动编译/使用vscode `easy sass` 插件
 *
 * 换行问题：AnyBlock的块宽度很多都是不满100%的，在窄区域中显示多文本时经常需要使用换行
 * - 不换行时
 *   - white-space: pre;          // 有换行符要换
 * - 换行时
 *   - white-space: break-spaces; // 自动换行 ~normal~~
 *   - overflow-wrap: break-word; // 单词中换行
 *   - code {white-space: pre;}   // 一个用于手动控制不换行的方式
 */
/**
 * 颜色相关
 */
:root {
  --ab-tab-root-bg-color: #0d1117;
  --ab-tab-root-bd-color: #34343f;
  --ab-tab-root-tx-color: #9e9e9e;
  --ab-bright-color: orange;
  --pre-background-color: #1b1b1b;
  --ab-table-border-width: 1px;
  --ab-table-border-color: #363636;
  --ab-width-inner: 100%;
  --ab-width-outer: 100%;
}

.theme-dark,
.theme-light {
  --ab-tab-root-bg-color: var(--color-base-00);
  --ab-tab-root-bd-color: var(--color-base-30);
  --ab-tab-root-tx-color: var(--color-base-70);
  --ab-bright-color: var(--blockquote-border-color);
  --pre-background-color: var(--secondary-background);
  --ab-table-border-width: var(--table-border-width);
  --ab-table-border-color: var(--table-border-color);
}

html[data-theme=light] #app,
html[data-theme=dark] #app {
  --ab-tab-root-bg-color: var(--vp-c-bg);
  --ab-tab-root-bd-color: var(--vp-c-border);
  --ab-tab-root-tx-color: var(--vp-c-text);
  --ab-bright-color: var(--vp-c-accent);
  --pre-background-color: var(--code-bg-color);
}

html[data-theme=light] #app {
  --color-red: #e93147;
  --color-orange: #ec7500;
  --color-yellow: #e0ac00;
  --color-green: #08b94e;
  --color-cyan: #00bfbc;
  --color-blue: #086ddd;
  --color-purple: #7852ee;
  --color-pink: #d53984;
}

html[data-theme=dark] #app {
  --color-red: #fb464c;
  --color-orange: #e9973f;
  --color-yellow: #e0de71;
  --color-green: #44cf6e;
  --color-cyan: #53dfdd;
  --color-blue: #027aff;
  --color-purple: #a882ff;
  --color-pink: #fa99cd;
}

/**
 * obsidian各模式下的微调
 * 
 * 替换内容
 * .ab-replace         // 整体 (外框、圆角)
 *   &>.ab-note        // 内容
 *     &>.ab-replaceEl // 内容 (感觉冗余了)
 *   &>.ab-button      // 操作控件 (刷新/编辑/下拉框)
 */
.app-container .markdown-reading-view .ab-replace {
  margin-top: 0;
  margin-bottom: 22px;
}
.app-container .markdown-source-view .ab-replace {
  margin-top: -0.5em;
  margin-bottom: -0.5em;
}
.app-container .markdown-source-view .block-language-anyblock > .ab-replace {
  margin-top: 0;
  margin-bottom: 0;
}
.app-container .markdown-source-view .markdown-preview-view .ab-replace {
  margin-top: 0;
  margin-bottom: 22px;
}
.print .ab-replace > .ab-button {
  display: none;
}
.print .ab-replaceEl > button {
  display: none;
}

/**
 * 替换内容
 * .ab-replace         // 整体 (外框、圆角)
 *   &>.ab-note        // 内容
 *     &>.ab-replaceEl // 内容 (感觉冗余了)
 *   &>.ab-button      // 操作控件 (刷新/编辑/下拉框)
 */
.ab-replace {
  /*background-color: #272e3a;*/
  position: relative;
  border-radius: 4px;
}
.ab-replace > .ab-note {
  position: relative;
  /*padding: 24px 12px 12px 12px;*/
}
.ab-replace > .ab-button {
  box-sizing: border-box;
  position: absolute;
  top: 4px;
}
.ab-replace > .ab-button.ab-button-1 {
  right: 4px;
}
.ab-replace > .ab-button.ab-button-2 {
  right: 40px;
}
.ab-replace > .ab-button.ab-button-3 {
  right: 76px;
}
.ab-replace > .ab-button.ab-button-select > * {
  padding: 0 10px;
  width: 24px;
  height: 24px;
  box-sizing: border-box;
}
.ab-replace > .ab-button.ab-button-select > button {
  padding: 0;
  position: absolute;
  pointer-events: none;
  z-index: 2;
  text-align: center;
  border: none;
  box-shadow: none;
  background: none;
}
.ab-replace > .ab-button.ab-button-select > select {
  z-index: 1;
  opacity: 0;
}
.ab-replace > .ab-button.ab-button-select option {
  padding: 0 10px;
}
.ab-replace img.cm-widgetBuffer {
  height: 2px;
}

.ab-note .markdown-rendered table {
  min-width: 0;
}
.ab-note .markdown-rendered ul li,
.ab-note .markdown-rendered ol li {
  margin-bottom: 0;
}
.ab-note table {
  display: table;
  width: 100%;
}
.ab-note table[modeT=true] tr {
  display: block;
  float: left;
}
.ab-note table[modeT=true] th, .ab-note table[modeT=true] td {
  display: block;
}
.ab-note pre.ab-mermaid-raw {
  border: solid 1px;
  border-radius: 6px;
  margin-top: 0;
}
.ab-note .ab-markmap-svg {
  border: solid 1px;
  border-radius: 6px;
  width: 100%;
}
.ab-note .ab-tab-root .ab-tab-nav {
  box-sizing: border-box;
  border: 2px solid var(--ab-tab-root-bd-color);
  background-color: var(--ab-tab-root-bd-color);
  color: var(--ab-tab-root-tx-color);
  border-bottom: none;
  border-radius: 10px 10px 0 0;
  padding-right: 60px;
}
.ab-note .ab-tab-root .ab-tab-nav .ab-tab-nav-item {
  height: 38px;
  box-shadow: none;
  border: none;
  border-radius: 10px 10px 0 0;
  padding-left: 20px;
  padding-right: 20px;
  font-size: 0.9em;
  font-weight: bold;
  cursor: pointer;
}
.ab-note .ab-tab-root .ab-tab-nav .ab-tab-nav-item[is_activate=false] {
  background-color: var(--ab-tab-root-bd-color);
  color: var(--ab-tab-root-tx-color);
}
.ab-note .ab-tab-root .ab-tab-nav .ab-tab-nav-item[is_activate=true] {
  background-color: var(--ab-tab-root-bg-color);
  color: var(--ab-tab-root-tx-color);
}
.ab-note .ab-tab-root .ab-tab-content {
  border: 2px solid var(--ab-tab-root-bd-color);
  background-color: var(--ab-tab-root-bg-color);
  color: var(--ab-tab-root-tx-color);
  border-top: none;
  padding: 10px 20px;
}
.ab-note td > p:first-child, .ab-note th > p:first-child,
.ab-note td > ul:first-child, .ab-note th > ul:first-child,
.ab-note .ab-list-table-witharrow > p:first-child,
.ab-note .ab-nodes-content > p:first-child,
.ab-note .ab-items-item > div > p:first-child, .ab-note .ab-items-item > div > ul:first-child,
.ab-note td > div > p:first-child, .ab-note th > div > p:first-child,
.ab-note td > div > ul:first-child, .ab-note th > div > ul:first-child,
.ab-note .ab-list-table-witharrow > div > p:first-child,
.ab-note .ab-nodes-content > div > p:first-child,
.ab-note .ab-items-item > div > div > p:first-child, .ab-note .ab-items-item > div > div > ul:first-child {
  margin-top: 2px;
}
.ab-note td > p:last-child, .ab-note th > p:last-child,
.ab-note td > ul:last-child, .ab-note th > ul:last-child,
.ab-note .ab-list-table-witharrow > p:last-child,
.ab-note .ab-nodes-content > p:last-child,
.ab-note .ab-items-item > div > p:last-child, .ab-note .ab-items-item > div > ul:last-child,
.ab-note td > div > p:last-child, .ab-note th > div > p:last-child,
.ab-note td > div > ul:last-child, .ab-note th > div > ul:last-child,
.ab-note .ab-list-table-witharrow > div > p:last-child,
.ab-note .ab-nodes-content > div > p:last-child,
.ab-note .ab-items-item > div > div > p:last-child, .ab-note .ab-items-item > div > div > ul:last-child {
  margin-bottom: 2px;
}
.ab-note table.ab-table {
  border-collapse: collapse;
}
.ab-note table.ab-table td, .ab-note table.ab-table th {
  white-space: normal;
  overflow-wrap: break-word;
  padding: 2px 5px;
  border: solid var(--ab-table-border-width) var(--ab-table-border-color);
}
.ab-note table.ab-table td code, .ab-note table.ab-table th code {
  white-space: pre;
}
.ab-note table.ab-table tr {
  background: none;
}
.ab-note .ab-branch-table.ab-table-likelist td {
  border: none;
  padding-bottom: 10px;
  vertical-align: top;
  box-sizing: border-box;
}
.ab-note .ab-branch-table.ab-table-likelist td[col_index="0"] {
  padding-right: 8px;
  padding-left: 20px;
  position: relative;
}
.ab-note .ab-branch-table.ab-table-likelist td[col_index="0"]::before {
  position: absolute;
  top: 14px;
  transform: translateY(-50%);
  width: 5px;
  height: 5px;
  border: none;
  left: 6px;
  content: "";
  border-radius: 50%;
  background-color: currentColor;
}
.ab-note .ab-branch-table.ab-table-likelist td[col_index="0"]::after {
  content: "";
  position: absolute;
  top: 3px;
  bottom: 5px;
  right: 0;
  border-right: 1px solid currentColor;
  pointer-events: none;
}
.ab-note table.ab-list-table .ab-foldable-tr > td:first-child > div {
  padding-left: 15px;
  position: relative;
}
.ab-note table.ab-list-table .ab-foldable-tr[able_fold=true] > td:first-child > div::before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
}
.ab-note table.ab-list-table .ab-foldable-tr[able_fold=true][is_fold=true] > td:first-child > div:first-child::before {
  top: 5px;
  left: 2px;
  border: 6px solid transparent;
  border-left-color: currentColor;
  cursor: pointer;
}
.ab-note table.ab-list-table .ab-foldable-tr[able_fold=true][is_fold=false] > td:first-child > div:first-child::before {
  top: 9px;
  left: -1px;
  border: 6px solid transparent;
  border-top-color: currentColor;
  cursor: pointer;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="0"] > td:first-child {
  padding-left: 0px;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="1"] > td:first-child {
  padding-left: 24px;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="2"] > td:first-child {
  padding-left: 48px;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="3"] > td:first-child {
  padding-left: 72px;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="4"] > td:first-child {
  padding-left: 96px;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="5"] > td:first-child {
  padding-left: 120px;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="6"] > td:first-child {
  padding-left: 144px;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="7"] > td:first-child {
  padding-left: 168px;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="8"] > td:first-child {
  padding-left: 192px;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="9"] > td:first-child {
  padding-left: 216px;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="10"] > td:first-child {
  padding-left: 240px;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="11"] > td:first-child {
  padding-left: 264px;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="12"] > td:first-child {
  padding-left: 288px;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="13"] > td:first-child {
  padding-left: 312px;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="14"] > td:first-child {
  padding-left: 336px;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="15"] > td:first-child {
  padding-left: 360px;
}
.ab-note table.ab-list-table .ab-foldable-tr[tr_level="16"] > td:first-child {
  padding-left: 384px;
}
.ab-note table.ab-list-table.ab-listtable-likelist {
  margin-left: 24px;
}
.ab-note table.ab-list-table.ab-listtable-likelist td {
  border: none;
  vertical-align: top;
}
.ab-note table.ab-list-table.ab-listtable-likelist .ab-foldable-tr > td:first-child > div.ab-list-table-witharrow {
  padding: 0 !important;
  padding-right: 8px;
  padding-left: 20px;
  position: relative;
  display: inline-block;
}
.ab-note table.ab-list-table.ab-listtable-likelist .ab-foldable-tr > td:first-child > div.ab-list-table-witharrow::before {
  border: solid 1px !important;
  background: none !important;
  width: 6px !important;
  height: 6px !important;
  box-sizing: border-box;
  position: absolute;
  top: 12px !important;
  transform: translateY(-50%) !important;
  left: -14px !important;
  content: "";
  border-radius: 50%;
}
.ab-note table.ab-list-table.ab-listtable-likelist .ab-foldable-tr[tr_level="0"] > td:first-child > div.ab-list-table-witharrow::before {
  border: 0 !important;
  background-color: currentColor !important;
  width: 6px !important;
  height: 6px !important;
}
.ab-note table.ab-list-table.ab-table-folder .ab-foldable-tr td:first-child {
  white-space: pre;
  vertical-align: top;
}
.ab-note table.ab-list-table.ab-table-folder .ab-foldable-tr > td > div {
  display: inline-block;
  vertical-align: top;
}
.ab-note table.ab-list-table.ab-table-folder .ab-foldable-tr > td > div.ab-list-table-witharrow {
  padding-left: 6px;
}
.ab-note table.ab-list-table.ab-table-folder .ab-foldable-tr .ab-list-table-svg {
  padding-top: 4px;
}
.ab-note table.ab-list-table.ab-table-folder .ab-foldable-tr .ab-list-table-svg svg {
  width: 14px;
  height: 14px;
  fill: var(--ab-bright-color);
}
.ab-note table.ab-list-table.ab-table-folder .ab-foldable-tr td:first-child {
  border: none;
}
.ab-note table.ab-list-table.ab-table-folder .ab-foldable-tr td {
  border-left: none;
  border-right: none;
  border-bottom: none;
}
.ab-note .ab-list-table-parent {
  position: relative;
}
.ab-note .ab-list-table-parent .ab-table-fold {
  position: absolute;
  bottom: 0;
  right: 0;
  background: none;
  border: none;
  color: currentColor;
  cursor: pointer;
  padding: 2px 6px;
}
.ab-note table.ab-table-timeline td {
  border-right: none;
  padding-left: 20px;
}
.ab-note table.ab-table-timeline td[col_index="0"] {
  border: none;
  border-left: none;
  border-right: solid 2px;
  padding-left: 5px;
  padding-right: 20px;
  color: var(--ab-bright-color);
  position: relative;
  overflow: visible;
}
.ab-note table.ab-table-timeline td[col_index="0"]::after {
  position: absolute;
  top: 1em;
  transform: translateY(-50%);
  width: 10px;
  height: 10px;
  right: -8px;
  content: "";
  background-color: var(--ab-bright-color);
  border-radius: 50%;
  border: solid 2px;
}
.ab-note table.ab-table-timeline tr:first-child td {
  border-top: none;
}
.ab-note table.ab-table-timeline tr:last-child td {
  border-bottom: none;
}
.ab-note .markdown-rendered.ab-faq-content p:first-child {
  margin-top: 0;
}
.ab-note .markdown-rendered.ab-faq-content p:last-child {
  margin-bottom: 0;
}
.ab-note .ab-faq .ab-faq-line {
  line-height: 30px;
  width: 100%;
  padding: 0 15px 0 15px;
  clear: both;
  overflow-y: auto;
}
.ab-note .ab-faq .ab-faq-line .ab-faq-bubble {
  float: left;
  width: calc(100% - 75px);
}
.ab-note .ab-faq .ab-faq-line.ab-faq-q,
.ab-note .ab-faq .ab-faq-line.ab-faq-Q {
  background-color: #1e53ee;
  border-radius: 12px 12px 0 0;
}
.ab-note .ab-faq .ab-faq-line.ab-faq-a,
.ab-note .ab-faq .ab-faq-line.ab-faq-A {
  background-color: #f5f8fe;
  border-radius: 0 0 12px 12px;
  color: #a994a6;
  margin-bottom: 20px;
}
.ab-note .ab-faq .ab-faq-line::before {
  float: left;
  width: 24px;
}
.ab-note .ab-faq .ab-faq-line.ab-faq-q::before,
.ab-note .ab-faq .ab-faq-line.ab-faq-Q::before {
  content: "Q";
}
.ab-note .ab-faq .ab-faq-line.ab-faq-a::before,
.ab-note .ab-faq .ab-faq-line.ab-faq-A::before {
  content: "A";
}
.ab-note .ab-deco-fold .ab-deco-fold-button {
  color: var(--ab-bright-color);
  border-bottom: 1px solid var(--ab-bright-color);
  height: 30px;
  line-height: 30px;
  padding-left: 6px;
  width: 100%;
}
.ab-note .ab-deco-scroll.ab-deco-scroll-y {
  overflow-y: auto;
}
.ab-note .ab-deco-scroll.ab-deco-scroll-x {
  overflow-x: auto;
  white-space: pre !important;
}
.ab-note .ab-deco-scroll.ab-deco-scroll-x th, .ab-note .ab-deco-scroll.ab-deco-scroll-x td {
  white-space: pre !important;
}
.ab-note .ab-deco-overfold {
  overflow-y: hidden;
  position: relative;
}
.ab-note .ab-deco-overfold .ab-deco-overfold-button {
  position: absolute;
  bottom: 0;
  height: 40px;
  line-height: 40px;
  width: 100%;
  text-align: center;
  box-sizing: content-box;
}
.ab-note .ab-deco-overfold .ab-deco-overfold-content {
  margin-bottom: 40px;
}
.ab-note .ab-deco-overfold[is-fold=true] .ab-deco-overfold-button {
  padding-top: 60px;
  background-image: linear-gradient(-180deg, rgba(24, 26, 27, 0) 0%, rgb(24, 26, 27) 100%);
}
.ab-note .ab-deco-heimu, .ab-note .ab-deco-heimu a {
  background-color: #252525;
  color: #252525;
  text-shadow: none;
}
.ab-note .ab-deco-heimu::-moz-selection, .ab-note .ab-deco-heimu a::-moz-selection {
  background: #9ab0c0;
  color: #fff;
}
.ab-note .ab-deco-heimu::selection, .ab-note .ab-deco-heimu a::selection {
  background: #9ab0c0;
  color: #fff;
}
.ab-note .ab-deco-heimu:hover, .ab-note .ab-deco-heimu:hover a {
  transition: color 0.13s linear;
  color: #fff;
}
.ab-note .ab-deco-title {
  width: 100%;
}
.ab-note .ab-deco-title p {
  font-size: 17px;
  line-height: 17px;
  margin: 0;
  margin-bottom: 10px;
}
.ab-note .ab-deco-title pre {
  margin: 0;
}
.ab-note .ab-deco-title .ab-deco-title-title {
  height: auto;
}
.ab-note .ab-deco-title .ab-deco-title-title[title-type=table] > p {
  font-weight: bold;
  text-align: center;
  margin-top: 10px;
  margin-bottom: 16px;
}
.ab-note .ab-deco-title .ab-deco-title-title[title-type=pre] > p {
  display: inline;
  font-size: 16px;
  font-weight: 400;
  background-color: var(--pre-background-color);
  padding: 8px 16px 11px 16px;
  border-radius: 8px 8px 0 0;
  margin: 0;
}
.ab-note .ab-deco-title .ab-deco-title-title[title-type=quote] > p {
  font-weight: bold;
  margin-bottom: 20px;
}
.ab-note .ab-deco-title .ab-deco-title-title[title-type=ul] > p {
  font-weight: bold;
}
.ab-note .ab-items {
  white-space: normal;
  overflow-wrap: break-word;
}
.ab-note .ab-items code {
  white-space: pre;
}
.ab-note .ab-items.ab-col {
  display: flex;
  flex-wrap: wrap;
  gap: 0rem;
}
.ab-note .ab-items.ab-col .ab-items-item {
  flex: 1 0 calc(33.33% - 1rem);
  box-sizing: border-box;
  padding: 10px;
}
.ab-note .ab-items.ab-card .ab-items-item {
  box-sizing: border-box;
  color: var(--ab-tab-root-tx-color);
  background-color: var(--ab-tab-root-bg-color);
  border: solid 2px var(--ab-tab-root-bd-color);
  border-radius: 6px;
  box-shadow: #34343f;
  margin-bottom: 10px;
  padding: 5px 10px;
}
.ab-note .ab-items.ab-card .ab-items-item > div {
  padding-top: 2px;
  padding-bottom: 2px;
}
.ab-note .ab-items.ab-card .ab-items-item .ab-items-title {
  border-bottom: 1px solid var(--ab-bright-color);
}
.ab-note .ab-items.ab-card.ab-lay-vfall:not(.ab-hfall) {
  /*display: flex; // Flexbox 布局
  flex-wrap: wrap; // 元素换行显示
  gap: 1rem; // 间隙
  .ab-items-item {
    flex: 1 0 calc(25% - 1rem); // 每个项目的宽度大约为容器的 33.33%，减去间隙的一半
  }*/
  -moz-column-count: 4;
       column-count: 4;
  -moz-column-gap: 10px;
       column-gap: 10px;
  /*display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 1rem; // 间隙
  grid-template-rows: masonry; // 兼容有问题，只有火狐支持，还tm的要开选项
  .ab-items-item {
    width: 100%;
    display: block;
  }*/
  /*display: flex;          // Flexbox 布局
  flex-wrap: wrap; // 元素换行显示
  // gap: 1rem;           // 间隙
  flex-direction: column; // 按列填充
  .ab-items-item {
    position: relative;
    width: calc(100% / 4);
    padding: 5px;
    box-sizing: border-box;
    &:nth-child(4n+1){ order: 1; }
    &:nth-child(4n+2){ order: 2; }
    &:nth-child(4n+3){ order: 3; }
    &:nth-child(4n+0){ order: 4; }
  }*/
}
.ab-note .ab-items.ab-card.ab-lay-vfall:not(.ab-hfall) .ab-items-item {
  -moz-column-break-inside: avoid;
       break-inside: avoid-column;
}
.ab-note .ab-items.ab-card.ab-lay-hfall {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
}
.ab-note .ab-items.ab-card.ab-lay-hfall .ab-items-item .ab-items-title {
  color: currentColor;
  border-bottom: none;
}
.ab-note .ab-items.ab-card.ab-lay-hfall::after {
  content: "";
  flex-grow: 99999;
}
.ab-note .ab-items.ab-card.ab-lay-hfall > .ab-items-item {
  flex-grow: 1;
  margin: 5px;
  padding: 0 10px;
  position: relative;
  overflow: hidden;
}
.ab-note .ab-items.ab-card.ab-lay-hfall > .ab-items-item img {
  max-width: 100%;
  min-width: 100%;
  height: 200px;
  margin: 0;
  -o-object-fit: cover;
     object-fit: cover;
  vertical-align: bottom;
}
.ab-note .ab-items.ab-card.ab-lay-hfall > .ab-items-item p {
  max-width: 300px;
  margin: 0;
}
.ab-note .ab-items.ab-card.ab-lay-hfall > .ab-items-item > .ab-items-content {
  height: 100%;
  min-width: 100%;
}
.ab-note .ab-items.ab-card.ab-lay-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 0px !important;
  margin: 0 auto;
}
.ab-note .ab-items.ab-card.ab-lay-grid::after {
  content: "";
  flex-grow: 99999;
}
.ab-note .ab-items.ab-card.ab-lay-grid > .ab-items-item {
  position: relative;
  margin: 5px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center; /* 确保文字居中 */
}
.ab-note .ab-items.ab-card.ab-lay-grid > .ab-items-item img {
  max-width: 200px;
  max-height: 200px;
  width: 100%; /* 确保图片宽度为100%，适应网格布局 */
  height: auto;
  margin: 0;
}
.ab-note .ab-items.ab-card.ab-lay-grid > .ab-items-item > .ab-items-content {
  width: 100%;
  padding: 0;
}
.ab-note .ab-items.ab-card.ab-deco-scroll-x {
  margin: 0 auto;
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto;
}
.ab-note .ab-items.ab-card.ab-deco-scroll-x .ab-items-item {
  flex: 0 0 auto;
  width: 170px;
}
.ab-note .ab-items > .ab-items-item {
  overflow-x: auto;
}
.ab-note .ab-items.ab-col1 {
  -moz-column-count: 1 !important;
       column-count: 1 !important;
  grid-template-columns: repeat(1, 1fr) !important;
}
.ab-note .ab-items.ab-col2 {
  -moz-column-count: 2 !important;
       column-count: 2 !important;
  grid-template-columns: repeat(2, 1fr) !important;
}
.ab-note .ab-items.ab-col3 {
  -moz-column-count: 3 !important;
       column-count: 3 !important;
  grid-template-columns: repeat(3, 1fr) !important;
}
.ab-note .ab-items.ab-col4 {
  -moz-column-count: 4 !important;
       column-count: 4 !important;
  grid-template-columns: repeat(4, 1fr) !important;
}
.ab-note .ab-items.ab-col5 {
  -moz-column-count: 5 !important;
       column-count: 5 !important;
  grid-template-columns: repeat(5, 1fr) !important;
}
.ab-note .ab-items.ab-col6 {
  -moz-column-count: 6 !important;
       column-count: 6 !important;
  grid-template-columns: repeat(6, 1fr) !important;
}
.ab-note .ab-items.ab-col7 {
  -moz-column-count: 7 !important;
       column-count: 7 !important;
  grid-template-columns: repeat(7, 1fr) !important;
}
.ab-note .ab-items.ab-col8 {
  -moz-column-count: 8 !important;
       column-count: 8 !important;
  grid-template-columns: repeat(8, 1fr) !important;
}
.ab-note .ab-nodes {
  white-space: normal;
  overflow-wrap: break-word;
}
.ab-note .ab-nodes code {
  white-space: pre;
}
.ab-note .ab-nodes .ab-nodes-content {
  display: inline-block;
  background-color: rgba(100, 100, 100, 0.25);
  padding: 2px 10px;
  margin-left: 5px;
}
.ab-note .ab-nodes .ab-nodes-children {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding-left: 0.8em;
  position: relative;
}
.ab-note .ab-nodes .ab-nodes-children .ab-nodes-bracket {
  content: "";
  box-sizing: border-box;
  position: absolute;
  width: 8px;
  height: 8px;
  top: calc(50% - 4px);
  left: -6px;
  -webkit-clip-path: polygon(100% 0, 100% 100%, 13.4% 50%);
          clip-path: polygon(100% 0, 100% 100%, 13.4% 50%);
  background-color: currentColor;
}
.ab-note .ab-nodes .ab-nodes-children .ab-nodes-bracket2 {
  content: "";
  box-sizing: border-box;
  position: absolute;
  width: 10px;
  height: calc(100% - 8px);
  top: 4px;
  left: 0;
  border-radius: 10px 0 0 10px;
  border-left: 2px solid currentColor;
  border-top: 2px solid currentColor;
  border-bottom: 2px solid currentColor;
}
.ab-note .ab-nodes .ab-nodes-children .ab-nodes-node {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1.2em;
}
.ab-note .ab-nodes > .ab-nodes-node > .ab-nodes-bracket {
  display: none;
}
.ab-note .ab-nodes > .ab-nodes-node > .ab-nodes-bracket2 {
  display: none;
}
.ab-note .ab-nodes .ab-nodes-node[has_children=false] > .ab-nodes-children {
  display: none;
}
.ab-note .ab-nodes.min .ab-nodes-content {
  padding: 0;
  background: none;
  border-bottom: solid 1px currentColor;
}
.ab-note .ab-nodes.min .ab-nodes-children {
  gap: 2px;
}
.ab-note .ab-nodes.min .ab-nodes-children .ab-nodes-bracket2 {
  border-width: 1px;
}
.ab-note .ab-nodes.min .ab-nodes-children .ab-nodes-bracket {
  display: none;
  width: 6px;
  height: 6px;
  top: calc(50% - 4px);
  left: -6px;
}
.ab-note .ab-nodes.min .ab-nodes-content {
  padding-right: 1.2em;
}
.ab-note .ab-nodes.min .ab-nodes-children .ab-nodes-node {
  gap: 0;
}
.ab-note .ab-nodes-node[has_children=false] > .ab-nodes-content {
  padding-right: 4px;
}
.ab-note .ab-nodes.min > .ab-nodes-children > .ab-nodes-node:nth-child(8n+1) {
  --node-color: var(--color-red);
}
.ab-note .ab-nodes.min > .ab-nodes-children > .ab-nodes-node:nth-child(8n+2) {
  --node-color: var(--color-orange);
}
.ab-note .ab-nodes.min > .ab-nodes-children > .ab-nodes-node:nth-child(8n+3) {
  --node-color: var(--color-yellow);
}
.ab-note .ab-nodes.min > .ab-nodes-children > .ab-nodes-node:nth-child(8n+4) {
  --node-color: var(--color-green);
}
.ab-note .ab-nodes.min > .ab-nodes-children > .ab-nodes-node:nth-child(8n+5) {
  --node-color: var(--color-cyan);
}
.ab-note .ab-nodes.min > .ab-nodes-children > .ab-nodes-node:nth-child(8n+6) {
  --node-color: var(--color-blue);
}
.ab-note .ab-nodes.min > .ab-nodes-children > .ab-nodes-node:nth-child(8n+7) {
  --node-color: var(--color-purple);
}
.ab-note .ab-nodes.min > .ab-nodes-children > .ab-nodes-node:nth-child(8n+0) {
  --node-color: var(--color-pink);
}
.ab-note .ab-nodes.min > .ab-nodes-children > .ab-nodes-node .ab-nodes-content, .ab-note .ab-nodes.min > .ab-nodes-children > .ab-nodes-node .ab-nodes-bracket, .ab-note .ab-nodes.min > .ab-nodes-children > .ab-nodes-node .ab-nodes-bracket2 {
  border-color: var(--node-color);
}
.ab-note .ab-nodes.min > .ab-nodes-children > .ab-nodes-node .ab-nodes-bracket {
  background-color: var(--node-color);
}
.ab-note .ab-nodes.scroll {
  overflow-x: auto;
}
.ab-note .ab-nodes.scroll .ab-nodes-content {
  white-space: nowrap;
  max-width: 300px;
  overflow-x: auto;
}
.ab-note table.scroll {
  overflow-x: auto;
}
.ab-note table.scroll td {
  white-space: nowrap;
  max-width: 300px;
  overflow-x: auto;
}

div.md-table-fig1 {
  overflow-x: scroll;
  transform: scaleY(-1);
}
div.md-table-fig1 table.ab-setting.md-table-fig2 {
  overflow-x: scroll;
  text-overflow: clip !important;
  transform: scaleY(-1);
  white-space: pre-wrap;
}

.ab-custom-text-red {
  color: red !important;
}

.ab-custom-text-orange {
  color: orange !important;
}

.ab-custom-text-yellow {
  color: yellow !important;
}

.ab-custom-text-green {
  color: green !important;
}

.ab-custom-text-cyan {
  color: cyan !important;
}

.ab-custom-text-blue {
  color: blue !important;
}

.ab-custom-text-purple {
  color: purple !important;
}

.ab-custom-text-white {
  color: white !important;
}

.ab-custom-text-black {
  color: black !important;
}

.ab-custom-bg-red {
  background-color: red !important;
}

.ab-custom-bg-orange {
  background-color: orange !important;
}

.ab-custom-bg-yellow {
  background-color: yellow !important;
}

.ab-custom-bg-green {
  background-color: green !important;
}

.ab-custom-bg-cyan {
  background-color: cyan !important;
}

.ab-custom-bg-blue {
  background-color: blue !important;
}

.ab-custom-bg-purple {
  background-color: purple !important;
}

.ab-custom-bg-white {
  background-color: white !important;
}

.ab-custom-bg-black {
  background-color: black !important;
}

.ab-custom-dire-top * {
  vertical-align: top !important;
}

.ab-custom-dire-down * {
  vertical-align: bottom !important;
}

.ab-custom-dire-left * {
  text-align: left !important;
}

.ab-custom-dire-right * {
  text-align: right !important;
}

.ab-custom-dire-center * {
  vertical-align: middle !important;
  text-align: center !important;
}

.ab-custom-dire-hcenter * {
  text-align: center !important;
}

.ab-custom-dire-vcenter * {
  vertical-align: middle !important;
}

.ab-custom-dire-justify * {
  text-align: justify !important;
}

.ab-custom-font-large {
  font-size: large !important;
}

.ab-custom-font-largex {
  font-size: x-large !important;
}

.ab-custom-font-largexx {
  font-size: xx-large !important;
}

.ab-custom-font-small {
  font-size: small !important;
}

.ab-custom-font-smallx {
  font-size: x-small !important;
}

.ab-custom-font-smallxx {
  font-size: xx-small !important;
}

.ab-custom-font-bold {
  font-weight: bold !important;
}

table.ab-table-fc th[col_index="0"], table.ab-table-fc td[col_index="0"] {
  white-space: pre;
  font-weight: 500;
  vertical-align: top;
  width: 0px;
}

.ab-table-fc > table th:first-child, .ab-table-fc > table td:first-child {
  white-space: pre;
  font-weight: 500;
  vertical-align: top;
  width: 0px;
}

.ab-custom-td-nowrap td, .ab-custom-td1-nowrap tr > td:first-child {
  white-space: pre !important;
}

.ab-custom-td-wrap td, .ab-custom-td1-wrap tr > td:first-child {
  white-space: normal !important;
  overflow-wrap: break-word;
}

.ab-custom-td-0 td, .ab-custom-td1-0 tr > td:first-child {
  width: 0px !important;
}

.ab-custom-td-100 td, .ab-custom-td1-100 tr > td:first-child {
  width: 100px !important;
}

.ab-custom-td-150 td, .ab-custom-td1-150 tr > td:first-child {
  width: 150px !important;
}

.ab-custom-td-200 td, .ab-custom-td1-200 tr > td:first-child {
  width: 200px !important;
}

.ab-custom-td-250 td, .ab-custom-td1-250 tr > td:first-child {
  width: 250px !important;
}

.ab-custom-td-300 td, .ab-custom-td1-300 tr > td:first-child {
  width: 300px !important;
}

.ab-custom-td-half td, .ab-custom-td1-half tr > td:first-child {
  width: 50% !important;
}

.ab-super-width-p {
  width: var(--ab-width-outer);
  margin-left: calc((100% - var(--ab-width-outer)) / 2);
}

/**************** 兼容性补充 ******************/
:is(.markdown-preview-view, .markdown-rendered).is-readable-line-width:not(.matrix) .ab-note :is(.markdown-rendered) {
  width: auto !important;
}