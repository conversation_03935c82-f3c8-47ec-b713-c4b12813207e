{"mcpServers": {"github.com/modelcontextprotocol/servers/tree/main/src/memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "disabled": false, "autoApprove": []}, "github.com/modelcontextprotocol/servers/tree/main/src/sequentialthinking": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "transportType": "stdio"}, "github.com/modelcontextprotocol/servers/tree/main/src/filesystem": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "D:/Uni/United"], "transportType": "stdio"}, "github.com/zcaceres/fetch-mcp": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "node", "args": ["C:/Users/<USER>/Documents/Cline/MCP/fetch-mcp/dist/index.js"], "transportType": "stdio"}, "github.com/executeautomation/mcp-playwright": {"autoApprove": [], "disabled": false, "timeout": 60, "command": "npx", "args": ["-y", "@executeautomation/playwright-mcp-server"], "transportType": "stdio"}, "github.com/modelcontextprotocol/servers/tree/main/src/github": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-github"], "env": {"GITHUB_PERSONAL_ACCESS_TOKEN": "****************************************"}, "disabled": false, "autoApprove": []}, "github.com/smithery-ai/mcp-obsidian": {"command": "npx", "args": ["-y", "mcp-obsidian", "D:/Uni/United"], "disabled": false, "autoApprove": []}, "Memory": {"disabled": false, "timeout": 60, "alwaysAllow": [], "type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}}}